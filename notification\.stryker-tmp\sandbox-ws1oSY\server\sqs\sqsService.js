// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const {
  SQSClient,
  DeleteMessageCommand
} = require('@aws-sdk/client-sqs');
const Notification = require('../util/notification');
const CONSTANTS = require('../util/constants');
const config = stryMutAct_9fa48("1325") ? {} : (stryCov_9fa48("1325"), {
  region: process.env.AWS_REGION
});
const PushNotificationService = require('../pushNotification/pushNotificationService');
const ChildModel = require('../models/child.model');
const sqsClient = new SQSClient(config);
class SqsService {
  static async pullEventFromQueue(event) {
    if (stryMutAct_9fa48("1326")) {
      {}
    } else {
      stryCov_9fa48("1326");
      try {
        if (stryMutAct_9fa48("1327")) {
          {}
        } else {
          stryCov_9fa48("1327");
          for (const record of event.Records) {
            if (stryMutAct_9fa48("1328")) {
              {}
            } else {
              stryCov_9fa48("1328");
              if (stryMutAct_9fa48("1331") ? record.eventSource !== 'aws:sqs' : stryMutAct_9fa48("1330") ? false : stryMutAct_9fa48("1329") ? true : (stryCov_9fa48("1329", "1330", "1331"), record.eventSource === (stryMutAct_9fa48("1332") ? "" : (stryCov_9fa48("1332"), 'aws:sqs')))) {
                if (stryMutAct_9fa48("1333")) {
                  {}
                } else {
                  stryCov_9fa48("1333");
                  const body = JSON.parse(record.body);
                  CONSOLE_LOGGER.info(stryMutAct_9fa48("1334") ? "" : (stryCov_9fa48("1334"), 'Received message from SQS: body '), body);
                  await this.processNotification(body, record.receiptHandle);
                  await this.deleteMessage(record.receiptHandle);
                }
              }
            }
          }
        }
      } catch (error) {
        if (stryMutAct_9fa48("1335")) {
          {}
        } else {
          stryCov_9fa48("1335");
          CONSOLE_LOGGER.error(error.message, stryMutAct_9fa48("1336") ? "" : (stryCov_9fa48("1336"), 'Error sending notification'));
        }
      }
    }
  }
  static async processNotification(message) {
    if (stryMutAct_9fa48("1337")) {
      {}
    } else {
      stryCov_9fa48("1337");
      const {
        trigger,
        childId,
        title,
        message: msg,
        topicKey,
        eventSignUp,
        eventDetails,
        score,
        postDetails,
        isPost
      } = message;
      CONSOLE_LOGGER.info(stryMutAct_9fa48("1338") ? "" : (stryCov_9fa48("1338"), ' process SQS Notification :: '), trigger);
      if (stryMutAct_9fa48("1341") ? trigger !== CONSTANTS.TRIGGER.REQUESTED_BY : stryMutAct_9fa48("1340") ? false : stryMutAct_9fa48("1339") ? true : (stryCov_9fa48("1339", "1340", "1341"), trigger === CONSTANTS.TRIGGER.REQUESTED_BY)) {
        if (stryMutAct_9fa48("1342")) {
          {}
        } else {
          stryCov_9fa48("1342");
          const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("1343") ? {} : (stryCov_9fa48("1343"), {
            title,
            topic: topicKey,
            body: msg,
            data: stryMutAct_9fa48("1344") ? {} : (stryCov_9fa48("1344"), {
              childId
            }),
            clickAction: stryMutAct_9fa48("1345") ? "" : (stryCov_9fa48("1345"), 'vaalee://routeName=requestedBy')
          }));
          await Notification.sendNotification(notificationObject);
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1346") ? "" : (stryCov_9fa48("1346"), 'Sending notification for requestedBy with object: '), notificationObject);
          await PushNotificationService.pushInNotificationTable(stryMutAct_9fa48("1347") ? {} : (stryCov_9fa48("1347"), {
            userId: eventSignUp.parentId,
            associatedChildId: childId,
            notificationAction: stryMutAct_9fa48("1348") ? "" : (stryCov_9fa48("1348"), 'vaalee://routeName=requestedBy')
          }), stryMutAct_9fa48("1349") ? {} : (stryCov_9fa48("1349"), {
            score,
            childId,
            eventId: eventSignUp.eventId
          }), msg);
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1350") ? "" : (stryCov_9fa48("1350"), 'Notification sent for requestedBy with object: '), notificationObject);
        }
      }
      if (stryMutAct_9fa48("1353") ? trigger !== CONSTANTS.TRIGGER.EVENT_SIGNUP : stryMutAct_9fa48("1352") ? false : stryMutAct_9fa48("1351") ? true : (stryCov_9fa48("1351", "1352", "1353"), trigger === CONSTANTS.TRIGGER.EVENT_SIGNUP)) {
        if (stryMutAct_9fa48("1354")) {
          {}
        } else {
          stryCov_9fa48("1354");
          const child = await ChildModel.get(stryMutAct_9fa48("1355") ? {} : (stryCov_9fa48("1355"), {
            id: childId
          }));
          const msg = stryMutAct_9fa48("1356") ? `` : (stryCov_9fa48("1356"), `Your ${eventDetails.title} event request for ${child.firstName} ${child.lastName} is now complete`);
          const clickAction = stryMutAct_9fa48("1357") ? "" : (stryCov_9fa48("1357"), 'vaalee://routeName=eventDetails');
          const notificationData = stryMutAct_9fa48("1358") ? {} : (stryCov_9fa48("1358"), {
            details: JSON.stringify(stryMutAct_9fa48("1359") ? {} : (stryCov_9fa48("1359"), {
              associatedChild: stryMutAct_9fa48("1360") ? {} : (stryCov_9fa48("1360"), {
                id: childId
              }),
              id: eventSignUp.eventId,
              startDateTime: eventDetails.startDateTime,
              endDateTime: eventDetails.endDateTime,
              score
            })),
            child: JSON.stringify(stryMutAct_9fa48("1361") ? {} : (stryCov_9fa48("1361"), {
              id: child.id,
              firstName: child.firstName,
              lastName: child.lastName,
              photoURL: child.photoURL,
              associatedColor: child.associatedColor
            })),
            route: stryMutAct_9fa48("1362") ? "" : (stryCov_9fa48("1362"), 'eventDetails')
          });
          const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("1363") ? {} : (stryCov_9fa48("1363"), {
            clickAction,
            title: stryMutAct_9fa48("1364") ? "" : (stryCov_9fa48("1364"), 'Event Confirmation!'),
            topic: topicKey,
            body: msg,
            data: notificationData
          }));
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1365") ? "" : (stryCov_9fa48("1365"), 'Sending notification for event signup with object: '), notificationObject);
          await Notification.sendNotification(notificationObject);
          await PushNotificationService.pushInNotificationTable(stryMutAct_9fa48("1366") ? {} : (stryCov_9fa48("1366"), {
            associatedChildId: childId,
            notificationAction: clickAction,
            title: stryMutAct_9fa48("1367") ? "" : (stryCov_9fa48("1367"), 'Event Confirmation!')
          }), notificationData, msg);
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1368") ? "" : (stryCov_9fa48("1368"), 'Notification sent for event signup with object: '), notificationObject);
        }
      }
      if (stryMutAct_9fa48("1371") ? trigger !== CONSTANTS.TRIGGER.FUNDRAISER_SIGNUP : stryMutAct_9fa48("1370") ? false : stryMutAct_9fa48("1369") ? true : (stryCov_9fa48("1369", "1370", "1371"), trigger === CONSTANTS.TRIGGER.FUNDRAISER_SIGNUP)) {
        if (stryMutAct_9fa48("1372")) {
          {}
        } else {
          stryCov_9fa48("1372");
          const childDetails = await ChildModel.get(stryMutAct_9fa48("1373") ? {} : (stryCov_9fa48("1373"), {
            id: childId
          }));
          // eslint-disable-next-line max-len
          const message = stryMutAct_9fa48("1374") ? `` : (stryCov_9fa48("1374"), `Your ${eventDetails.title} fundraiser request for ${childDetails.firstName} ${childDetails.lastName} is now complete.`);
          const clickAction = stryMutAct_9fa48("1375") ? "" : (stryCov_9fa48("1375"), 'vaalee://routeName=fundraiserDetails');
          const notificationDataObj = stryMutAct_9fa48("1376") ? {} : (stryCov_9fa48("1376"), {
            details: JSON.stringify(stryMutAct_9fa48("1377") ? {} : (stryCov_9fa48("1377"), {
              associatedChild: stryMutAct_9fa48("1378") ? {} : (stryCov_9fa48("1378"), {
                id: childId
              }),
              id: eventSignUp.eventId,
              isFundraiser: stryMutAct_9fa48("1379") ? false : (stryCov_9fa48("1379"), true),
              fundraiserSignupId: eventSignUp.id,
              startDateTime: eventDetails.startDateTime,
              endDateTime: eventDetails.endDateTime,
              score
            })),
            child: JSON.stringify(stryMutAct_9fa48("1380") ? {} : (stryCov_9fa48("1380"), {
              id: childDetails.id,
              firstName: childDetails.firstName,
              lastName: childDetails.lastName,
              photoURL: childDetails.photoURL,
              associatedColor: childDetails.associatedColor
            })),
            route: stryMutAct_9fa48("1381") ? "" : (stryCov_9fa48("1381"), 'fundraiserDetails')
          });
          const createdNotificationObject = Notification.createNotificationObject(stryMutAct_9fa48("1382") ? {} : (stryCov_9fa48("1382"), {
            clickAction,
            title: stryMutAct_9fa48("1383") ? "" : (stryCov_9fa48("1383"), 'Fundraiser Confirmation!'),
            topic: topicKey,
            body: message,
            data: notificationDataObj
          }));
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1384") ? "" : (stryCov_9fa48("1384"), 'Sending notification for fundraiser signup with object: '), createdNotificationObject);
          await Notification.sendNotification(createdNotificationObject);
          await PushNotificationService.pushInNotificationTable(stryMutAct_9fa48("1385") ? {} : (stryCov_9fa48("1385"), {
            associatedChildId: childId,
            notificationAction: clickAction,
            title: stryMutAct_9fa48("1386") ? "" : (stryCov_9fa48("1386"), 'Fundraiser Confirmation!')
          }), notificationDataObj, message);
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1387") ? "" : (stryCov_9fa48("1387"), 'Notification sent for fundraiser signup with object: '), createdNotificationObject);
        }
      }
      if (stryMutAct_9fa48("1390") ? trigger !== CONSTANTS.TRIGGER.BOOSTER_DONATION : stryMutAct_9fa48("1389") ? false : stryMutAct_9fa48("1388") ? true : (stryCov_9fa48("1388", "1389", "1390"), trigger === CONSTANTS.TRIGGER.BOOSTER_DONATION)) {
        if (stryMutAct_9fa48("1391")) {
          {}
        } else {
          stryCov_9fa48("1391");
          const child = await ChildModel.get(stryMutAct_9fa48("1392") ? {} : (stryCov_9fa48("1392"), {
            id: childId
          }));
          const clickAction = stryMutAct_9fa48("1393") ? "" : (stryCov_9fa48("1393"), 'vaalee://routeName=fundraiserDetails');
          const notificationData = stryMutAct_9fa48("1394") ? {} : (stryCov_9fa48("1394"), {
            details: JSON.stringify(stryMutAct_9fa48("1395") ? {} : (stryCov_9fa48("1395"), {
              associatedChild: stryMutAct_9fa48("1396") ? {} : (stryCov_9fa48("1396"), {
                id: childId
              }),
              id: stryMutAct_9fa48("1397") ? eventSignUp.fundraiserId : (stryCov_9fa48("1397"), eventSignUp?.fundraiserId),
              isFundraiser: stryMutAct_9fa48("1398") ? false : (stryCov_9fa48("1398"), true),
              fundraiserSignupId: stryMutAct_9fa48("1399") ? eventSignUp.fundraiserSignupId : (stryCov_9fa48("1399"), eventSignUp?.fundraiserSignupId),
              startDateTime: stryMutAct_9fa48("1400") ? eventDetails.startDate : (stryCov_9fa48("1400"), eventDetails?.startDate),
              endDateTime: stryMutAct_9fa48("1401") ? eventDetails.endDate : (stryCov_9fa48("1401"), eventDetails?.endDate),
              score
            })),
            child: JSON.stringify(stryMutAct_9fa48("1402") ? {} : (stryCov_9fa48("1402"), {
              id: child.id,
              firstName: child.firstName,
              lastName: child.lastName,
              photoURL: child.photoURL,
              associatedColor: child.associatedColor
            })),
            route: stryMutAct_9fa48("1403") ? "" : (stryCov_9fa48("1403"), 'fundraiserDetails')
          });
          const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("1404") ? {} : (stryCov_9fa48("1404"), {
            clickAction,
            title,
            topic: topicKey,
            body: msg,
            data: notificationData
          }));
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1405") ? "" : (stryCov_9fa48("1405"), 'Sending notification for booster donation with object: '), notificationObject);
          await Notification.sendNotification(notificationObject);
          await PushNotificationService.pushInNotificationTable(stryMutAct_9fa48("1406") ? {} : (stryCov_9fa48("1406"), {
            associatedChildId: childId,
            notificationAction: clickAction,
            title
          }), notificationData, msg);
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1407") ? "" : (stryCov_9fa48("1407"), 'Notification sent for booster donation with object: '), notificationObject);
        }
      }
      if (stryMutAct_9fa48("1410") ? trigger !== CONSTANTS.TRIGGER.POST_CREATED : stryMutAct_9fa48("1409") ? false : stryMutAct_9fa48("1408") ? true : (stryCov_9fa48("1408", "1409", "1410"), trigger === CONSTANTS.TRIGGER.POST_CREATED)) {
        if (stryMutAct_9fa48("1411")) {
          {}
        } else {
          stryCov_9fa48("1411");
          const child = await ChildModel.get(stryMutAct_9fa48("1412") ? {} : (stryCov_9fa48("1412"), {
            id: childId
          }));
          const clickAction = stryMutAct_9fa48("1413") ? "" : (stryCov_9fa48("1413"), 'vaalee://routeName=postDetails');
          const notificationData = stryMutAct_9fa48("1414") ? {} : (stryCov_9fa48("1414"), {
            details: JSON.stringify(stryMutAct_9fa48("1415") ? {} : (stryCov_9fa48("1415"), {
              associatedChild: stryMutAct_9fa48("1416") ? {} : (stryCov_9fa48("1416"), {
                id: childId
              }),
              id: postDetails.id,
              publishedDate: postDetails.publishedDate,
              isPost,
              score
            })),
            child: JSON.stringify(stryMutAct_9fa48("1417") ? {} : (stryCov_9fa48("1417"), {
              id: child.id,
              firstName: child.firstName,
              lastName: child.lastName,
              photoURL: child.photoURL,
              associatedColor: child.associatedColor
            })),
            route: stryMutAct_9fa48("1418") ? "" : (stryCov_9fa48("1418"), 'postDetails')
          });
          const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("1419") ? {} : (stryCov_9fa48("1419"), {
            clickAction,
            title,
            topic: topicKey,
            body: msg,
            data: notificationData
          }));
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1420") ? "" : (stryCov_9fa48("1420"), 'Sending notification for post published with object: '), stryMutAct_9fa48("1421") ? {} : (stryCov_9fa48("1421"), {
            notificationObject
          }));
          // await Notification.sendNotification(notificationObject);
          await PushNotificationService.pushInNotificationTable(stryMutAct_9fa48("1422") ? {} : (stryCov_9fa48("1422"), {
            associatedChildId: childId,
            notificationAction: clickAction,
            title
          }), notificationData, msg, trigger);
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1423") ? "" : (stryCov_9fa48("1423"), 'Notification sent for post published with object: '), notificationObject);
        }
      }
      if (stryMutAct_9fa48("1426") ? trigger !== CONSTANTS.TRIGGER.EVENT_UPDATED : stryMutAct_9fa48("1425") ? false : stryMutAct_9fa48("1424") ? true : (stryCov_9fa48("1424", "1425", "1426"), trigger === CONSTANTS.TRIGGER.EVENT_UPDATED)) {
        if (stryMutAct_9fa48("1427")) {
          {}
        } else {
          stryCov_9fa48("1427");
          const child = await ChildModel.get(stryMutAct_9fa48("1428") ? {} : (stryCov_9fa48("1428"), {
            id: childId
          }));
          const msg = stryMutAct_9fa48("1429") ? `` : (stryCov_9fa48("1429"), `${title} is updated for your child ${child.firstName} ${child.lastName}.`);
          const clickAction = stryMutAct_9fa48("1430") ? "" : (stryCov_9fa48("1430"), 'vaalee://routeName=eventDetails');
          const notificationData = stryMutAct_9fa48("1431") ? {} : (stryCov_9fa48("1431"), {
            details: JSON.stringify(stryMutAct_9fa48("1432") ? {} : (stryCov_9fa48("1432"), {
              associatedChild: stryMutAct_9fa48("1433") ? {} : (stryCov_9fa48("1433"), {
                id: childId
              }),
              id: eventSignUp.eventId,
              startDateTime: eventDetails.startDateTime,
              endDateTime: eventDetails.endDateTime,
              score
            })),
            child: JSON.stringify(stryMutAct_9fa48("1434") ? {} : (stryCov_9fa48("1434"), {
              id: childId,
              firstName: child.firstName,
              lastName: child.lastName,
              photoURL: child.photoURL,
              associatedColor: child.associatedColor
            })),
            route: stryMutAct_9fa48("1435") ? "" : (stryCov_9fa48("1435"), 'eventDetails')
          });
          const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("1436") ? {} : (stryCov_9fa48("1436"), {
            clickAction,
            title: stryMutAct_9fa48("1437") ? "" : (stryCov_9fa48("1437"), 'Event Updated!'),
            topic: topicKey,
            body: msg,
            data: notificationData
          }));
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1438") ? "" : (stryCov_9fa48("1438"), 'Sending notification for event updated with object: '), notificationObject);
          await Notification.sendNotification(notificationObject);
          await PushNotificationService.pushInNotificationTable(stryMutAct_9fa48("1439") ? {} : (stryCov_9fa48("1439"), {
            associatedChildId: childId,
            notificationAction: clickAction,
            title: stryMutAct_9fa48("1440") ? "" : (stryCov_9fa48("1440"), 'Event Updated!')
          }), notificationData, msg);
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1441") ? "" : (stryCov_9fa48("1441"), 'Notification sent for event updated with object: '), notificationObject);
        }
      }
      if (stryMutAct_9fa48("1444") ? trigger !== CONSTANTS.TRIGGER.CONVERSATION : stryMutAct_9fa48("1443") ? false : stryMutAct_9fa48("1442") ? true : (stryCov_9fa48("1442", "1443", "1444"), trigger === CONSTANTS.TRIGGER.CONVERSATION)) {
        if (stryMutAct_9fa48("1445")) {
          {}
        } else {
          stryCov_9fa48("1445");
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1446") ? "" : (stryCov_9fa48("1446"), 'Start sending notification for conversation'));
          const {
            messageText,
            senderName,
            groupName,
            topicKey,
            groupId
          } = message;
          const clickAction = stryMutAct_9fa48("1447") ? "" : (stryCov_9fa48("1447"), 'vaalee://routeName=conversation');
          const notificationData = stryMutAct_9fa48("1448") ? {} : (stryCov_9fa48("1448"), {
            details: JSON.stringify(stryMutAct_9fa48("1449") ? {} : (stryCov_9fa48("1449"), {
              actionText: stryMutAct_9fa48("1450") ? "" : (stryCov_9fa48("1450"), 'Message Received!'),
              groupId
            })),
            route: stryMutAct_9fa48("1451") ? "" : (stryCov_9fa48("1451"), 'conversation')
          });
          const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("1452") ? {} : (stryCov_9fa48("1452"), {
            clickAction,
            topic: topicKey,
            body: messageText,
            title: senderName + (stryMutAct_9fa48("1453") ? "" : (stryCov_9fa48("1453"), ' to ')) + groupName,
            data: notificationData
          }));
          const resp = await Notification.sendNotification(notificationObject);
          if (stryMutAct_9fa48("1455") ? false : stryMutAct_9fa48("1454") ? true : (stryCov_9fa48("1454", "1455"), resp.isError)) {
            if (stryMutAct_9fa48("1456")) {
              {}
            } else {
              stryCov_9fa48("1456");
              throw new Error(stryMutAct_9fa48("1457") ? "" : (stryCov_9fa48("1457"), 'Error sending notification'));
            }
          } else {
            if (stryMutAct_9fa48("1458")) {
              {}
            } else {
              stryCov_9fa48("1458");
              CONSOLE_LOGGER.info(stryMutAct_9fa48("1459") ? "" : (stryCov_9fa48("1459"), 'Notification sent for conversation'));
            }
          }
        }
      }
      if (stryMutAct_9fa48("1462") ? trigger !== CONSTANTS.TRIGGER.PERSONAL_CONVERSATION : stryMutAct_9fa48("1461") ? false : stryMutAct_9fa48("1460") ? true : (stryCov_9fa48("1460", "1461", "1462"), trigger === CONSTANTS.TRIGGER.PERSONAL_CONVERSATION)) {
        if (stryMutAct_9fa48("1463")) {
          {}
        } else {
          stryCov_9fa48("1463");
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1464") ? "" : (stryCov_9fa48("1464"), 'Start sending notification for personal conversation'));
          const {
            messageText,
            senderName,
            topicKey,
            conversationId,
            receiverId,
            senderId
          } = message;
          const clickAction = stryMutAct_9fa48("1465") ? "" : (stryCov_9fa48("1465"), 'vaalee://routeName=personalConversation');
          const notificationData = stryMutAct_9fa48("1466") ? {} : (stryCov_9fa48("1466"), {
            details: JSON.stringify(stryMutAct_9fa48("1467") ? {} : (stryCov_9fa48("1467"), {
              actionText: stryMutAct_9fa48("1468") ? "" : (stryCov_9fa48("1468"), 'Personal Message Received!'),
              conversationId,
              receiverId,
              senderId
            })),
            route: stryMutAct_9fa48("1469") ? "" : (stryCov_9fa48("1469"), 'personalConversation')
          });
          const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("1470") ? {} : (stryCov_9fa48("1470"), {
            clickAction,
            topic: topicKey,
            body: messageText,
            title: senderName,
            data: notificationData
          }));
          const resp = await Notification.sendNotification(notificationObject);
          if (stryMutAct_9fa48("1472") ? false : stryMutAct_9fa48("1471") ? true : (stryCov_9fa48("1471", "1472"), resp.isError)) {
            if (stryMutAct_9fa48("1473")) {
              {}
            } else {
              stryCov_9fa48("1473");
              throw new Error(stryMutAct_9fa48("1474") ? "" : (stryCov_9fa48("1474"), 'Error sending notification'));
            }
          } else {
            if (stryMutAct_9fa48("1475")) {
              {}
            } else {
              stryCov_9fa48("1475");
              CONSOLE_LOGGER.info(stryMutAct_9fa48("1476") ? "" : (stryCov_9fa48("1476"), 'Notification sent for personal conversation'));
            }
          }
        }
      }
    }
  }
  static async deleteMessage(receiptHandle) {
    if (stryMutAct_9fa48("1477")) {
      {}
    } else {
      stryCov_9fa48("1477");
      const deleteParams = stryMutAct_9fa48("1478") ? {} : (stryCov_9fa48("1478"), {
        QueueUrl: process.env.QUEUE_URL,
        ReceiptHandle: receiptHandle
      });
      CONSOLE_LOGGER.info(stryMutAct_9fa48("1479") ? "" : (stryCov_9fa48("1479"), 'Deleting message from queue with params: '), deleteParams);
      const command = new DeleteMessageCommand(deleteParams);
      return await sqsClient.send(command);
    }
  }
}
module.exports = SqsService;