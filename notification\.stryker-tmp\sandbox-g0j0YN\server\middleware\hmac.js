function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const Utils = require('../util/utilFunctions');
const crypto = require('crypto');
const HTTPStatus = require('../util/http-status');
const secretKey = process.env.HMAC_SECRET_KEY;
const url = require('url');

/**
 * @desc This function is being used to hmac authenticate each request
 * <AUTHOR>
 * @since 19/10/2023
 * @param {Object} req Request req.headers  req.headers.reqtoken
 * @param {Object} res Response
 * @param {function} next exceptionHandler Calls exceptionHandler
 */
module.exports = function (req, res, next) {
  if (stryMutAct_9fa48("84")) {
    {}
  } else {
    stryCov_9fa48("84");
    try {
      if (stryMutAct_9fa48("85")) {
        {}
      } else {
        stryCov_9fa48("85");
        const currentDate = MOMENT().utc().format(stryMutAct_9fa48("86") ? "" : (stryCov_9fa48("86"), 'MMYYYYDD')); // 03202426
        const reqURL = url.parse(req.originalUrl).pathname; // /feed/path
        const message = stryMutAct_9fa48("87") ? currentDate - reqURL : (stryCov_9fa48("87"), currentDate + reqURL);
        const receivedHmac = req.headers.reqtoken;
        const calculatedHmac = crypto.createHmac(stryMutAct_9fa48("88") ? "" : (stryCov_9fa48("88"), 'sha256'), secretKey).update(message).digest(stryMutAct_9fa48("89") ? "" : (stryCov_9fa48("89"), 'hex'));
        if (stryMutAct_9fa48("92") ? calculatedHmac !== receivedHmac : stryMutAct_9fa48("91") ? false : stryMutAct_9fa48("90") ? true : (stryCov_9fa48("90", "91", "92"), calculatedHmac === receivedHmac)) {
          if (stryMutAct_9fa48("93")) {
            {}
          } else {
            stryCov_9fa48("93");
            next();
          }
        } else {
          if (stryMutAct_9fa48("94")) {
            {}
          } else {
            stryCov_9fa48("94");
            throw stryMutAct_9fa48("95") ? "" : (stryCov_9fa48("95"), 'invalid hmac');
          }
        }
      }
    } catch (err) {
      if (stryMutAct_9fa48("96")) {
        {}
      } else {
        stryCov_9fa48("96");
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__(stryMutAct_9fa48("97") ? "" : (stryCov_9fa48("97"), 'ACCESS_DENIED'));
        res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
      }
    }
  }
};