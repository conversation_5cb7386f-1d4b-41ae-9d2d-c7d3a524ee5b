/**
 * This file contains routes used for notification.
 * Created by Growexx on 31/01/2024.
 * @name notificationRoutes
*/
// @ts-nocheck


const router = require('express').Router();

const NotificationController = require('../services/notification/notificationController');

const AuthMiddleware = require('../middleware/auth');
const HmacMiddleware = require('../middleware/hmac');

router.get('/list', HmacMiddleware, AuthMiddleware, NotificationController.getNotificationList);
router.post('/', HmacMiddleware, AuthMiddleware, NotificationController.createNotification);
router.patch('/', HmacMiddleware, AuthMiddleware, NotificationController.updateNotification);
router.post('/send', HmacMiddleware, AuthMiddleware, NotificationController.sendNotification);
router.post('/notify', NotificationController.notify);
router.post('/contact-us', NotificationController.contactUs);
module.exports = router;
