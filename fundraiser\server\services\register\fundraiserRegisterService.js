/* eslint-disable max-len */
const Stripe = require('../../util/Stripe');
const Organization = require('../../models/organization.model');
const FundraiserSignup = require('../../models/fundraiserSignup.model');
const Fundraiser = require('../../models/fundraiser.model');
const User = require('../../models/user.model');
const lodash = require('lodash');
const EmailService = require('../../util/sendEmail');
const Child = require('../../models/child.model');
const PaymentValidator = require('./fundraiserRegisterValidator');
const AwsOpenSearchService = require('../../util/opensearch');
const Utils = require('../../util/utilFunctions');
const CONSTANTS = require('../../util/constants');
const { v4: uuidv4 } = require('uuid');

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

/**
 * Class represents services for stripe
 */
class FundraiserRegisterService {
    /**
    * @desc This function is being used to create a stripe checkout session
    * <AUTHOR>
    * @since 15/11/2023
    * @param {Object} loggedInUser loggedInUser
    */
    static async createCheckoutSession (loggedInUser) {
        const user = await User.get({
            id: loggedInUser.id,
            email: loggedInUser.email
        });
        let customerId = lodash.get(user, 'stripeCustomerId');
        if (!customerId) {
            const customer = await Stripe.createCustomer(
                user.email,
                `${user.firstName} ${user.lastName}`,
                stripe
            );
            customerId = customer.id;
            await User.update(
                { id: loggedInUser.id, email: user.email },
                { stripeCustomerId: customerId }
            );
        }
        return customerId;
    }

    /**
     * @desc This function is being used to get or create child
     * @param {Object} reqBody reqBody
     * @param {string} organizationId organizationId
     * @returns {Object} - Returns child.
     */
    static async getOrCreateChild ({
        reqBody,
        organizationId
    }) {
        const {
            childId,
            isGuestSignup,
            userEmail,
            childFirstName,
            childLastName
        } = reqBody;

        let child;
        let user;
        if (!isGuestSignup) {
            child = await Child.get(childId);
        } else {
            const childId = uuidv4();
            const userId = uuidv4();
            user = await User.create({
                id: userId,
                email: userEmail,
                children: [childId],
                isGuestUser: true
            });
            child = await Child.create({
                id: childId,
                firstName: childFirstName,
                lastName: childLastName,
                createdBy: userId,
                associatedOrganizations: [organizationId],
                isGuestChild: true,
                guardians: [userId]
            });
        }

        return { child, user };
    }

    /**
    * @desc This function is being used to get fundraiser signup
    * @param {string} eventId eventId
    * @param {string} childId childId
    * @param {boolean} isGuestSignup isGuestSignup
    * @returns {Array} - Returns fundraiser signup, if the child is registered for the event, empty otherwise.
    */
    static async getFundraiserSignup ({ eventId, childId, isGuestSignup }) {
        let isExistsChild = [];
        if (!isGuestSignup) {
            isExistsChild = await FundraiserSignup
                .query('eventId').eq(eventId)
                .where('childId').eq(childId)
                .exec();
        }
        return isExistsChild;
    }

    /**
    * @desc This function is being used to create a payment intent and register user
    * <AUTHOR>
    * @since 15/11/2023
    * @param {Object} req req
    * @param {Object} loggedInUser loggedInUser
    * @param {Object} locale locale
    */
    static async registerUserAndPayment (req, loggedInUser, locale) {
        const Validator = new PaymentValidator(req.body, locale);
        Validator.validate();
        const {
            eventId,
            purchasedProducts,
            paymentType,
            isCoveredByUser,
            isGuestSignup
        } = req.body;

        const event = await Fundraiser.get({ id: eventId });
        if (
            isGuestSignup
            && [CONSTANTS.FUNDRAISER_TYPES.ORG_MANAGED_BOOSTER, CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP].includes(event.fundraiserType)
        ) {
            throw {
                message: MESSAGES.CANNOT_REGISTER_FOR_GUEST_SIGNUP,
                statusCode: 400
            };
        }
        const currentDateTime = MOMENT();
        const endDateTime = MOMENT(event.endDate, 'MM/DD/YYYY hh:mm:ss');
        const isFree = CONSTANTS.PAYMENT_TYPES.FREE;
        const isStripe = CONSTANTS.PAYMENT_TYPES.STRIPE;
        const isCheck = CONSTANTS.PAYMENT_TYPES.CHECK;
        const isCash = CONSTANTS.PAYMENT_TYPES.CASH;
        const isVenmo = CONSTANTS.PAYMENT_TYPES.VENMO;
        const isPaymentInitiated = CONSTANTS.PAYMENT_TYPES.PAYMENT_INITIATED;
        const isPending = CONSTANTS.PAYMENT_STATUS.PENDING;
        const isApproved = CONSTANTS.PAYMENT_STATUS.APPROVED;
        if (endDateTime.isBefore(currentDateTime)) {
            throw {
                message: MESSAGES.CANT_REGISTER_TO_PAST_EVENT,
                statusCode: 400
            };
        }

        let { boosterGoalForChild, boosterMessageForChild } = req.body;
        const { organizationId, products, membershipBenefitDetails, fundraiserType } = event;
        const membershipBenefitDetailsParsed = membershipBenefitDetails ?? {};
        membershipBenefitDetailsParsed.benefitDiscount = membershipBenefitDetailsParsed.benefitDiscount ? JSON.parse(membershipBenefitDetails.benefitDiscount) : [];

        if (fundraiserType === 'booster') {
            if (!boosterGoalForChild) {
                boosterGoalForChild = event.boosterGoalForChild;
            }
            if (!boosterMessageForChild) {
                boosterMessageForChild = event.boosterMessageForChild;
            }
        }

        if (membershipBenefitDetailsParsed.isOnlyForMembers && isGuestSignup) {
            throw {
                message: MESSAGES.FUNDRAISER_ONLY_FOR_MEMBERS,
                statusCode: 403
            };
        }

        const { child, user } = await this.getOrCreateChild({
            reqBody: req.body,
            organizationId
        });
        const childId = child.id;
        const childName = `${child.firstName} ${child.lastName}`;
        const fundraiserStartDate = MOMENT(event.startDate).format('MMMM Do YYYY');
        const fundraiserEndDate = MOMENT(event.endDate).format('MMMM Do YYYY');

        if (user) {
            loggedInUser = user;
        }

        let membershipType;
        let membershipId;

        if (fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP) {
            membershipType = purchasedProducts[0].membershipType;
            Validator.validateMembershipType(membershipType);
            membershipId = purchasedProducts[0].itemId;
            await this.checkExistingMembership(loggedInUser, child, organizationId, membershipType);
        }

        const childFundraiserSignup = await this.getFundraiserSignup({
            eventId,
            childId,
            isGuestSignup
        });
        const paymentStatus = lodash.get(
            childFundraiserSignup?.[0],
            'paymentDetails.paymentStatus'
        );
        const eventSignupPaymentType = lodash.get(
            childFundraiserSignup?.[0],
            'paymentDetails.paymentType'
        );

        const existingPaymentTypeStripe =
            eventSignupPaymentType && eventSignupPaymentType === isStripe;
        const isCashChequeVenmo =
            paymentType === isCash ||
            paymentType === isCheck ||
            paymentType === isVenmo;

        const membershipsEnabledForChild = Utils.getMembershipsOfChild({
            child,
            isGuestSignup,
            organizationId,
            eventStartDate: MOMENT(event.startDate)
        });

        if (membershipBenefitDetailsParsed.isOnlyForMembers && !membershipsEnabledForChild) {
            throw {
                message: MESSAGES.FUNDRAISER_ONLY_FOR_MEMBERS,
                statusCode: 403
            };
        }

        const parsedProducts = JSON.parse(products);

        const optionPriceMap = new Map();
        parsedProducts.forEach(product => {
            product.optionPrices?.forEach(optionPrice => {
                optionPriceMap.set(optionPrice.id, optionPrice.itemCost);
            });
        });

        let membershipBenefitAmount = 0;

        if (membershipsEnabledForChild && membershipBenefitDetailsParsed) {
            const purchasedMembershipMapping =
                membershipBenefitDetailsParsed?.benefitDiscount?.filter(
                    (membership) =>
                        `${membership.id}` ===
                        membershipsEnabledForChild.membershipId
                );
            membershipBenefitAmount = purchasedMembershipMapping.length ? purchasedMembershipMapping[0].value : 0;
        }

        let childMembershipDiscount = 0;
        const fee = purchasedProducts.reduce((acc, purchasedProduct) => {
            if (optionPriceMap.has(purchasedProduct.id)) {
                const itemCost = parseFloat(optionPriceMap.get(purchasedProduct.id));
                acc += itemCost * purchasedProduct.quantity;
            }
            return acc;
        }, 0);

        if (membershipsEnabledForChild && membershipBenefitDetails?.benefitDiscount && !isNaN(membershipBenefitAmount)) {
            const totalDiscount = Math.min(membershipBenefitAmount, fee);
            childMembershipDiscount = totalDiscount;
        }

        const finalFeeWithDiscount = fee - childMembershipDiscount;

        const purchasedProductsStr = JSON.stringify(purchasedProducts);

        // current user is already pending for the event with payment type stripe and now paying with payment type cash or cheque
        if (
            childFundraiserSignup.length &&
            paymentStatus === isPaymentInitiated &&
            existingPaymentTypeStripe &&
            isCashChequeVenmo
        ) {
            CONSOLE_LOGGER.info(
                'User first paid with stripe now trying to pay with cash/check/venmo'
            );
            const fundraiserSignup = childFundraiserSignup[0];
            fundraiserSignup.paymentDetails.stripeCustomerId = '';
            fundraiserSignup.paymentDetails.stripeConnectAccountId = '';
            fundraiserSignup.paymentDetails.transactionFee = 0;
            fundraiserSignup.paymentDetails.platformFeeCoveredBy = undefined;
            fundraiserSignup.paymentDetails.paymentType = paymentType;
            fundraiserSignup.paymentDetails.paymentStatus = isPending;
            fundraiserSignup.paymentDetails.membershipDiscount = childMembershipDiscount;
            fundraiserSignup.purchasedProducts = purchasedProductsStr;
            fundraiserSignup.stripePaymentIntentId = undefined;

            await fundraiserSignup.save();
            throw {
                message: MESSAGES.PAYMENT_SUCCESS_CASH_CHEQUE,
                data: {
                    fundraiserSignupId: fundraiserSignup.id
                },
                statusCode: 200
            };
        }

        if (paymentType === isFree && finalFeeWithDiscount === 0) {
            const fundraiserSignup = await FundraiserSignup.create({
                eventId,
                organizationId,
                childId,
                isGuestSignup,
                parentId: loggedInUser.id,
                createdBy: loggedInUser.id,
                purchasedProducts: purchasedProductsStr,
                paymentDetails: { paymentStatus: isApproved, paymentType: isFree },
                boosterGoalForChild,
                boosterMessageForChild
            });
            await this.updateChildMembershipStatus(event, loggedInUser, organizationId, membershipType, child, fundraiserSignup.id, membershipId);

            await this.sendRegisterMail({
                reqObj: {
                    fundraiserName: event.title,
                    startDate: fundraiserStartDate,
                    endDate: fundraiserEndDate,
                    userEmail: loggedInUser.email,
                    fundraiserSignupId: fundraiserSignup.id,
                    childName,
                    isGuestSignup
                },
                templateFile: 'fundraiserSignupConfirmation.html',
                subject: `Fundraiser Registration for ${event.title}`
            });

            await this.updateFreeEventChildFeeds({
                childId,
                eventId,
                isGuestSignup,
                fundraiserSignupId: fundraiserSignup.id,
                purchasedProducts: purchasedProductsStr
            });
            return {
                fundraiserSignupId: fundraiserSignup.id
            };
        } else if (
            paymentType === isCash ||
            paymentType === isCheck ||
            paymentType === isVenmo
        ) {
            const fundraiserSignup = await FundraiserSignup.create({
                eventId,
                organizationId,
                childId,
                isGuestSignup,
                parentId: loggedInUser.id,
                createdBy: loggedInUser.id,
                purchasedProducts: purchasedProductsStr,
                paymentDetails: { paymentType, paymentStatus: isPending, membershipDiscount: childMembershipDiscount },
                boosterGoalForChild,
                boosterMessageForChild
            });

            await this.sendRegisterMail({
                reqObj: {
                    fundraiserName: event.title,
                    startDate: fundraiserStartDate,
                    endDate: fundraiserEndDate,
                    userEmail: loggedInUser.email,
                    fundraiserSignupId: fundraiserSignup.id,
                    pendingReason: 'Once the admin approves your request, you’ll receive an email for the final registration.',
                    childName,
                    isGuestSignup
                },
                templateFile: 'fundraiserSignupRequestedPendingApproval.html',
                subject: `Fundraiser Registration for ${event.title}`
            });

            await this.updateChildFeedsAndPendingEvents({
                childId,
                eventId,
                isGuestSignup,
                fundraiserSignupId: fundraiserSignup.id,
                purchasedProducts: purchasedProductsStr
            });
            throw {
                message: MESSAGES.PAYMENT_SUCCESS_CASH_CHEQUE,
                data: {
                    fundraiserSignupId: fundraiserSignup.id
                },
                statusCode: 200
            };
        } else if (
            childFundraiserSignup.length &&
            paymentStatus === isPaymentInitiated &&
            existingPaymentTypeStripe &&
            paymentType === isStripe
        ) {
            CONSOLE_LOGGER.info(
                'User first paid with stripe now again trying to pay with stripe and total fee is ', fee
            );
            const org = await Organization.get({ id: organizationId });
            const orgId = lodash.get(org, 'paymentDetails.stripeConnectAccountId');
            const orgStripeStatus = lodash.get(
                org,
                'paymentDetails.stripeOnboardingStatus'
            );
            if (orgStripeStatus !== CONSTANTS.STATUS.ACTIVE || !orgId) {
                throw {
                    message: MESSAGES.STRIPE_NOT_ACTIVE,
                    statusCode: 403
                };
            }
            let isOptionalStripeFee = lodash.get(org, 'platformFeeCoveredBy');
            if (paymentType === isStripe && isOptionalStripeFee === 'optional') {
                const Validator = new PaymentValidator(req.body, locale);
                Validator.validateOptionalFee();
                isOptionalStripeFee = isCoveredByUser ? 'parent' : 'organization';
            }

            const customerId = await this.createCheckoutSession(loggedInUser);
            const { paymentIntent, stripeFee } = await Stripe.createPaymentIntent(
                stripe,
                finalFeeWithDiscount,
                orgId,
                customerId,
                org,
                isCoveredByUser
            );
            const fundraiserSignup = childFundraiserSignup[0];
            fundraiserSignup.purchasedProducts = purchasedProductsStr;
            fundraiserSignup.stripePaymentIntentId = paymentIntent.id;
            fundraiserSignup.paymentDetails.stripeCustomerId = customerId;
            fundraiserSignup.paymentDetails.stripeConnectAccountId = orgId;
            fundraiserSignup.paymentDetails.paymentType = isStripe;
            fundraiserSignup.paymentDetails.transactionFee = stripeFee / 100;
            fundraiserSignup.paymentDetails.platformFeeCoveredBy = isOptionalStripeFee;
            fundraiserSignup.paymentDetails.membershipDiscount = childMembershipDiscount;

            await fundraiserSignup.save();
            return {
                clientSecret: paymentIntent.client_secret,
                publicableKey: process.env.PUBLISHABLE_SECRET_KEY,
                fundraiserSignupId: fundraiserSignup.id
            };
        } else {
            CONSOLE_LOGGER.info('User trying to pay with stripe, payment-initiated');

            const org = await Organization.get({ id: organizationId });
            const orgId = lodash.get(org, 'paymentDetails.stripeConnectAccountId');
            const orgStripeStatus = lodash.get(
                org,
                'paymentDetails.stripeOnboardingStatus'
            );

            if (orgStripeStatus !== CONSTANTS.STATUS.ACTIVE || !orgId) {
                throw {
                    message: MESSAGES.STRIPE_NOT_ACTIVE,
                    statusCode: 403
                };
            }
            let isOptionalStripeFee = lodash.get(org, 'platformFeeCoveredBy');
            if (paymentType === isStripe && isOptionalStripeFee === 'optional') {
                const Validator = new PaymentValidator(req.body, locale);
                Validator.validateOptionalFee();
                isOptionalStripeFee = isCoveredByUser ? 'parent' : 'organization';
            }

            const customerId = await this.createCheckoutSession(loggedInUser);
            const { paymentIntent, stripeFee } = await Stripe.createPaymentIntent(
                stripe,
                finalFeeWithDiscount,
                orgId,
                customerId,
                org,
                isCoveredByUser
            );
            const fundraiserSignup = await FundraiserSignup.create({
                eventId,
                organizationId,
                childId,
                isGuestSignup,
                parentId: loggedInUser.id,
                createdBy: loggedInUser.id,
                stripePaymentIntentId: paymentIntent.id,
                purchasedProducts: purchasedProductsStr,
                paymentDetails: {
                    stripeCustomerId: customerId,
                    stripeConnectAccountId: orgId,
                    paymentType: isStripe,
                    paymentStatus: isPaymentInitiated,
                    transactionFee: stripeFee / 100,
                    platformFeeCoveredBy: isOptionalStripeFee,
                    membershipDiscount: childMembershipDiscount
                },
                boosterGoalForChild,
                boosterMessageForChild
            });

            return {
                clientSecret: paymentIntent.client_secret,
                publicableKey: process.env.PUBLISHABLE_SECRET_KEY,
                fundraiserSignupId: fundraiserSignup.id
            };
        }
    }

    static async updateFreeEventChildFeeds ({ childId, eventId, fundraiserSignupId, purchasedProducts, isGuestSignup }) {
        if (isGuestSignup) {
            return;
        }
        await AwsOpenSearchService.registerEventInChildEvents(
            CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
            childId,
            {
                fundraiserId: eventId,
                fundraiserSignupId,
                purchasedProducts
            }
        );
    }

    static async updateChildFeedsAndPendingEvents ({ childId, eventId, fundraiserSignupId, purchasedProducts, isGuestSignup }) {
        if (isGuestSignup) {
            return;
        }
        await AwsOpenSearchService.registerInChildPendingEvents(
            CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
            childId,
            {
                fundraiserId: eventId,
                fundraiserSignupId,
                purchasedProducts
            }
        );
    }

    /**
    * @desc This function is being used to handle Webhook
    * <AUTHOR>
    * @since 16/11/2023
    * @param {Object} req req
    * @param {Object} res res
    */
    static async stripeWebhook (req, res) {
        const signature = req.headers['stripe-signature'];
        let event;
        try {
            event = await Stripe.constructWebhookEvent(
                req.rawBody,
                signature,
                stripe
            );
        } catch (err) {
            CONSOLE_LOGGER.error('Webhook Error:', err);
            throw {
                message: `Webhook Error: ${err.message}`,
                statusCode: 400
            };
        }
        if (event.type === 'payment_intent.canceled') {
            const payment = event.data.object;
            const eventSignup = await FundraiserSignup.query('stripePaymentIntentId')
                .eq(payment.id)
                .exec();

            if (eventSignup.length) {
                await eventSignup[0].delete();
            }

            CONSOLE_LOGGER.info('payment intent: canceled ', payment);
        }
        if (event.type === 'payment_intent.succeeded') {
            // Extract payment details from the event
            const payment = event.data.object;

            // Fetch the event signup details
            const eventSignup = await FundraiserSignup.query('stripePaymentIntentId')
                .eq(payment.id)
                .exec();
            const {
                id: signupId,
                parentId,
                childId,
                eventId,
                purchasedProducts,
                paymentDetails,
                isGuestSignup
            } = eventSignup[0];

            paymentDetails.paymentStatus = CONSTANTS.PAYMENT_STATUS.APPROVED;
            await eventSignup[0].save();

            // Fetch user and child details
            const user = await User.query('id').eq(parentId).exec();
            const child = await Child.get({ id: childId });
            const childName = `${child.firstName} ${child.lastName}`;

            // Fetch event details
            const eventDetails = await Fundraiser.get({ id: eventId });
            let membershipType;
            let membershipId;

            if (eventDetails.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP) {
                const parsedPurchasedProducts = JSON.parse(purchasedProducts);
                membershipType = parsedPurchasedProducts[0].membershipType;
                membershipId = parsedPurchasedProducts[0].itemId;
            }

            await this.updateChildMembershipStatus(eventDetails, user[0], eventDetails.organizationId, membershipType, child, eventSignup.id, membershipId);
            await eventDetails.save();

            const fundraiserStartDate = MOMENT(eventDetails.startDate).format('MMMM Do YYYY');
            const fundraiserEndDate = MOMENT(eventDetails.endDate).format('MMMM Do YYYY');

            await this.sendRegisterMail({
                reqObj: {
                    fundraiserName: eventDetails.title,
                    startDate: fundraiserStartDate,
                    endDate: fundraiserEndDate,
                    userEmail: user[0].email,
                    fundraiserSignupId: signupId,
                    childName,
                    isGuestSignup
                },
                templateFile: 'fundraiserSignupConfirmation.html',
                subject: `Fundraiser Registration for ${eventDetails.title}`
            });

            if (!isGuestSignup) {
                await AwsOpenSearchService.registerEventInChildEvents(
                    CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD,
                    childId,
                    {
                        fundraiserId: eventId,
                        fundraiserSignupId: signupId,
                        purchasedProducts
                    }
                );
            }
            CONSOLE_LOGGER.info('payment intent: success', payment);
        }
        if (event.type === 'payment_intent.payment_failed') {
            const payment = event.data.object;
            const eventSignup = await FundraiserSignup.query('stripePaymentIntentId')
                .eq(payment.id)
                .exec();

            if (eventSignup.length && !eventSignup?.[0]?.isGuestSignup) {
                await eventSignup[0].delete();
            }

            CONSOLE_LOGGER.info('payment intent: payment_failed ', payment);
        } else {
            CONSOLE_LOGGER.info(`Unhandled event type: ${event.type}`);
        }
        CONSOLE_LOGGER.info(JSON.stringify(event), 'all events');
        res.send();
    }

    static async checkExistingMembership (user, child, organizationId, membershipType) {
        const hasFamilyMembership = user.membershipsPurchased?.some(
            membership => membership.organizationId === organizationId && MOMENT(membership.endDate).isAfter(MOMENT().utc())
        );

        if (hasFamilyMembership) {
            throw {
                message: MESSAGES.ALREADY_HAVE_FAMILY_MEMBERSHIP,
                statusCode: 400
            };
        }

        if (membershipType === CONSTANTS.MEMBERSHIP_TYPES.CHILD) {
            if (!child) {
                throw {
                    message: MESSAGES.INVALID_CHILD_ID,
                    statusCode: 400
                };
            }

            const hasChildMembership = child.membershipsPurchased?.some(
                membership => membership.organizationId === organizationId && MOMENT(membership.endDate).isAfter(MOMENT().utc())
            );

            if (hasChildMembership) {
                throw {
                    message: MESSAGES.ALREADY_HAVE_CHILD_MEMBERSHIP,
                    statusCode: 400
                };
            }
        }
    }

    /**
     * @desc Generic function to perform batchGet in chunks
     * <AUTHOR>
     * @since 25/07/2025
     * @param {Object} options
     * @param {Array} options.ids - List of IDs to fetch
     * @param {Object} options.model - Dynamoose model to use
     * @param {Array} [options.attributes] - Attributes to fetch (optional)
     * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET] - Max chunk size per request
     * @returns {Promise<Array>} Combined result of all batchGets
    */
    static async batchGetInChunks ({ ids, model, attributes, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET }) {
        if (!Array.isArray(ids)) {
            return [];
        }
        ids = ids.filter(Boolean);
        if (ids.length === 0) {
            return [];
        }

        const result = [];

        for (let i = 0; i < ids.length; i += chunkSize) {
            const chunk = ids.slice(i, i + chunkSize);
            const dataChunk = await model.batchGet(chunk, attributes ? { attributes } : undefined);
            result.push(...dataChunk);
        }

        return result;
    }

    static async updateChildMembershipStatus (fundraiser, parentUser, organizationId, membershipType, child, fundraiserSignupId, membershipId) {
        if (fundraiser.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP) {
            const startDate = MOMENT(fundraiser.startDate).toDate();
            const endDate = MOMENT(fundraiser.endDate).toDate();
            const membershipIdStr = `${membershipId}`;
            if (membershipType === CONSTANTS.MEMBERSHIP_TYPES.FAMILY) {
                if (!parentUser.membershipsPurchased) {
                    parentUser.membershipsPurchased = [];
                }
                parentUser.membershipsPurchased.push({
                    fundraiserSignupId,
                    organizationId,
                    startDate,
                    endDate,
                    membershipId: membershipIdStr,
                    membershipType: CONSTANTS.MEMBERSHIP_TYPES.FAMILY
                });

                const childrenIds = [...new Set(parentUser.children)].filter(Boolean);

                if (childrenIds.length > 0) {
                    const children = await this.batchGetInChunks({
                        ids: childrenIds,
                        model: Child
                    });

                    for (const childItem of children) {
                        if (childItem.associatedOrganizations.includes(organizationId)) {
                            if (!childItem.membershipsPurchased) {
                                childItem.membershipsPurchased = [];
                            }
                            childItem.membershipsPurchased.push({
                                fundraiserSignupId,
                                organizationId,
                                startDate,
                                endDate,
                                membershipId: membershipIdStr,
                                membershipType: CONSTANTS.MEMBERSHIP_TYPES.FAMILY
                            });

                            await childItem.save();
                        }
                    }
                }

                await parentUser.save();
            } else if (membershipType === CONSTANTS.MEMBERSHIP_TYPES.CHILD) {
                if (!child.membershipsPurchased) {
                    child.membershipsPurchased = [];
                }
                child.membershipsPurchased.push({
                    fundraiserSignupId,
                    organizationId,
                    startDate,
                    endDate,
                    membershipId: membershipIdStr,
                    membershipType: CONSTANTS.MEMBERSHIP_TYPES.CHILD
                });

                await child.save();
            }
        }
    }

    /**
    * @desc This function is being used to send event registration to email
    * <AUTHOR>
    * @since 16/11/2023
    * @param {Object} reqObj reqObj
    */
    static async sendRegisterMail ({ reqObj, templateFile, subject }) {
        const {
            childName,
            fundraiserName,
            startDate,
            endDate,
            fundraiserSignupId,
            isGuestSignup,
            userEmail: email,
            pendingReason = ''
        } = reqObj;

        if (!isGuestSignup) {
            return;
        }

        const template = `emailTemplates/${templateFile}`;
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const templateVariables = {
            year,
            fundraiserName,
            startDate,
            endDate,
            pendingReason,
            referenceId: fundraiserSignupId,
            trackingLink: `${process.env.FAMILY_WEB_APP_URL}/track-fundraiser-signup?referenceId=${fundraiserSignupId}`,
            email: CONSTANTS.CLIENT_INFO.HELP_EMAIL,
            appname: CONSTANTS.APP_NAME,
            username: `${childName}`
        };
        await EmailService.prepareAndSendEmail(
            [email],
            subject,
            template,
            templateVariables
        );
    }

    static async patchChildBoosterDetails (req, locale) {
        const Validator = new PaymentValidator(req.body, locale);
        Validator.validateChildBoosterDetails();
        const { fundraiserSignupId, boosterGoalForChild, boosterMessageForChild } = req.body;
        const fundraiserSignup = await FundraiserSignup.get({ id: fundraiserSignupId });

        if (!fundraiserSignup) {
            throw {
                message: 'Fundraiser Sign up not found',
                statusCode: 400
            };
        }
        if (
            fundraiserSignup.boosterGoalForChild !== boosterGoalForChild ||
            fundraiserSignup.boosterMessageForChild !== boosterMessageForChild
        ) {
            await FundraiserSignup.update(
                { id: fundraiserSignupId },
                { boosterGoalForChild, boosterMessageForChild }
            );
        }
    }
}

module.exports = FundraiserRegisterService;
