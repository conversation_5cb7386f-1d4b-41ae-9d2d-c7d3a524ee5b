// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const homeroomDonationsSchema = new dynamoose.Schema(stryMutAct_9fa48("439") ? {} : (stryCov_9fa48("439"), {
  homeRoomId: String,
  donationsAmount: Number
}));
const homeroomRegistrationsSchema = new dynamoose.Schema(stryMutAct_9fa48("440") ? {} : (stryCov_9fa48("440"), {
  homeRoomId: String,
  registrationsCount: Number
}));
const childLevelDonationsSchema = new dynamoose.Schema(stryMutAct_9fa48("441") ? {} : (stryCov_9fa48("441"), {
  childId: String,
  donationsAmount: Number
}));
const fundraiserSchema = new dynamoose.Schema(stryMutAct_9fa48("442") ? {} : (stryCov_9fa48("442"), {
  id: stryMutAct_9fa48("443") ? {} : (stryCov_9fa48("443"), {
    hashKey: stryMutAct_9fa48("444") ? false : (stryCov_9fa48("444"), true),
    type: String,
    default: stryMutAct_9fa48("445") ? () => undefined : (stryCov_9fa48("445"), () => uuidv4())
  }),
  title: stryMutAct_9fa48("446") ? {} : (stryCov_9fa48("446"), {
    type: String,
    required: stryMutAct_9fa48("447") ? false : (stryCov_9fa48("447"), true)
  }),
  description: stryMutAct_9fa48("448") ? {} : (stryCov_9fa48("448"), {
    type: String,
    required: stryMutAct_9fa48("449") ? false : (stryCov_9fa48("449"), true)
  }),
  products: stryMutAct_9fa48("450") ? {} : (stryCov_9fa48("450"), {
    type: String,
    required: stryMutAct_9fa48("451") ? false : (stryCov_9fa48("451"), true)
  }),
  imageURL: stryMutAct_9fa48("452") ? {} : (stryCov_9fa48("452"), {
    type: String
  }),
  fundraiserType: stryMutAct_9fa48("453") ? {} : (stryCov_9fa48("453"), {
    type: String,
    enum: stryMutAct_9fa48("454") ? [] : (stryCov_9fa48("454"), [stryMutAct_9fa48("455") ? "" : (stryCov_9fa48("455"), 'spiritwearFundraiser'), stryMutAct_9fa48("456") ? "" : (stryCov_9fa48("456"), 'annualFundraiser'), stryMutAct_9fa48("457") ? "" : (stryCov_9fa48("457"), 'membershipFundraiser'), stryMutAct_9fa48("458") ? "" : (stryCov_9fa48("458"), 'other'), stryMutAct_9fa48("459") ? "" : (stryCov_9fa48("459"), 'booster')]),
    default: stryMutAct_9fa48("460") ? "" : (stryCov_9fa48("460"), 'spiritwearFundraiser')
  }),
  startDate: stryMutAct_9fa48("461") ? {} : (stryCov_9fa48("461"), {
    type: Date,
    required: stryMutAct_9fa48("462") ? false : (stryCov_9fa48("462"), true)
  }),
  endDate: stryMutAct_9fa48("463") ? {} : (stryCov_9fa48("463"), {
    type: Date,
    required: stryMutAct_9fa48("464") ? false : (stryCov_9fa48("464"), true)
  }),
  status: stryMutAct_9fa48("465") ? {} : (stryCov_9fa48("465"), {
    type: String,
    enum: stryMutAct_9fa48("466") ? [] : (stryCov_9fa48("466"), [stryMutAct_9fa48("467") ? "" : (stryCov_9fa48("467"), 'published'), stryMutAct_9fa48("468") ? "" : (stryCov_9fa48("468"), 'draft'), stryMutAct_9fa48("469") ? "" : (stryCov_9fa48("469"), 'cancelled')]),
    default: stryMutAct_9fa48("470") ? "" : (stryCov_9fa48("470"), 'draft')
  }),
  organizationId: stryMutAct_9fa48("471") ? {} : (stryCov_9fa48("471"), {
    type: String,
    required: stryMutAct_9fa48("472") ? false : (stryCov_9fa48("472"), true),
    index: stryMutAct_9fa48("473") ? {} : (stryCov_9fa48("473"), {
      name: stryMutAct_9fa48("474") ? "" : (stryCov_9fa48("474"), 'organizationId-index'),
      global: stryMutAct_9fa48("475") ? false : (stryCov_9fa48("475"), true),
      project: stryMutAct_9fa48("476") ? false : (stryCov_9fa48("476"), true)
    })
  }),
  membershipBenefitDetails: stryMutAct_9fa48("477") ? {} : (stryCov_9fa48("477"), {
    type: Object,
    schema: stryMutAct_9fa48("478") ? {} : (stryCov_9fa48("478"), {
      'benefitDiscount': stryMutAct_9fa48("479") ? {} : (stryCov_9fa48("479"), {
        'type': String
      }),
      'isOnlyForMembers': stryMutAct_9fa48("480") ? {} : (stryCov_9fa48("480"), {
        'type': Boolean
      })
    })
  }),
  boosterGoal: stryMutAct_9fa48("481") ? {} : (stryCov_9fa48("481"), {
    type: Number,
    default: 0
  }),
  boosterGoalForChild: stryMutAct_9fa48("482") ? {} : (stryCov_9fa48("482"), {
    type: Number
  }),
  boosterMessageForChild: stryMutAct_9fa48("483") ? {} : (stryCov_9fa48("483"), {
    type: String
  }),
  homeroomStats: stryMutAct_9fa48("484") ? {} : (stryCov_9fa48("484"), {
    type: Object,
    schema: stryMutAct_9fa48("485") ? {} : (stryCov_9fa48("485"), {
      'donations': stryMutAct_9fa48("486") ? {} : (stryCov_9fa48("486"), {
        type: Array,
        schema: stryMutAct_9fa48("487") ? [] : (stryCov_9fa48("487"), [stryMutAct_9fa48("488") ? {} : (stryCov_9fa48("488"), {
          type: Object,
          schema: homeroomDonationsSchema
        })])
      }),
      'registrations': stryMutAct_9fa48("489") ? {} : (stryCov_9fa48("489"), {
        type: Array,
        schema: stryMutAct_9fa48("490") ? [] : (stryCov_9fa48("490"), [stryMutAct_9fa48("491") ? {} : (stryCov_9fa48("491"), {
          type: Object,
          schema: homeroomRegistrationsSchema
        })])
      })
    })
  }),
  childLevelStats: stryMutAct_9fa48("492") ? {} : (stryCov_9fa48("492"), {
    type: Object,
    schema: stryMutAct_9fa48("493") ? {} : (stryCov_9fa48("493"), {
      'donations': stryMutAct_9fa48("494") ? {} : (stryCov_9fa48("494"), {
        type: Array,
        schema: stryMutAct_9fa48("495") ? [] : (stryCov_9fa48("495"), [stryMutAct_9fa48("496") ? {} : (stryCov_9fa48("496"), {
          type: Object,
          schema: childLevelDonationsSchema
        })])
      })
    })
  }),
  raisedDonationsAmountForOrg: stryMutAct_9fa48("497") ? {} : (stryCov_9fa48("497"), {
    type: Number,
    default: 0
  }),
  createdBy: stryMutAct_9fa48("498") ? {} : (stryCov_9fa48("498"), {
    type: String,
    required: stryMutAct_9fa48("499") ? false : (stryCov_9fa48("499"), true)
  }),
  updatedBy: stryMutAct_9fa48("500") ? {} : (stryCov_9fa48("500"), {
    type: String
  }),
  isDeleted: stryMutAct_9fa48("501") ? {} : (stryCov_9fa48("501"), {
    type: Number,
    enum: stryMutAct_9fa48("502") ? [] : (stryCov_9fa48("502"), [0, 1]),
    default: 0
  })
}), stryMutAct_9fa48("503") ? {} : (stryCov_9fa48("503"), {
  timestamps: stryMutAct_9fa48("504") ? {} : (stryCov_9fa48("504"), {
    createdAt: stryMutAct_9fa48("505") ? "" : (stryCov_9fa48("505"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("506") ? "" : (stryCov_9fa48("506"), 'updatedAt')
  })
}));
module.exports = dynamoose.model(stryMutAct_9fa48("507") ? "" : (stryCov_9fa48("507"), 'Fundraisers'), fundraiserSchema);