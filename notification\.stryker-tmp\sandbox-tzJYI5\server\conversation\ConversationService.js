// @ts-nocheck
const CONSTANTS = require('../util/constants');
const Groups = require('../models/groups.model');
const MOMENT = require('moment');

class ConversationService {
    static async updateConversationGroups () {
        try {
            const groups = await Groups.query('groupType').eq(CONSTANTS.GROUP_CONVERSATION_TYPES.EVENT).using('groupType-index')
                .where('status').eq(CONSTANTS.GROUP_CONVERSATION_STATUS.ACTIVE).exec();
            const expiredEventGroups = groups.filter(
                group =>
                    MOMENT(group.eventMetaData?.endDateTime)
                        .add(CONSTANTS.GROUP_CONVERSATION_EXPIRATION_DAYS, 'days').isBefore(MOMENT())
            );

            CONSOLE_LOGGER.info('Expired event groups length', expiredEventGroups.length);
            CONSOLE_LOGGER.info('Expired event groups ids', expiredEventGroups.map(group => group.groupId));

            expiredEventGroups.forEach(group => {
                group.status = CONSTANTS.GROUP_CONVERSATION_STATUS.EXPIRED;
            });

            if (expiredEventGroups.length > 0) {
                await this.batchPutInChunks({
                    model: Groups,
                    data: expiredEventGroups
                });
            }
        } catch (error) {
            CONSOLE_LOGGER.error('Error updating conversation groups', error);
        }
    }

    /**
     * @desc Generic function to perform batchPut in chunks
     * <AUTHOR>
     * @since 25/07/2025
     * @param {Object} options
     * @param {Object} options.model - Dynamoose model to use
     * @param {Array} options.data - Data to put
     * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT] - Max chunk size per request
     */
    static async batchPutInChunks ({ model, data, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT }) {
        if (!Array.isArray(data)) {
            return;
        }

        data = data.filter(Boolean);
        if (data.length === 0) {
            return;
        }

        for (let i = 0; i < data.length; i += chunkSize) {
            const dataChunk = data.slice(i, i + chunkSize);
            await model.batchPut(dataChunk);
        }
    }
}

module.exports = ConversationService;
