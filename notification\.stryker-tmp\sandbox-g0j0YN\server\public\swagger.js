function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
module.exports = stryMutAct_9fa48("893") ? {} : (stryCov_9fa48("893"), {
  definition: stryMutAct_9fa48("894") ? {} : (stryCov_9fa48("894"), {
    explorer: stryMutAct_9fa48("895") ? false : (stryCov_9fa48("895"), true),
    openapi: stryMutAct_9fa48("896") ? "" : (stryCov_9fa48("896"), '3.0.0'),
    info: stryMutAct_9fa48("897") ? {} : (stryCov_9fa48("897"), {
      title: stryMutAct_9fa48("898") ? "" : (stryCov_9fa48("898"), 'Vaalee API Docs'),
      version: stryMutAct_9fa48("899") ? "" : (stryCov_9fa48("899"), '1.0.0'),
      description: stryMutAct_9fa48("900") ? "" : (stryCov_9fa48("900"), 'development environment hostname :  api.vaalee.com')
    }),
    servers: stryMutAct_9fa48("901") ? [] : (stryCov_9fa48("901"), [stryMutAct_9fa48("902") ? {} : (stryCov_9fa48("902"), {
      url: process.env.BASE_URL,
      description: stryMutAct_9fa48("903") ? `` : (stryCov_9fa48("903"), `${process.env.NODE_ENV} server`)
    })])
  }),
  apis: stryMutAct_9fa48("904") ? [] : (stryCov_9fa48("904"), [stryMutAct_9fa48("905") ? "" : (stryCov_9fa48("905"), './server/services/*.swagger.js'), stryMutAct_9fa48("906") ? "" : (stryCov_9fa48("906"), './server/services/**/*.swagger.js')])
});