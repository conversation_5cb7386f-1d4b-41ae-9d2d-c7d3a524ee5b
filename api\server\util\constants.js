/* eslint-disable max-len */
module.exports = {
    // For AES, this is always 16
    IV_LENGTH: 16,
    LOG_LEVEL: 'debug',
    PROFILE_PICTURE: {
        MIN_SIZE: 5120,
        MAX_SIZE: 5242880,
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png']
    },
    USER_DOCUMENT_FILE: {
        MIN_SIZE: 10240,
        MAX_SIZE: 5242880,
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf']
    },
    REGEX: {
        EMAIL: /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/,
        NAME: /^[a-zA-Z0-9,'~._^ -]{1,100}$/,
        ALPHA_ONLY: /^[a-zA-Z']*$/,
        ALPHA_SPECIAL_CHAR: /^[ A-Za-z0-9_@./#&+-]*$/,
        ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: /^[ A-Za-z_@./#&+-]*$/,
        FULL_ACCESS: /^[^<> ?//\\]+$/,
        ALPHA_NUMARIC: /^[\w@ ]+$/,
        templateName: /^[ A-Za-z0-9_@./#&+-]*$/,
        SUBJECT: /^[ A-Za-z0-9_@./#&+-]*$/,
        URL: /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/,
        MOBILE: /^([+]\d{1,2})?\d{5,15}$/,
        PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        ZIP: /^(?=.*\d.*)[A-Za-z0-9]{5,10}$/,
        UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    },
    OTPLENGTH: 6,
    OTP_EXPIRY_DURATION: 30,
    VERIFIED: {
        PENDING: 0,
        ACTIVE: 1
    },
    EMAIL_TEMPLATE: {
        REGISTER: 'verificationOtpMail.html',
        FORGOTPASSWORD: 'forgotPassword.html'
    },
    OTP_TYPE: ['register', 'forgotPassword'],
    CATEGORIES: {
        PTO: 'PTO',
        SCHOOL: 'School',
        HOME_ROOM: 'Homeroom',
        CLUB: 'Club',
        SUPER_ORGANIZATION: 'Super Organization',
        BUSINESS: 'Business'
    },
    CATEGORY_TYPE: ['PTO', 'School', 'Homeroom', 'Club', 'Business'],
    ORGANIZATION_NOT_ALLOWED: ['Super Organization', 'PTO', 'School', 'Homeroom'],
    QUANTITY_TYPE: ['People', 'Items'],
    STATUS: {
        INACTIVE: 'inactive',
        ACTIVE: 'active',
        SUSPENDED: 'suspended',
        FREEZED: 'freezed',
        ORGANIZATION_DISABLED: 0,
        ORGANIZATION_DELETED: 1,
        PUBLISHED: 'published',
        DELETED: 'deleted'
    },
    ENVIRONMENT: {
        TESTING: 'testing',
        LOCAL: 'local',
        DEV: 'dev',
        PRODUCTION: 'production'
    },
    DEVELOPERS_EMAIL: '<EMAIL>',
    SES_HOST: 'email-smtp.us-east-2.amazonaws.com',
    CLIENT_INFO: {
        EMAIL: '<EMAIL>'
    },
    APP_NAME: 'Vaalee',
    APP_URL: {
        ANDROID: 'https://play.google.com',
        IOS: 'https://www.apple.com/in/app-store/',
        WEBPAGE: 'https://invite.vaalee.com/'
    },
    ROLE: {
        USER: 1,
        ORG_SUPER_ADMIN: 'super admin',
        ORG_ADMIN: 'admin',
        ORG_EDITOR: 'editor',
        ORG: 3,
        ADMIN: 4
    },
    ACCESS_LEVEL: {
        APP: 'app',
        ORG_APP: 'org_app',
        ROOT: 'root'
    },
    INVITE_PARTNER_STATUS: {
        PENDING: 'pending',
        ACCEPTED: 'accepted',
        REJECTED: 'rejected'
    },
    ORG_ROLES: ['super admin', 'admin', 'editor'],
    ORG_STATUS: ['active', 'inactive', 'deleted'],
    ALLOWED_ASSOCIATED_COLORS: ['#2772ED', '#FACD01', '#90C33A', '#FF82A9', '#CB66D9', '#FF8F7C'],
    FOLLOW_REQUEST_STATUS: {
        REQUESTED: 'requested',
        ACCEPTED: 'accepted',
        REJECTED: 'rejected',
        UNFOLLOW: 'unfollow'
    },
    CONNECTION_STATUS: {
        REQUESTED_BY: 'requestedBy',
        REQUESTED_TO: 'requestedTo',
        CONNECTED: 'connected',
        REJECTED: 'rejected',
        DISCONNECTED: 'disconnected'
    },
    PROVIDER: {
        EMAIL: 'email',
        GOOGLE: 'google',
        APPLE: 'apple'
    },
    OPEN_SEARCH: {
        COLLECTION: {
            EVENT: 'events',
            CHILD: 'children',
            ORGANIZATION: 'organizations'
        },
        SEARCHABLE_FIELDS: {
            'events': ['title', 'details.details'],
            'children': ['firstName', 'lastName'],
            'organizations': ['name']
        },
        INDICES: {
            'events': 'eventSchema',
            'children': 'childSchema',
            'organizations': 'organizationSchema'
        }
    },
    CHILD_CONNECTION_STATUS: {
        NOT_CONNECTED: 'not-connected',
        ACCEPT_DENY: 'accept/deny',
        PENDING: 'pending',
        CONNECTED: 'connected'
    },
    COMPRESSION_QUALITY: 60,
    AWS_S3_PUBLIC_BUCKET: 'vaalee-assets',
    KEY_FOR_USER_EVENTS: 'user-events',
    KEY_FOR_USER_REGISTERED_EVENTS: 'user-registered-events',
    KEY_FOR_USER_CALENDAR_EVENTS: 'user-calendar-events',
    KEY_FOR_USER_DETAILS: 'user-details',
    KEY_FOR_CHILD_EVENTS: 'child-events',
    KEY_FOR_CHILD_REGISTERED_EVENTS: 'child-registered-events',
    KEY_FOR_CHILD_CALENDAR_EVENTS: 'child-calendar-events',
    KEY_FOR_CHILD_DETAILS: 'child-details',
    KEY_FOR_EVENT_DETAILS: 'event-details',
    KEY_FOR_FUNDRAISER_DETAILS: 'fundraiser-details',
    KEY_FOR_POST_DETAILS: 'post-details',
    KEY_FOR_ORGANIZATION_DETAILS: 'organization-details',
    FEED_VERSION_PREFIX: 'FeedVersionPrefix',
    ALLOWED_MESSAGE_SIZE: 125 * 1024, // 125kb,
    DB_BATCH_OPERATION_CHUNK_SIZE: {
        BATCH_PUT: 25,
        BATCH_DELETE: 25,
        BATCH_GET: 100
    },
    FEED_LIST: [
        {
            'id': '1',
            'title': 'Technology Conference',
            'details': 'Join us for Technology Conference.',
            'imageURL': 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'John Smith',
            'updatedBy': 'John Smith',
            'organizer': 'TechEvents LLC',
            'status': 'Active'
        },
        {
            'id': '2',
            'title': 'Fundraiser Gala',
            'details': 'Join us for Fundraiser Gala.',
            'imageURL': 'https://images.unsplash.com/photo-1469571486292-0ba58a3f068b?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'Emily Johnson',
            'updatedBy': 'Emily Johnson',
            'organizer': 'Helping Hands Foundation',
            'status': 'Active'
        },
        {
            'id': '3',
            'title': 'Art Exhibition',
            'details': 'Join us for Art Exhibition.',
            'imageURL': 'https://images.unsplash.com/photo-1578301978018-3005759f48f7?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'Sarah Lee',
            'updatedBy': 'Sarah Lee',
            'organizer': 'City Art Society',
            'status': 'Active'
        },
        {
            'id': '4',
            'title': 'Living Workshop',
            'details': 'Join us for Living Workshop.',
            'imageURL': 'https://images.unsplash.com/photo-1556761175-5973dc0f32e7?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'David Miller',
            'updatedBy': 'David Miller',
            'organizer': 'Green Living Initiative',
            'status': 'Active'
        },
        {
            'id': '5',
            'title': 'Food Festival',
            'details': 'Join us for Food Festival.',
            'imageURL': 'https://images.unsplash.com/photo-1437750769465-301382cdf094?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'Maria Rodriguez',
            'updatedBy': 'Maria Rodriguez',
            'organizer': 'Culinary Delights Events',
            'status': 'Active'
        },
        {
            'id': '6',
            'title': 'Entrepreneurship Summit',
            'details': 'Join us for Entrepreneurship Summit.',
            'imageURL': 'https://images.unsplash.com/photo-1474631245212-32dc3c8310c6?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'Alex Turner',
            'updatedBy': 'Alex Turner',
            'organizer': 'Startup Network Inc.',
            'status': 'Active'
        },
        {
            'id': '7',
            'title': 'Environmental Seminar',
            'details': 'Join us for Environmental Seminar.',
            'imageURL': 'https://images.unsplash.com/photo-1451847251646-8a6c0dd1510c?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'Olivia Green',
            'updatedBy': 'Olivia Green',
            'organizer': 'EcoAwareness Foundation',
            'status': 'Active'
        },
        {
            'id': '8',
            'title': 'Health and Wellness Expo',
            'details': 'Join us for Health and Wellness Expo.',
            'imageURL': 'https://images.unsplash.com/photo-1535914254981-b5012eebbd15?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'Michael Adams',
            'updatedBy': 'Michael Adams',
            'organizer': 'Wellness Ventures Ltd.',
            'status': 'Active'
        },
        {
            'id': '9',
            'title': 'Film Festival',
            'details': 'Join us for Film Festival.',
            'imageURL': 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'Emma Thompson',
            'updatedBy': 'Emma Thompson',
            'organizer': 'Silver Screen Events',
            'status': 'Active'
        },
        {
            'id': '10',
            'title': 'Technology Expo',
            'details': 'Join us for Technology Expo.',
            'imageURL': 'https://images.unsplash.com/photo-1558685555-bcdb675f9b9a?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'Dr. Thomas Anderson',
            'updatedBy': 'Dr. Thomas Anderson',
            'organizer': 'SciTech Innovations Inc.',
            'status': 'Active'
        },
        {
            'id': '11',
            'title': 'Annual Charity',
            'details': 'Join us for Annual Charity.',
            'imageURL': 'https://images.unsplash.com/photo-1532629345422-7515f3d16bb6?auto=format&fit=crop&q=80&w=800&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            'createdBy': 'Jane Anderson',
            'updatedBy': 'Jane Anderson',
            'organizer': 'Community Outreach Foundation',
            'status': 'Active'
        }
    ]
};
