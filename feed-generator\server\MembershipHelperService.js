const CONSTANTS = require('./constants');
const FundraiserSignups = require('./models/fundraiserSignup.model');
const User = require('./models/user.model');
const Child = require('./models/child.model');
const MOMENT = require('moment');

class MembershipHelperService {
    /**
     * @description Deletes all memberships
     * <AUTHOR>
     * @param {Object} fundraiser - The fundraiser object
     * @returns {Promise<Number>} The number of children deleted
     */
    static async deleteAllMemberships (fundraiser) {
        if (fundraiser.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP) {
            const fundraiserSignups = await FundraiserSignups.query('eventId')
                .eq(fundraiser.id)
                .using('eventId-index')
                .attributes(['id', 'parentId', 'childId', 'purchasedProducts'])
                .exec();

            for (const fundraiserSignup of fundraiserSignups) {
                const purchasedProducts = JSON.parse(
                    fundraiserSignup.purchasedProducts
                );
                if (
                    purchasedProducts[0].membershipType ===
                    CONSTANTS.MEMBERSHIP_TYPES.FAMILY
                ) {
                    const users = await User.query({
                        id: fundraiserSignup.parentId
                    }).exec();

                    if (users.length > 0) {
                        const user = users[0];
                        const childrenIds = await this.removeMembershipsFromUser(
                            user,
                            fundraiserSignup.id
                        );

                        if (childrenIds.length > 0) {
                            const children = await this.batchGetInChunks({
                                ids: childrenIds,
                                model: Child
                            });
                            for (const child of children) {
                                await this.removeMembershipsFromChild(
                                    child,
                                    fundraiserSignup.id
                                );
                            }
                        }
                    }
                } else {
                    const child = await Child.get(fundraiserSignup.childId);
                    if (child) {
                        await this.removeMembershipsFromChild(child, fundraiserSignup.id);
                    }
                }
            }
        }
        return fundraiser;
    }

    /**
     * @desc Generic function to perform batchGet in chunks
     * <AUTHOR>
     * @since 25/07/2025
     * @param {Object} options
     * @param {Array} options.ids - List of IDs to fetch
     * @param {Object} options.model - Dynamoose model to use
     * @param {Array} [options.attributes] - Attributes to fetch (optional)
     * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET] - Max chunk size per request
     * @returns {Promise<Array>} Combined result of all batchGets
    */
    static async batchGetInChunks ({ ids, model, attributes, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET }) {
        if (!Array.isArray(ids)) {
            return [];
        }
        ids = ids.filter(Boolean);
        if (ids.length === 0) {
            return [];
        }

        const result = [];

        for (let i = 0; i < ids.length; i += chunkSize) {
            const chunk = ids.slice(i, i + chunkSize);
            const dataChunk = await model.batchGet(chunk, attributes ? { attributes } : undefined);
            result.push(...dataChunk);
        }

        return result;
    }

    /**
     * @description Removes memberships from user
     * <AUTHOR>
     * @param {Object} user - The user object
     * @param {String} fundraiserSignupId - The fundraiser signup id
     * @returns {Promise<Array>} The children ids
     */
    static async removeMembershipsFromUser (user, fundraiserSignupId) {
        const originalLength = user.membershipsPurchased?.length || 0;
        user.membershipsPurchased = user.membershipsPurchased?.filter(
            (membership) => membership.fundraiserSignupId !== fundraiserSignupId
        );
        if (
            originalLength &&
            user.membershipsPurchased?.length !== originalLength
        ) {
            await user.save();
            return user.children;
        }
        return [];
    }

    /**
     * @description Removes memberships from child
     * <AUTHOR>
     * @param {Object} child - The child object
     * @param {String} fundraiserSignupId - The fundraiser signup id
     * @returns {Promise<Object>} The child object
     */
    static async removeMembershipsFromChild (child, fundraiserSignupId) {
        const originalLength = child.membershipsPurchased?.length || 0;
        child.membershipsPurchased = child.membershipsPurchased?.filter(
            (membership) => membership.fundraiserSignupId !== fundraiserSignupId
        );
        if (
            originalLength &&
            child.membershipsPurchased?.length !== originalLength
        ) {
            await child.save();
        }
        return child;
    }

    /**
     * @description Updates the family membership in the user
     * <AUTHOR>
     * @param {Object} user - The user object
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @param {Object} newFundraiser - The new fundraiser object
     * @returns {Promise<Boolean>} The updated flag
     */
    static async updateFamilyMembershipInUser (
        user,
        fundraiserSignup,
        newFundraiser
    ) {
        let updated = false;

        user.membershipsPurchased = user.membershipsPurchased?.map((membership) => {
            if (
                membership.fundraiserSignupId === fundraiserSignup.id &&
                membership.membershipType === CONSTANTS.MEMBERSHIP_TYPES.FAMILY
            ) {
                if (
                    !MOMENT(membership.startDate).isSame(
                        MOMENT(newFundraiser.startDate),
                        'D'
                    )
                ) {
                    membership.startDate = newFundraiser.startDate;
                    updated = true;
                }
                if (
                    !MOMENT(membership.endDate).isSame(MOMENT(newFundraiser.endDate), 'D')
                ) {
                    membership.endDate = newFundraiser.endDate;
                    updated = true;
                }
            }
            return membership;
        });

        if (updated) {
            await user.save();
        }

        return updated;
    }

    /**
     * @description Updates the family membership in the children
     * <AUTHOR>
     * @param {Array} childrenIds - The children ids
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @param {Object} newFundraiser - The new fundraiser object
     * @returns {Promise<Array>} The children ids
     */
    static async updateFamilyMembershipInChildren (
        childrenIds,
        fundraiserSignup,
        newFundraiser
    ) {
        if (childrenIds.length > 0) {
            const children = await this.batchGetInChunks({
                ids: childrenIds,
                model: Child
            });

            for (const child of children) {
                child.membershipsPurchased = child.membershipsPurchased?.map(
                    (membership) => {
                        if (
                            membership.fundraiserSignupId === fundraiserSignup.id &&
                            membership.membershipType === CONSTANTS.MEMBERSHIP_TYPES.FAMILY
                        ) {
                            if (
                                !MOMENT(membership.startDate).isSame(
                                    MOMENT(newFundraiser.startDate),
                                    'D'
                                )
                            ) {
                                membership.startDate = newFundraiser.startDate;
                            }
                            if (
                                !MOMENT(membership.endDate).isSame(
                                    MOMENT(newFundraiser.endDate),
                                    'D'
                                )
                            ) {
                                membership.endDate = newFundraiser.endDate;
                            }
                        }
                        return membership;
                    }
                );

                await child.save();
            }
        }
        return childrenIds;
    }

    /**
     * @description Updates the child membership
     * <AUTHOR>
     * @param {Object} child - The child object
     * @param {Object} fundraiserSignup - The fundraiser signup object
     * @param {Object} newFundraiser - The new fundraiser object
     * @returns {Promise<Boolean>} The updated flag
     */
    static async updateChildMembership (child, fundraiserSignup, newFundraiser) {
        let updated = false;

        child.membershipsPurchased = child.membershipsPurchased?.map(
            (membership) => {
                if (
                    membership.fundraiserSignupId === fundraiserSignup.id &&
                    membership.membershipType === CONSTANTS.MEMBERSHIP_TYPES.CHILD
                ) {
                    if (
                        !MOMENT(membership.startDate).isSame(
                            MOMENT(newFundraiser.startDate),
                            'D'
                        )
                    ) {
                        membership.startDate = newFundraiser.startDate;
                        updated = true;
                    }
                    if (
                        !MOMENT(membership.endDate).isSame(
                            MOMENT(newFundraiser.endDate),
                            'D'
                        )
                    ) {
                        membership.endDate = newFundraiser.endDate;
                        updated = true;
                    }
                }
                return membership;
            }
        );

        if (updated) {
            await child.save();
        }

        return updated;
    }

    /**
     * Updates the membership validity based on the changes in the fundraiser details.
     * @param {Object} oldFundraiser - The fundraiser before the update.
     * @param {Object} newFundraiser - The fundraiser after the update.
     * @returns {Promise} - Resolves when the membership validity is updated.
    */
    static async updateMembershipValidity (oldFundraiser, newFundraiser) {
        if (
            (!MOMENT(oldFundraiser.startDate).isSame(
                MOMENT(newFundraiser.startDate),
                'D'
            ) ||
                !MOMENT(oldFundraiser.endDate).isSame(
                    MOMENT(newFundraiser.endDate),
                    'D'
                )) &&
            oldFundraiser.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP &&
            newFundraiser.fundraiserType === CONSTANTS.FUNDRAISER_TYPES.MEMBERSHIP
        ) {
            const fundraiserSignups = await FundraiserSignups.query('eventId')
                .eq(oldFundraiser.id)
                .using('eventId-index')
                .attributes([
                    'id',
                    'parentId',
                    'childId',
                    'purchasedProducts',
                    'paymentDetails'
                ])
                .exec();

            for (const fundraiserSignup of fundraiserSignups) {
                if (
                    fundraiserSignup.paymentDetails.paymentStatus ===
                    CONSTANTS.PAYMENT_STATUS.APPROVED
                ) {
                    const purchasedProducts = JSON.parse(
                        fundraiserSignup.purchasedProducts
                    );
                    if (
                        purchasedProducts[0].membershipType ===
                        CONSTANTS.MEMBERSHIP_TYPES.FAMILY
                    ) {
                        const users = await User.query({
                            id: fundraiserSignup.parentId
                        }).exec();

                        if (users.length > 0) {
                            const user = users[0];
                            const updated = await this.updateFamilyMembershipInUser(
                                user,
                                fundraiserSignup,
                                newFundraiser
                            );

                            if (updated) {
                                const childrenIds = [...new Set(user.children)].filter(Boolean);
                                await this.updateFamilyMembershipInChildren(
                                    childrenIds,
                                    fundraiserSignup,
                                    newFundraiser
                                );
                            }
                        }
                    } else {
                        const child = await Child.get(fundraiserSignup.childId);
                        if (child) {
                            await this.updateChildMembership(
                                child,
                                fundraiserSignup,
                                newFundraiser
                            );
                        }
                    }
                }
            }
        }
        return newFundraiser;
    }
}

module.exports = MembershipHelperService;
