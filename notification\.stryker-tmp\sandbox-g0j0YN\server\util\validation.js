function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const GeneralError = require('../util/GeneralError');
const REQUIRED = stryMutAct_9fa48("2047") ? "" : (stryCov_9fa48("2047"), 'FIELD_REQUIRED');
const INVALID = stryMutAct_9fa48("2048") ? "" : (stryCov_9fa48("2048"), 'FIELD_NOT_VALID');
const INVALID_NAME = stryMutAct_9fa48("2049") ? "" : (stryCov_9fa48("2049"), 'NAME_NOT_VALID');

/**
 * Created by Growexx on 04/06/2020
 * @name validator
 */
class Validator {
  constructor(locale) {
    if (stryMutAct_9fa48("2050")) {
      {}
    } else {
      stryCov_9fa48("2050");
      this.NOT_VALID = INVALID;
      this.REQUIRED = REQUIRED;
      this.INVALID_NAME = INVALID_NAME;
      if (stryMutAct_9fa48("2052") ? false : stryMutAct_9fa48("2051") ? true : (stryCov_9fa48("2051", "2052"), locale)) {
        if (stryMutAct_9fa48("2053")) {
          {}
        } else {
          stryCov_9fa48("2053");
          this.__ = locale;
        }
      }
    }
  }

  /**
   * @desc This function is being used to validate if field exists
   * <AUTHOR>
   * @param {Number|String} value field value
   * @param {String} name field name
   * @since 04/10/2023
   */
  field(value, name) {
    if (stryMutAct_9fa48("2054")) {
      {}
    } else {
      stryCov_9fa48("2054");
      if (stryMutAct_9fa48("2057") ? false : stryMutAct_9fa48("2056") ? true : stryMutAct_9fa48("2055") ? value : (stryCov_9fa48("2055", "2056", "2057"), !value)) {
        if (stryMutAct_9fa48("2058")) {
          {}
        } else {
          stryCov_9fa48("2058");
          throw new GeneralError(this.__(REQUIRED, name), 400);
        }
      }
    }
  }

  /**
   * @desc This function is being used to validate if field is array and not empty
   * <AUTHOR>
   * @param {Array} value field value
   * @param {String} name field name
   * @since 21/02/2024
   */
  arrayField(value, name) {
    if (stryMutAct_9fa48("2059")) {
      {}
    } else {
      stryCov_9fa48("2059");
      if (stryMutAct_9fa48("2062") ? !value && !value.length : stryMutAct_9fa48("2061") ? false : stryMutAct_9fa48("2060") ? true : (stryCov_9fa48("2060", "2061", "2062"), (stryMutAct_9fa48("2063") ? value : (stryCov_9fa48("2063"), !value)) || (stryMutAct_9fa48("2064") ? value.length : (stryCov_9fa48("2064"), !value.length)))) {
        if (stryMutAct_9fa48("2065")) {
          {}
        } else {
          stryCov_9fa48("2065");
          throw new GeneralError(this.__(REQUIRED, name), 400);
        }
      }
    }
  }
}
module.exports = Validator;