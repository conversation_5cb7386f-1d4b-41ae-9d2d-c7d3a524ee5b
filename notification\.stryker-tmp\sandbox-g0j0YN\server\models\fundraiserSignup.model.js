function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const fundraiserSignupSchema = new dynamoose.Schema(stryMutAct_9fa48("508") ? {} : (stryCov_9fa48("508"), {
  id: stryMutAct_9fa48("509") ? {} : (stryCov_9fa48("509"), {
    hashKey: stryMutAct_9fa48("510") ? false : (stryCov_9fa48("510"), true),
    type: String,
    default: stryMutAct_9fa48("511") ? () => undefined : (stryCov_9fa48("511"), () => uuidv4())
  }),
  eventId: stryMutAct_9fa48("512") ? {} : (stryCov_9fa48("512"), {
    type: String,
    required: stryMutAct_9fa48("513") ? false : (stryCov_9fa48("513"), true),
    index: stryMutAct_9fa48("514") ? {} : (stryCov_9fa48("514"), {
      name: stryMutAct_9fa48("515") ? "" : (stryCov_9fa48("515"), 'eventId-index'),
      global: stryMutAct_9fa48("516") ? false : (stryCov_9fa48("516"), true),
      project: stryMutAct_9fa48("517") ? false : (stryCov_9fa48("517"), true)
    })
  }),
  organizationId: stryMutAct_9fa48("518") ? {} : (stryCov_9fa48("518"), {
    type: String,
    required: stryMutAct_9fa48("519") ? false : (stryCov_9fa48("519"), true)
  }),
  parentId: stryMutAct_9fa48("520") ? {} : (stryCov_9fa48("520"), {
    type: String,
    required: stryMutAct_9fa48("521") ? false : (stryCov_9fa48("521"), true)
  }),
  childId: stryMutAct_9fa48("522") ? {} : (stryCov_9fa48("522"), {
    type: String,
    required: stryMutAct_9fa48("523") ? false : (stryCov_9fa48("523"), true),
    index: stryMutAct_9fa48("524") ? {} : (stryCov_9fa48("524"), {
      name: stryMutAct_9fa48("525") ? "" : (stryCov_9fa48("525"), 'childId-index'),
      global: stryMutAct_9fa48("526") ? false : (stryCov_9fa48("526"), true),
      project: stryMutAct_9fa48("527") ? false : (stryCov_9fa48("527"), true)
    })
  }),
  boosterGoalForChild: stryMutAct_9fa48("528") ? {} : (stryCov_9fa48("528"), {
    type: Number
  }),
  boosterMessageForChild: stryMutAct_9fa48("529") ? {} : (stryCov_9fa48("529"), {
    type: String
  }),
  volunteerDetails: stryMutAct_9fa48("530") ? {} : (stryCov_9fa48("530"), {
    type: Set,
    schema: stryMutAct_9fa48("531") ? [] : (stryCov_9fa48("531"), [String])
  }),
  stripePaymentIntentId: stryMutAct_9fa48("532") ? {} : (stryCov_9fa48("532"), {
    type: String,
    index: stryMutAct_9fa48("533") ? {} : (stryCov_9fa48("533"), {
      global: stryMutAct_9fa48("534") ? false : (stryCov_9fa48("534"), true),
      name: stryMutAct_9fa48("535") ? "" : (stryCov_9fa48("535"), 'stripePaymentIntentId-index'),
      project: stryMutAct_9fa48("536") ? false : (stryCov_9fa48("536"), true)
    })
  }),
  paymentDetails: stryMutAct_9fa48("537") ? {} : (stryCov_9fa48("537"), {
    type: Object,
    schema: stryMutAct_9fa48("538") ? {} : (stryCov_9fa48("538"), {
      stripeCustomerId: stryMutAct_9fa48("539") ? {} : (stryCov_9fa48("539"), {
        type: String
      }),
      stripeConnectAccountId: stryMutAct_9fa48("540") ? {} : (stryCov_9fa48("540"), {
        type: String
      }),
      transactionFee: stryMutAct_9fa48("541") ? {} : (stryCov_9fa48("541"), {
        type: Number,
        default: 0
      }),
      platformFeeCoveredBy: stryMutAct_9fa48("542") ? {} : (stryCov_9fa48("542"), {
        type: String,
        enum: stryMutAct_9fa48("543") ? [] : (stryCov_9fa48("543"), [stryMutAct_9fa48("544") ? "" : (stryCov_9fa48("544"), 'parent'), stryMutAct_9fa48("545") ? "" : (stryCov_9fa48("545"), 'organization')])
      }),
      paymentStatus: stryMutAct_9fa48("546") ? {} : (stryCov_9fa48("546"), {
        type: String,
        enum: stryMutAct_9fa48("547") ? [] : (stryCov_9fa48("547"), [stryMutAct_9fa48("548") ? "" : (stryCov_9fa48("548"), 'pending'), stryMutAct_9fa48("549") ? "" : (stryCov_9fa48("549"), 'approved'), stryMutAct_9fa48("550") ? "" : (stryCov_9fa48("550"), 'cancelled'), stryMutAct_9fa48("551") ? "" : (stryCov_9fa48("551"), 'payment-initiated')]),
        required: stryMutAct_9fa48("552") ? false : (stryCov_9fa48("552"), true)
      }),
      paymentType: stryMutAct_9fa48("553") ? {} : (stryCov_9fa48("553"), {
        type: String,
        enum: stryMutAct_9fa48("554") ? [] : (stryCov_9fa48("554"), [stryMutAct_9fa48("555") ? "" : (stryCov_9fa48("555"), 'stripe'), stryMutAct_9fa48("556") ? "" : (stryCov_9fa48("556"), 'cash'), stryMutAct_9fa48("557") ? "" : (stryCov_9fa48("557"), 'cheque'), stryMutAct_9fa48("558") ? "" : (stryCov_9fa48("558"), 'venmo'), stryMutAct_9fa48("559") ? "" : (stryCov_9fa48("559"), 'free')])
      }),
      membershipDiscount: stryMutAct_9fa48("560") ? {} : (stryCov_9fa48("560"), {
        type: Number,
        default: 0
      })
    }),
    default: stryMutAct_9fa48("561") ? {} : (stryCov_9fa48("561"), {
      stripeCustomerId: stryMutAct_9fa48("562") ? "Stryker was here!" : (stryCov_9fa48("562"), ''),
      stripeConnectAccountId: stryMutAct_9fa48("563") ? "Stryker was here!" : (stryCov_9fa48("563"), ''),
      transactionFee: 0
    })
  }),
  purchasedProducts: stryMutAct_9fa48("564") ? {} : (stryCov_9fa48("564"), {
    type: String,
    required: stryMutAct_9fa48("565") ? false : (stryCov_9fa48("565"), true)
  }),
  isFulfilled: stryMutAct_9fa48("566") ? {} : (stryCov_9fa48("566"), {
    type: Boolean,
    default: stryMutAct_9fa48("567") ? true : (stryCov_9fa48("567"), false)
  }),
  isGuestSignup: stryMutAct_9fa48("568") ? {} : (stryCov_9fa48("568"), {
    type: Boolean,
    default: stryMutAct_9fa48("569") ? true : (stryCov_9fa48("569"), false)
  }),
  createdBy: stryMutAct_9fa48("570") ? {} : (stryCov_9fa48("570"), {
    type: String,
    required: stryMutAct_9fa48("571") ? false : (stryCov_9fa48("571"), true)
  }),
  updatedBy: stryMutAct_9fa48("572") ? {} : (stryCov_9fa48("572"), {
    type: String
  })
}), stryMutAct_9fa48("573") ? {} : (stryCov_9fa48("573"), {
  timestamps: stryMutAct_9fa48("574") ? {} : (stryCov_9fa48("574"), {
    createdAt: stryMutAct_9fa48("575") ? "" : (stryCov_9fa48("575"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("576") ? "" : (stryCov_9fa48("576"), 'updatedAt')
  })
}));
module.exports = dynamoose.model(stryMutAct_9fa48("577") ? "" : (stryCov_9fa48("577"), 'FundraiserSignup'), fundraiserSignupSchema);