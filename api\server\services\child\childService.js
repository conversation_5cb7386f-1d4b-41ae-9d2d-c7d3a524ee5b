const ChildValidator = require('./childValidator');
const Validation = require('../../util/validation');
const ChildModel = require('../../models/child.model');
const ChildOrganizationMappingModel = require('../../models/childOrganizationMapping.model');
const GeneralError = require('../../util/GeneralError');
const dynamoose = require('dynamoose');
const UploadService = require('../../util/uploadService');
const OrganizationModel = require('../../models/organization.model');
const ParentValidator = require('../parent/parentValidator');
const ParentService = require('../parent/parentService');
const AwsOpenSearchService = require('../../util/opensearch');
const Notification = require('../../util/notification');
const NotificationModel = require('../../models/notification.model');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const CONSTANTS = require('../../util/constants');

/**
 * Class represents services for child.
 */
class ChildService {
    /**
     * @desc This function is being used to get child details
     * <AUTHOR>
     * @since 22/12/2023
     */
    static async getChildDetails (req, loggedInUser, locale) {
        const { childId } = req.query;
        const Validator = new Validation(locale);
        Validator.field(childId, 'Child Id');
        Validator.uuid(childId, 'Child Id');
        if (!loggedInUser.children.includes(childId)) {
            throw {
                message: MESSAGES.CHILD_NOT_FOUND,
                statusCode: 404
            };
        }
        const child = await ChildModel.get({ id: childId });
        const organization = await OrganizationModel.get({ id: child.school });
        let homeroom;
        if (child.homeRoom) {
            homeroom = await OrganizationModel.get({ id: child.homeRoom });
        }

        const { acceptedFollowersCount, acceptedFollowingsCount } = await this.countAcceptedFollowersFollowings(child);
        const { acceptedConnectionsCount } = await this.countAcceptedConnections(child);

        const formattedResponse = {
            id: child.id,
            firstName: child.firstName,
            lastName: child.lastName,
            dob: child.dob,
            zipCode: child.zipCode,
            associatedColor: child.associatedColor,
            followersCount: acceptedFollowersCount,
            followingCount: acceptedFollowingsCount,
            connectionsCount: acceptedConnectionsCount,
            connections: child.connections && child.connections.length > 0 ? child.connections.filter(e=>e.status === 'connected') : [],
            schoolId: child.school,
            schoolName: organization.name,
            photoURL: child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null
        };

        if (homeroom) {
            formattedResponse.homeroomId = child.homeRoom;
            formattedResponse.homeroomName = homeroom.name;
        }

        return formattedResponse;
    }

    /**
     * @desc This function is being used to validate child existence
     * <AUTHOR>
     * @since 27/12/2023
     */
    static async validateChildExists (req, loggedInUser, locale) {
        const Validator = new ParentValidator(req.body, locale);
        Validator.validate();
        const { firstName, lastName, schoolId } = req.body;
        CONSOLE_LOGGER.info(`Exists Child API called with data lastName: ${lastName} schoolId: ${schoolId} by user: ${loggedInUser.id}`);

        const childExists = await ChildModel
            .query('school').eq(schoolId)
            .using('school-index')
            .where('firstName').eq(firstName)
            .where('lastName').eq(lastName)
            .exec();

        if (childExists.length) {
            CONSOLE_LOGGER.info(`Child already exists with data lastName: ${lastName} schoolId: ${schoolId} by user: ${loggedInUser.id}`);
            throw new GeneralError(MESSAGES.CHILD_ALREADY_EXISTS, 200);
        } else {
            CONSOLE_LOGGER.info(`Child does not exists with data 
                lastName: ${lastName} schoolId: ${schoolId} by user: ${loggedInUser.id} so adding child`);
            return await ParentService.addChild(req, loggedInUser, locale);
        }
    }

    /**
     * @desc This function is being used to update child details by parent
     * <AUTHOR>
     * @since 20/12/2023
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
     */
    static async sendFollowRequestToChild (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale);
        Validator.validateFollowRequest();

        const { followerChildId, followingChildId } = req.body;

        const { followerChild, followingChild } = await this.validateChildAssociationWithUser(
            loggedInUser, followerChildId, followingChildId, true, locale);

        this.validateFollowRequest(followerChild, followingChild, followingChildId);

        followerChild.followings.push({ childId: followingChildId, status: CONSTANTS.FOLLOW_REQUEST_STATUS.REQUESTED });
        followingChild.followers.push({ childId: followerChildId, status: CONSTANTS.FOLLOW_REQUEST_STATUS.REQUESTED });

        const transactions = [];

        const followerChildUpdate = await ChildModel.transaction.update({ id: followerChildId }, { followings: followerChild.followings });
        transactions.push(followerChildUpdate);

        const followingChildUpdate = await ChildModel.transaction.update({ id: followingChildId }, { followers: followingChild.followers });
        transactions.push(followingChildUpdate);

        await dynamoose.transaction(transactions);
    }

    /**
     * @desc This function is being used to send connection request from one child to another
     * <AUTHOR>
     * @since 20/12/2023
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
    */
    static async sendConnectionRequest (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale);
        Validator.validateConnectionRequest();

        const { requesterChildId, requestedChildId } = req.body;

        const { requesterChild, requestedChild } = await this.validateChildAssociationWithUserForConnection(
            loggedInUser, requesterChildId, requestedChildId, true, locale);

        this.validateConnectionRequest(requesterChild, requestedChild, requestedChildId);

        requesterChild.connections.push({ childId: requestedChildId, status: CONSTANTS.CONNECTION_STATUS.REQUESTED_TO });
        requestedChild.connections.push({ childId: requesterChildId, status: CONSTANTS.CONNECTION_STATUS.REQUESTED_BY });

        const transactions = [];

        const requesterChildUpdate = await ChildModel.transaction
            .update({ id: requesterChildId }, { connections: requesterChild.connections });
        transactions.push(requesterChildUpdate);

        const requestedChildUpdate = await ChildModel.transaction
            .update({ id: requestedChildId }, { connections: requestedChild.connections });
        transactions.push(requestedChildUpdate);

        await dynamoose.transaction(transactions);
        this.sendConnectionNotification(requestedChildId, requesterChild, requestedChild);
    }


    static async sendConnectionNotification (childId, requesterChild, requestedChild) {
        const msg = `You’ve got a new connection request from ${requesterChild.firstName} ${requesterChild.lastName}!`;
        const title = 'Connection Request';
        const clickAction = 'vaalee://routeName=childDetails';
        const formattedChld = {
            associatedColor: requestedChild.associatedColor,
            id: requestedChild.id,
            firstName: requestedChild.firstName,
            lastName: requestedChild.lastName,
            photoURL: requestedChild.photoURL || null
        };
        const notificationData = {
            child: JSON.stringify(formattedChld),
            route: 'childDetails',
            activeTab: 'requestedBy'
        };
        const notificationObject = Notification.createNotificationObject({
            clickAction,
            title,
            topic: `childId-${childId}`,
            body: msg,
            data: notificationData
        });
        await this.pushInNotificationTable({
            associatedChildId: childId,
            notificationAction: clickAction,
            title
        }, { route: notificationData.route, activeTab: notificationData.activeTab }, msg, notificationObject, requesterChild.id);
    }

    /**
     * @desc Generic function to perform batchPut in chunks
     * <AUTHOR>
     * @since 25/07/2025
     * @param {Object} options
     * @param {Object} options.model - Dynamoose model to use
     * @param {Array} options.data - Data to put
     * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT] - Max chunk size per request
     */
    static async batchPutInChunks ({ model, data, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT }) {
        if (!Array.isArray(data)) {
            return;
        }

        data = data.filter(Boolean);
        if (data.length === 0) {
            return;
        }

        for (let i = 0; i < data.length; i += chunkSize) {
            const dataChunk = data.slice(i, i + chunkSize);
            await model.batchPut(dataChunk);
        }
    }

    static async pushInNotificationTable (notification, payload, description, notificationObject, requesterChildId) {
        const { associatedChildId, notificationAction, title } = notification;
        const child = await ChildModel.get({ id: associatedChildId });
        const notificationIds = [];
        const notifications = [];
        for (const guardian of child.guardians) {
            const id = uuidv4();
            notificationIds.push(id);
            const notification = {
                id,
                description,
                associatedChildId,
                notificationAction,
                title,
                payload,
                userId: guardian,
                readStatus: false
            };
            notifications.push(notification);
        }
        await this.batchPutInChunks({
            model: NotificationModel,
            data: notifications
        });
        const childConnections = child.connections.map(conn => {
            if (conn.childId === requesterChildId) {
                conn.notificationIds = notificationIds;
            }
            return conn;
        });
        child.connections = childConnections;
        await child.save();
        try {
            await axios.post(`${process.env.BE_URL}notification/notify`, notificationObject);
        } catch (error) {
            CONSOLE_LOGGER.error(error);
        }
    }

    /**
     * @desc This function is used to validate the association between two children and a user
     * <AUTHOR>
     * @since 20/12/2023
     * @param {Object} loggedInUser - The logged in user
     * @param {String} followerChildId - The ID of the child sending the follow request or review
     * @param {String} followingChildId - The ID of the child being followed or reviewed
     * @param {Boolean} isFollowRequest - A flag indicating whether this is a follow request (true) or a review (false)
     * @param {Object} locale - The locale object used for localization
     * @returns {Object} An object containing the follower and following child objects
     * @throws {GeneralError} Throws an error if the children are not found or if a child tries to follow itself
    */
    static async validateChildAssociationWithUser (loggedInUser, followerChildId, followingChildId, isFollowRequest, locale) {
        if (isFollowRequest) {
            if (loggedInUser.children.indexOf(followerChildId) === -1) {
                throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'follower'), 400);
            }
            if (loggedInUser.children.indexOf(followingChildId) !== -1) {
                throw new GeneralError(MESSAGES.CANT_FOLLOW_OWN_CHILD, 400);
            }
        } else {
            if (loggedInUser.children.indexOf(followingChildId) === -1) {
                throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'following'), 400);
            }
            if (loggedInUser.children.indexOf(followerChildId) !== -1) {
                throw new GeneralError(MESSAGES.CANT_FOLLOW_OWN_CHILD, 400);
            }
        }

        const followerChild = await ChildModel.get(followerChildId);
        const followingChild = await ChildModel.get(followingChildId);

        if (!followerChild) {
            throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'follower'), 400);
        }
        if (!followingChild) {
            throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'following'), 400);
        }

        if (followerChild.school !== followingChild.school) {
            throw new GeneralError(MESSAGES.CANT_FOLLOW_CHILD_FROM_OTHER_SCHOOL, 400);
        }

        return { followerChild, followingChild };
    }

    /**
     * @desc This function is used to validate the association of a child with a user for a connection request
     * <AUTHOR>
     * @since 20/12/2023
     * @param {Object} loggedInUser - The user who is logged in
     * @param {String} requesterChildId - The ID of the child sending the connection request
     * @param {String} requestedChildId - The ID of the child being requested to connect
     * @param {Boolean} isConnectionRequest - A flag indicating whether this is a connection request
     * @param {Object} locale - The locale object for localization
     * @returns {Object} An object containing the requesterChild and requestedChild objects
     * @throws {GeneralError} Throws an error if the children are not found or if a child tries to connect with itself
     */
    static async validateChildAssociationWithUserForConnection (
        loggedInUser, requesterChildId, requestedChildId, isConnectionRequest, locale, checkSchool = true) {
        if (isConnectionRequest) {
            if (loggedInUser.children.indexOf(requesterChildId) === -1) {
                throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'requester'), 400);
            }
            if (loggedInUser.children.indexOf(requestedChildId) !== -1) {
                throw new GeneralError(MESSAGES.CANT_CONNECT_OWN_CHILD, 400);
            }
        } else {
            if (loggedInUser.children.indexOf(requestedChildId) === -1) {
                throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'requested'), 400);
            }
            if (loggedInUser.children.indexOf(requesterChildId) !== -1) {
                throw new GeneralError(MESSAGES.CANT_ACCEPT_DENY_OWN_CHILD, 400);
            }
        }

        const requesterChild = await ChildModel.get(requesterChildId);
        const requestedChild = await ChildModel.get(requestedChildId);

        if (!requesterChild) {
            throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'requester'), 400);
        }
        if (!requestedChild) {
            throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'requested'), 400);
        }

        if (requesterChild.school !== requestedChild.school && checkSchool) {
            throw new GeneralError(MESSAGES.CANT_CONNECT_CHILD_FROM_OTHER_SCHOOL, 400);
        }

        return { requesterChild, requestedChild };
    }

    /**
     * @desc This function is being used to validate is new request or if a request has already been accepted
     * <AUTHOR>
     * @since 20/12/2023
     * @param {Object} followerChild - The child sending the follow request
     * @param {Object} followingChild - The child being followed
     * @param {String} followingChildId - The ID of the child that the followerChild is attempting to follow
     * @param {String} status - The status to check for in the follow request
    */
    static validateFollowRequest (followerChild, followingChild, followingChildId, status) {
        followerChild.followings = followerChild.followings || [];
        followingChild.followers = followingChild.followers || [];

        const followingIndex = followerChild.followings.findIndex(following => following.childId === followingChildId);
        const followerIndex = followingChild.followers.findIndex(follower => follower.childId === followerChild.id);

        if (followingIndex !== -1 && followerIndex !== -1) {
            const following = followerChild.followings[followingIndex];
            const follower = followingChild.followers[followerIndex];

            if (status) {
                if (following.status === status && follower.status === status
                    && status === CONSTANTS.FOLLOW_REQUEST_STATUS.ACCEPTED) {
                    throw new GeneralError(MESSAGES.ALREADY_REQUEST_ACCEPTED, 400);
                } else if (following.status === CONSTANTS.FOLLOW_REQUEST_STATUS.REQUESTED &&
                    follower.status === CONSTANTS.FOLLOW_REQUEST_STATUS.REQUESTED &&
                    status === CONSTANTS.FOLLOW_REQUEST_STATUS.UNFOLLOW) {
                    throw new GeneralError(MESSAGES.CANT_UNFOLLOW, 400);
                } else {
                    /** do not updated status */
                }
            } else if (following.status === CONSTANTS.FOLLOW_REQUEST_STATUS.ACCEPTED) {
                throw new GeneralError(MESSAGES.ALREADY_FOLLOWING_CHILD, 400);
            } else if (following.status === CONSTANTS.FOLLOW_REQUEST_STATUS.REQUESTED) {
                throw new GeneralError(MESSAGES.ALREADY_FOLLOW_REQUEST_SENT, 400);
            } else {
                throw new GeneralError(MESSAGES.CANT_FOLLOW_CHILD, 400);
            }
        }

        return { followingIndex, followerIndex };
    }

    /**
     * @desc This function is used to validate a new connection request or check if a connection has already been accepted
     * <AUTHOR>
     * @since 16/01/2024
     * @param {Object} requesterChild - The child object that is sending the connection request
     * @param {Object} requestedChild - The child object that is being requested to connect
     * @param {String} requestedChildId - The ID of the child that the requesterChild is attempting to connect with
     * @param {String} status - The status to check for in the connection request
     * @returns {Object} - An object containing the indices of the connection request in both children's connections arrays
     * @throws {GeneralError} - Throws an error if the connection request cannot be validated
     */
    static validateConnectionRequest (requesterChild, requestedChild, requestedChildId, status) {
        requesterChild.connections = requesterChild.connections || [];
        requestedChild.connections = requestedChild.connections || [];

        const connectionIndexInRequesterChild =
            requesterChild.connections.findIndex(connection => connection.childId === requestedChildId);
        const connectionIndexInRequestedChild =
            requestedChild.connections.findIndex(connection => connection.childId === requesterChild.id);

        if (connectionIndexInRequesterChild !== -1 && connectionIndexInRequestedChild !== -1) {
            const connectionInRequesterChild = requesterChild.connections[connectionIndexInRequesterChild];
            const connectionInRequestedChild = requestedChild.connections[connectionIndexInRequestedChild];

            if (status) {
                if (connectionInRequesterChild.status === status
                    && connectionInRequestedChild.status === status
                    && status === CONSTANTS.CONNECTION_STATUS.CONNECTED) {
                    throw new GeneralError(MESSAGES.ALREADY_CONNECTED, 400);
                } else if (connectionInRequesterChild.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_TO
                    && connectionInRequestedChild.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_BY
                    && status === CONSTANTS.CONNECTION_STATUS.DISCONNECTED) {
                    throw new GeneralError(MESSAGES.CANT_DISCONNECT, 400);
                } else {
                    /** do not update status */
                }
            } else if (connectionInRequesterChild.status === CONSTANTS.CONNECTION_STATUS.CONNECTED) {
                throw new GeneralError(MESSAGES.ALREADY_CONNECTED, 400);
            } else if (connectionInRequesterChild.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_TO) {
                throw new GeneralError(MESSAGES.ALREADY_SENT_CONNECTION_REQUEST, 400);
            } else if (connectionInRequesterChild.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_BY) {
                throw new GeneralError(MESSAGES.ALREADY_RECEIVED_CONNECTION_REQUEST, 400);
            } else {
                throw new GeneralError(MESSAGES.CANT_CONNECT_CHILD, 400);
            }
        }

        return { connectionIndexInRequesterChild, connectionIndexInRequestedChild };
    }

    /**
     * @desc This function is being used to change follow request status of a child by parent
     * <AUTHOR>
     * @since 20/12/2023
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
    */
    static async changeFollowRequestStatus (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale);
        Validator.validateChangeFollowRequestStatus();

        const { followingChildId, followerChildId, status } = req.body;

        const { followingChild, followerChild } = await this.validateChildAssociationWithUser(
            loggedInUser, followerChildId, followingChildId, false, locale);

        const { followingIndex, followerIndex } = this.validateFollowRequest(followerChild, followingChild, followingChildId, status);

        if (followingIndex === -1 || followerIndex === -1) {
            throw new GeneralError(MESSAGES.FOLLOW_REQUEST_NOT_FOUND, 400);
        }

        if (status === CONSTANTS.FOLLOW_REQUEST_STATUS.REJECTED) {
            followingChild.followers.splice(followerIndex, 1);
            followerChild.followings.splice(followingIndex, 1);
        } else {
            followingChild.followers[followerIndex].status = status;
            followerChild.followings[followingIndex].status = status;
        }

        const transactions = [];

        const followingChildUpdate = await ChildModel.transaction
            .update({ id: followingChildId }, { followers: followingChild.followers });
        transactions.push(followingChildUpdate);

        const followerChildUpdate = await ChildModel.transaction
            .update({ id: followerChildId }, { followings: followerChild.followings });
        transactions.push(followerChildUpdate);

        await dynamoose.transaction(transactions);

        req.query.childId = followingChildId;
        req.query.relationshipType = 'requestedBy';
        return await this.getRelationships(req, loggedInUser, locale);
    }

    /**
     * @desc This function is being used to change connection request status of a child by parent
     * @name changeConnectionRequestStatus
     * <AUTHOR>
     * @since 17/01/2024
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
     * @returns {Object} The updated list of connections
     * @throws {GeneralError} Throws an error if the connection request is not found
     */
    static async changeConnectionRequestStatus (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale);
        Validator.validateChangeConnectionRequestStatus();

        const { requesterChildId, requestedChildId, status } = req.body;

        const { requesterChild, requestedChild } = await this.validateChildAssociationWithUserForConnection(
            loggedInUser, requesterChildId, requestedChildId, false, locale);

        const { connectionIndexInRequesterChild, connectionIndexInRequestedChild } = this.validateConnectionRequest(
            requesterChild, requestedChild, requestedChildId, status);

        if (connectionIndexInRequesterChild === -1 || connectionIndexInRequestedChild === -1) {
            throw new GeneralError(MESSAGES.CONNECTION_REQUEST_NOT_FOUND, 400);
        }
        const notificationIds = requestedChild.connections[connectionIndexInRequestedChild].notificationIds;

        if (status === CONSTANTS.CONNECTION_STATUS.REJECTED) {
            requestedChild.connections[connectionIndexInRequestedChild].notificationIds = [];
            requesterChild.connections.splice(connectionIndexInRequesterChild, 1);
            requestedChild.connections.splice(connectionIndexInRequestedChild, 1);
        } else {
            requestedChild.connections[connectionIndexInRequestedChild].notificationIds = [];
            requesterChild.connections[connectionIndexInRequesterChild].status = status;
            requestedChild.connections[connectionIndexInRequestedChild].status = status;
        }

        const transactions = [];

        const requesterChildUpdate = await ChildModel.transaction
            .update({ id: requesterChildId }, { connections: requesterChild.connections });
        transactions.push(requesterChildUpdate);

        const requestedChildUpdate = await ChildModel.transaction
            .update({ id: requestedChildId }, { connections: requestedChild.connections });
        transactions.push(requestedChildUpdate);

        await dynamoose.transaction(transactions);

        req.query.childId = requestedChildId;
        req.query.connectionType = CONSTANTS.CONNECTION_STATUS.REQUESTED_BY;
        await this.deleteConnectionNotification(notificationIds);
        return await this.getChildConnections(req, loggedInUser, locale);
    }

    /**
     * @desc Generic function to perform batchDelete in chunks
     * <AUTHOR>
     * @since 25/07/2025
     * @param {Object} options
     * @param {Object} options.model - Dynamoose model to use
     * @param {Array} options.data - Data to delete
     * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_DELETE] - Max chunk size per request
     */
    static async batchDeleteInChunks ({ model, data, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_DELETE }) {
        if (!Array.isArray(data)) {
            return;
        }

        data = data.filter(Boolean);
        if (data.length === 0) {
            return;
        }

        for (let i = 0; i < data.length; i += chunkSize) {
            const dataChunk = data.slice(i, i + chunkSize);
            await model.batchDelete(dataChunk);
        }
    }

    static async deleteConnectionNotification (notificationIds) {
        await this.batchDeleteInChunks({
            model: NotificationModel,
            data: notificationIds
        });
    }

    /**
     * @desc This function is being used to remove follower of a child by parent
     * @name removeFollower
     * <AUTHOR>
     * @since 28/12/2023
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
     * @returns {Object} The updated list of followers
     */
    static async removeFollower (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale);
        Validator.validateFollowRequest();

        const { followerChildId, followingChildId } = req.body;

        const { followingChild, followerChild } = await this.validateChildAssociationWithUser(
            loggedInUser, followerChildId, followingChildId, false, locale);


        const { followingIndex, followerIndex } = this.validateFollowRequest(
            followerChild, followingChild, followingChildId, CONSTANTS.FOLLOW_REQUEST_STATUS.REJECTED);

        if (followingIndex === -1 || followerIndex === -1) {
            throw new GeneralError(MESSAGES.FOLLOW_REQUEST_NOT_FOUND, 400);
        }

        followingChild.followers.splice(followerIndex, 1);
        followerChild.followings.splice(followingIndex, 1);

        const transactions = [];

        const followingChildUpdate = await ChildModel.transaction
            .update({ id: followingChildId }, { followers: followingChild.followers });
        transactions.push(followingChildUpdate);

        const followerChildUpdate = await ChildModel.transaction
            .update({ id: followerChildId }, { followings: followerChild.followings });
        transactions.push(followerChildUpdate);

        await dynamoose.transaction(transactions);

        req.query.childId = followingChildId;
        req.query.relationshipType = 'followers';
        return await this.getRelationships(req, loggedInUser, locale);
    }

    /**
     * @desc This function is being used to remove connection of a child by parent
     * @name removeConnection
     * <AUTHOR>
     * @since 22/01/2024
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
     * @returns {Object} The updated list of connections
     * @throws {GeneralError} Throws an error if the connection is not found
     */
    static async removeConnection (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale);
        Validator.validateConnectionRequest();

        const { requesterChildId, requestedChildId } = req.body;

        const { requesterChild, requestedChild } = await this.validateChildAssociationWithUserForConnection(
            loggedInUser, requesterChildId, requestedChildId, false, locale, false);

        const { connectionIndexInRequesterChild, connectionIndexInRequestedChild } = this.validateConnectionRequest(
            requesterChild, requestedChild, requestedChildId, CONSTANTS.CONNECTION_STATUS.DISCONNECTED);

        if (connectionIndexInRequesterChild === -1 || connectionIndexInRequestedChild === -1) {
            throw new GeneralError(MESSAGES.CONNECTION_REQUEST_NOT_FOUND, 400);
        }

        requesterChild.connections.splice(connectionIndexInRequesterChild, 1);
        requestedChild.connections.splice(connectionIndexInRequestedChild, 1);

        const transactions = [];

        const requesterChildUpdate = await ChildModel.transaction
            .update({ id: requesterChildId }, { connections: requesterChild.connections });
        transactions.push(requesterChildUpdate);

        const requestedChildUpdate = await ChildModel.transaction
            .update({ id: requestedChildId }, { connections: requestedChild.connections });
        transactions.push(requestedChildUpdate);

        await dynamoose.transaction(transactions);

        req.query.childId = requestedChildId;
        req.query.connectionType = CONSTANTS.CONNECTION_STATUS.CONNECTED;
        return await this.getChildConnections(req, loggedInUser, locale);
    }

    /**
     * @desc This function is being used to search child by parent within same school of parent's child
     * @name searchChild
     * <AUTHOR>
     * @since 22/12/2023
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
    */
    static async searchChild (req, loggedInUser, locale) {
        const { filteredChildren, child } = await this.searchChildInEntity(req, loggedInUser, locale);

        if (!filteredChildren.length) {
            return [];
        }

        return await this.formatChildren(filteredChildren, child);
    }

    /**
     * @desc This function is being used to search child by parent within same school of parent's child
     * @name searchChildInOrganization
     * <AUTHOR>
     * @since 16/01/2024
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
     * @returns {Array} List of children
     * @throws {GeneralError} Throws an error if the child is not found
    */
    static async searchChildInOrganization (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale, req.query);
        Validator.validateSearchChild();
        const { childId, searchValue } = req.query;
        if (loggedInUser.children.indexOf(childId) === -1) {
            throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'child'), 400);
        }
        const child = await ChildModel.get(childId);
        const opensearchFilteredChildren = await AwsOpenSearchService.searchChild(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, {
            searchValue, school: child.school
        });
        const formattedChildren = opensearchFilteredChildren.map(child => {
            const source = child._source;
            return {
                id: source.id,
                firstName: source.firstName,
                lastName: source.lastName,
                school: source.school,
                associatedOrganizations: source.associatedOrganizations,
                associatedColor: source.associatedColor,
                photoURL: source.photoURL ? source.photoURL : null,
                homeRoom: source.homeRoom
            };
        });
        const notOurChildren = formattedChildren.filter(child => !loggedInUser.children.includes(child.id));
        if (!notOurChildren.length) {
            return [];
        }

        return await this.formatChildrenWithStatus(notOurChildren, child);
    }

    /**
     * @desc This function is being used to search child by parent within same school of parent's child
     * @name searchChildInEntity
     * <AUTHOR>
     * @since 16/01/2024
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
     * @returns {Array} List of children
     * @throws {GeneralError} Throws an error if the child is not found
     */
    static async searchChildInEntity (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale, req.query);
        Validator.validateSearchChild();

        const { childId, searchValue } = req.query;

        if (loggedInUser.children.indexOf(childId) === -1) {
            throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'child'), 400);
        }

        const child = await ChildModel.get(childId);

        const childrenInSameSchool = await ChildOrganizationMappingModel
            .query('organizationId').eq(child.school).using('organizationId-index').exec();

        const childrenInSameSchoolIds = childrenInSameSchool
            .filter(child => !loggedInUser.children.includes(child.childId))
            .map(child => child.childId);

        if (!childrenInSameSchoolIds.length) {
            return { filteredChildren: [], child };
        }

        const children = await this.batchGetInChunks({
            ids: childrenInSameSchoolIds,
            model: ChildModel
        });

        const filteredChildren = children.filter(child => {
            const fullName = (`${child.firstName.toLowerCase()} ${child.lastName.toLowerCase()}`);
            const searchParts = searchValue.toLowerCase().split(' ').filter(part => part);
            return searchParts.every(part => fullName.includes(part));
        });

        return { filteredChildren, child };
    }

    /**
     * @desc Generic function to perform batchGet in chunks
     * <AUTHOR>
     * @since 25/07/2025
     * @param {Object} options
     * @param {Array} options.ids - List of IDs to fetch
     * @param {Object} options.model - Dynamoose model to use
     * @param {Array} [options.attributes] - Attributes to fetch (optional)
     * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET] - Max chunk size per request
     * @returns {Promise<Array>} Combined result of all batchGets
     */
    static async batchGetInChunks ({ ids, model, attributes, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET }) {
        if (!Array.isArray(ids)) {
            return [];
        }

        ids = ids.filter(Boolean);
        if (ids.length === 0) {
            return [];
        }

        const result = [];

        for (let i = 0; i < ids.length; i += chunkSize) {
            const chunk = ids.slice(i, i + chunkSize);
            const dataChunk = await model.batchGet(chunk, attributes ? { attributes } : undefined);
            result.push(...dataChunk);
        }

        return result;
    }

    /**
     * @desc This function is being used to format children data for response
     * @name formatChildren
     * <AUTHOR>
     * @since 22/12/2023
     * @param {Array} children Children
    */
    static async formatChildren (children, loggedInChild) {
        return await Promise.all(children.map(async child => {
            let status = 'Follow';
            if (loggedInChild) {
                loggedInChild.followings = loggedInChild.followings || [];
                const following = loggedInChild.followings.find(follow => follow.childId === child.id);
                if (following) {
                    if (following.status === CONSTANTS.FOLLOW_REQUEST_STATUS.REQUESTED) {
                        status = 'Request sent';
                    } else if (following.status === CONSTANTS.FOLLOW_REQUEST_STATUS.ACCEPTED) {
                        status = 'Following';
                    } else {
                        /** skipped status */
                    }
                }
            }
            return {
                id: child.id,
                firstName: child.firstName,
                lastName: child.lastName,
                school: await this.getOrganizationName(child.school),
                homeroom: child.homeRoom && await this.getOrganizationName(child.homeRoom),
                photoURL: child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null,
                associatedColor: child.associatedColor,
                status: loggedInChild ? status : undefined
            };
        }));
    }

    /**
     * @desc This function is being used to format children data for response
     * @name formatChildrenWithStatus
     * <AUTHOR>
     * @since 16/01/2024
     * @param {Array} children Children
     */
    static async formatChildrenWithStatus (children, loggedInChild) {
        return await Promise.all(children.map(async child => {
            let status = CONSTANTS.CHILD_CONNECTION_STATUS.NOT_CONNECTED;
            if (loggedInChild) {
                loggedInChild.connections = loggedInChild.connections || [];
                const connection = loggedInChild.connections.find(conn => conn.childId === child.id);
                if (connection) {
                    if (connection.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_BY) {
                        status = CONSTANTS.CHILD_CONNECTION_STATUS.ACCEPT_DENY;
                    } else if (connection.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_TO) {
                        status = CONSTANTS.CHILD_CONNECTION_STATUS.PENDING;
                    } else if (connection.status === CONSTANTS.CONNECTION_STATUS.CONNECTED) {
                        status = CONSTANTS.CHILD_CONNECTION_STATUS.CONNECTED;
                    } else {
                        /** updated status */
                    }
                }
            }
            return {
                id: child.id,
                firstName: child.firstName,
                lastName: child.lastName,
                school: await this.getOrganizationName(child.school),
                homeroom: child.homeRoom && await this.getOrganizationName(child.homeRoom),
                photoURL: child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null,
                associatedColor: child.associatedColor,
                status: loggedInChild ? status : undefined
            };
        }));
    }

    /**
     * @desc This function is being used to get organization name by organization id
     * @name getOrganizationName
     * <AUTHOR>
     * @since 22/12/2023
     * @param {String} organizationId Organization id
    */
    static async getOrganizationName (organizationId) {
        const organization = await OrganizationModel.get(organizationId, { attributes: ['name'] });
        return organization.name;
    }

    /**
     * @desc This function is being used to unfollow a child by parent
     * @name unfollowChild
     * <AUTHOR>
     * @since 26/12/2023
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
    */
    static async unfollowChild (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale);
        Validator.validateFollowRequest();

        const { followingChildId, followerChildId } = req.body;

        const { followingChild, followerChild } = await this.validateChildAssociationWithUser(
            loggedInUser, followerChildId, followingChildId, true, locale);

        const { followingIndex, followerIndex } = this.validateFollowRequest(
            followerChild, followingChild, followingChildId, CONSTANTS.FOLLOW_REQUEST_STATUS.UNFOLLOW);

        if (followingIndex === -1 || followerIndex === -1) {
            throw new GeneralError(MESSAGES.FOLLOW_REQUEST_NOT_FOUND, 400);
        }

        followingChild.followers.splice(followingIndex, 1);
        followerChild.followings.splice(followerIndex, 1);

        const transactions = [];

        const followingChildUpdate = await ChildModel.transaction
            .update({ id: followingChildId }, { followers: followingChild.followers });
        transactions.push(followingChildUpdate);

        const followerChildUpdate = await ChildModel.transaction
            .update({ id: followerChildId }, { followings: followerChild.followings });
        transactions.push(followerChildUpdate);

        await dynamoose.transaction(transactions);

        req.query.childId = followerChildId;
        req.query.relationshipType = 'followings';
        return await this.getRelationships(req, loggedInUser, locale);
    }

    /**
     * @desc This function is being used to get relationships of a child by parent
     * @name getRelationships
     * <AUTHOR>
     * @since 22/12/2023
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
    */
    static async getRelationships (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale, req.query);
        Validator.validateGetRelationships();

        const { childId, relationshipType } = req.query;

        if (loggedInUser.children.indexOf(childId) === -1) {
            throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'child'), 400);
        }

        const child = await ChildModel.get(childId);

        const relationships = this.getRelationshipList(relationshipType, child);
        const acceptedFollowersCount = child.followers.filter(follower => follower.status === 'accepted').length;
        const acceptedFollowingsCount = child.followings.filter(following => following.status === 'accepted').length;

        if (!relationships.length) {
            return {
                list: [],
                followersCount: acceptedFollowersCount,
                followingCount: acceptedFollowingsCount
            };
        }


        const children = await this.batchGetInChunks({
            ids: relationships,
            model: ChildModel
        });
        return {
            list: await this.formatChildren(children),
            followersCount: acceptedFollowersCount,
            followingCount: acceptedFollowingsCount
        };
    }

    /**
     * @desc This function is being used to get connections of a child by parent
     * @name getConnections
     * <AUTHOR>
     * @since 17/01/2024
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
     * @returns {Object} An object containing the list of connections and the count of accepted connections
     * @throws {GeneralError} Throws an error if the child is not found
     */
    static async getChildConnections (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale, req.query);
        Validator.validateGetChildConnections();

        const { childId, connectionType } = req.query;

        if (loggedInUser.children.indexOf(childId) === -1) {
            throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'child'), 400);
        }

        const child = await ChildModel.get(childId);

        const connections = this.getConnectionList(connectionType, child);
        const acceptedConnectionsCount = child.connections
            .filter(connection => connection.status === CONSTANTS.CONNECTION_STATUS.CONNECTED).length;

        if (!connections.length) {
            return {
                list: [],
                connectionsCount: acceptedConnectionsCount
            };
        }

        const children = await this.batchGetInChunks({
            ids: connections,
            model: ChildModel
        });
        return {
            list: await this.formatChildrenWithStatus(children),
            connectionsCount: acceptedConnectionsCount
        };
    }

    /**
    * Returns the appropriate relationship list based on the relationship type.
    * @param {string} relationshipType - The type of relationship ('followers', 'followings', 'requestedBy', 'requestedTo').
    * @returns {Array} The appropriate relationship list.
    */
    static getRelationshipList (relationshipType, child) {
        child.followers = child.followers || [];
        child.followings = child.followings || [];

        switch (relationshipType) {
            case 'followers':
                return child.followers
                    .filter(follower => follower.status === CONSTANTS.FOLLOW_REQUEST_STATUS.ACCEPTED)
                    .map(follower => follower.childId);
            case 'followings':
                return child.followings
                    .filter(following => following.status === CONSTANTS.FOLLOW_REQUEST_STATUS.ACCEPTED)
                    .map(following => following.childId);
            case 'requestedBy':
                return child.followers
                    .filter(follower => follower.status === CONSTANTS.FOLLOW_REQUEST_STATUS.REQUESTED)
                    .map(follower => follower.childId);
            case 'requestedTo':
                return child.followings
                    .filter(following => following.status === CONSTANTS.FOLLOW_REQUEST_STATUS.REQUESTED)
                    .map(following => following.childId);
            default:
                return [];
        }
    }

    /**
     * Returns the appropriate connection list based on the connection type.
     * @param {string} connectionType - The type of connection ('requestedBy', 'requestedTo', 'connected').
     * @param {Object} child - The child object.
     * @returns {Array} The appropriate connection list.
     */
    static getConnectionList (connectionType, child) {
        child.connections = child.connections || [];

        switch (connectionType) {
            case 'requestedBy':
                return child.connections
                    .filter(connection => connection.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_BY)
                    .map(connection => connection.childId);
            case 'requestedTo':
                return child.connections
                    .filter(connection => connection.status === CONSTANTS.CONNECTION_STATUS.REQUESTED_TO)
                    .map(connection => connection.childId);
            case 'connected':
                return child.connections
                    .filter(connection => connection.status === CONSTANTS.CONNECTION_STATUS.CONNECTED)
                    .map(connection => connection.childId);
            default:
                return [];
        }
    }

    static addFamilyMembershipToChildForNewOrg (user, associatedOrganizations, child) {
        const currentDate = MOMENT();

        if (!child.membershipsPurchased) {
            child.membershipsPurchased = [];
        }

        const validMemberships = user.membershipsPurchased?.filter(membership =>
            MOMENT(membership.endDate).isAfter(currentDate) && associatedOrganizations.includes(membership.organizationId)
        ) ?? [];

        const membershipsToAdd = validMemberships.filter(validMembership =>
            !child.membershipsPurchased.some(childMembership =>
                childMembership.organizationId === validMembership.organizationId &&
            childMembership.fundraiserSignupId === validMembership.fundraiserSignupId
            )
        );

        child.membershipsPurchased.push(...membershipsToAdd);
    }

    /**
     * @desc This function is being used to update child details by parent
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
    */
    static async updateChildDetails (req, loggedInUser, locale) {
        const Validator = new ChildValidator(req.body, locale, req.query, req.file);
        Validator.validateUpdateChild();
        const { childId, firstName, lastName, dob, schoolId, homeroomId, zipCode, associatedColor } = req.body;

        const child = await this.getChild(childId, loggedInUser, locale);
        const oldSchoolId = child.school;
        const { childOrganizationMapping, childPtoMapping } = await this.updateSchool(child, schoolId, childId);
        const transactionItems = [];

        childOrganizationMapping && transactionItems.push(ChildOrganizationMappingModel.transaction.update({
            childOrganizationMappingId: childOrganizationMapping[0].childOrganizationMappingId
        }, {
            ..._.omit(childOrganizationMapping[0],
                ['childOrganizationMappingId',
                    'updatedAt',
                    'createdAt'])
        } ));
        childPtoMapping && transactionItems.push(ChildOrganizationMappingModel.transaction.update({
            childOrganizationMappingId: childPtoMapping[0].childOrganizationMappingId
        }, {
            ..._.omit(childPtoMapping[0],
                ['childOrganizationMappingId',
                    'updatedAt',
                    'createdAt'])
        }));

        await this.handleHomeRoomUpdate(homeroomId, child, oldSchoolId, schoolId, childId, transactionItems);
        const fileName = await this.uploadProfilePicture(req, loggedInUser, childId);

        this.updateChildProperties(child, { firstName, lastName, dob, zipCode, associatedColor, fileName, loggedInUser });

        this.addFamilyMembershipToChildForNewOrg(loggedInUser, child.associatedOrganizations, child);

        transactionItems.push(ChildModel.transaction.update({
            id: child.id
        }, {
            ..._.omit(child,
                ['id',
                    'updatedAt',
                    'createdAt'])
        }));

        await dynamoose.transaction(transactionItems);

        const updatedChild = await this.getChild(childId, loggedInUser, locale);

        updatedChild.createdAt = MOMENT(updatedChild.createdAt).utc().toDate().getTime();
        updatedChild.updatedAt = MOMENT(updatedChild.updatedAt).utc().toDate().getTime();
        if (updatedChild.dob) {
            updatedChild.dob = MOMENT(updatedChild.dob).utc().toDate().getTime();
        }
        await AwsOpenSearchService.updateField(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, child.id, updatedChild);

        this.updateChildInOpenSearch(updatedChild);

        const { acceptedFollowersCount, acceptedFollowingsCount } = await this.countAcceptedFollowersFollowings(updatedChild);
        const { acceptedConnectionsCount } = await this.countAcceptedConnections(updatedChild);
        const { school, homeroom } = await this.getSchoolHomeroomDetails(updatedChild);

        const formattedResponse = {
            id: updatedChild.id,
            firstName: updatedChild.firstName,
            lastName: updatedChild.lastName,
            dob: updatedChild.dob,
            zipCode: updatedChild.zipCode,
            associatedColor: updatedChild.associatedColor,
            associatedOrganizations: updatedChild.associatedOrganizations,
            followersCount: acceptedFollowersCount,
            followingCount: acceptedFollowingsCount,
            connectionsCount: acceptedConnectionsCount,
            schoolId: updatedChild.school,
            schoolName: school.name,
            photoURL: updatedChild.photoURL ? await UploadService.getSignedUrl(updatedChild.photoURL) : null
        };

        if (updatedChild.homeRoom) {
            formattedResponse.homeroomId = updatedChild.homeRoom;
            formattedResponse.homeroomName = homeroom.name;
        }

        return formattedResponse;
    }

    static async handleHomeRoomUpdate (homeroomId, child, oldSchoolId, schoolId, childId, transactionItems) {
        if (homeroomId) {
            const { childHomeroomMapping, isUpdateHomeRoom } = await this.updateHomeroom(child, homeroomId, oldSchoolId, schoolId, childId);

            if (isUpdateHomeRoom) {
                childHomeroomMapping && transactionItems.push(ChildOrganizationMappingModel.transaction.update({
                    childOrganizationMappingId: childHomeroomMapping[0].childOrganizationMappingId
                }, {
                    ..._.omit(childHomeroomMapping[0],
                        ['childOrganizationMappingId',
                            'updatedAt',
                            'createdAt'])
                }));
            } else {
                childHomeroomMapping && transactionItems.push(ChildOrganizationMappingModel.transaction.create(
                    childHomeroomMapping[0]
                ));
            }
        } else {
            const homeRoomId = child.homeRoom;
            const childHomeroomMapping = homeRoomId && await ChildOrganizationMappingModel
                .query('organizationId').eq(homeRoomId).where('childId').eq(childId).exec();
            if (this.isChildHomeroomMapping(childHomeroomMapping)) {
                transactionItems.push(ChildOrganizationMappingModel.transaction.delete({
                    childOrganizationMappingId: childHomeroomMapping[0].childOrganizationMappingId
                }));
            }
            const index = child.associatedOrganizations.indexOf(homeRoomId);
            if (index !== -1) {
                child.associatedOrganizations.splice(index, 1);
            }
            child.homeRoom = undefined;
        }
    }

    static isChildHomeroomMapping (childHomeroomMapping) {
        return childHomeroomMapping && childHomeroomMapping.length;
    }

    static async updateChildInOpenSearch () {
        /**
         * commenting for now just to debug the issue
         * await AwsOpenSearchService.updateField(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, updatedChild.id, updatedChild);
         *
         * await AwsOpenSearchService.emptyChildCalendarEvents(CONSTANTS.OPEN_SEARCH.COLLECTION.CHILD, updatedChild.id);
         * await ParentService.setCalendarEventsInOpensearch(updatedChild);
         */
    }

    /**
     * Counts the number of accepted followers and followings for a child.
     * @param {Object} child - The child object.
     * @returns {Object} An object containing the counts of accepted followers and followings.
     */
    static async countAcceptedFollowersFollowings (child) {
        child.followers = child.followers || [];
        child.followings = child.followings || [];

        const acceptedFollowersCount = child.followers.filter(follower => follower.status === 'accepted').length;
        const acceptedFollowingsCount = child.followings.filter(following => following.status === 'accepted').length;

        return { acceptedFollowersCount, acceptedFollowingsCount };
    }

    /**
     * @desc This function is being used to count the number of connected children for a child
     * @name countAcceptedConnections
     * <AUTHOR>
     * @since 17/01/2024
     * @param {Object} child Child
     * @returns {Object} An object containing the count of accepted connections
     */
    static async countAcceptedConnections (child) {
        child.connections = child.connections || [];

        const acceptedConnectionsCount = child.connections
            .filter(connection => connection.status === CONSTANTS.CONNECTION_STATUS.CONNECTED).length;

        return { acceptedConnectionsCount };
    }

    /**
     * Gets the details of the school and homeroom for a child.
     * @param {Object} child - The child object.
     * @returns {Object} An object containing the school and homeroom details.
     */
    static async getSchoolHomeroomDetails (child) {
        const school = await OrganizationModel.get(child.school);
        const homeroom = child.homeRoom && await OrganizationModel.get(child.homeRoom);

        return { school, homeroom };
    }

    /**
     * @desc This function is being used to get child details
     * @param {String} childId Child ID
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
     * @returns {Object} Child
    */
    static async getChild (childId, loggedInUser, locale) {
        const child = await ChildModel.get({ id: childId });

        if (loggedInUser.children.indexOf(childId) === -1 || !child) {
            throw new GeneralError(locale(MESSAGES.CHILD_NOT_FOUND), 400);
        }

        return child;
    }

    /**
     * @desc This function is being used to update child's school
     * @param {Object} child Child
     * @param {String} schoolId School ID
     * @param {String} childId Child ID
     * @param {Object} locale Locale
    */
    static async updateSchool (child, schoolId, childId) {
        let childOrganizationMapping;
        let childPtoMapping;
        if (child.school !== schoolId) {
            const school = await OrganizationModel.get({ id: schoolId });
            if (!school || school.category !== CONSTANTS.CATEGORIES.SCHOOL) {
                throw new GeneralError(MESSAGES.SCHOOL_DOES_NOT_EXIST, 400);
            }

            childOrganizationMapping = await ChildOrganizationMappingModel
                .query('organizationId').eq(child.school).where('childId').eq(childId).exec();

            const oldSchoolPto = await OrganizationModel
                .query('parentOrganization').eq(child.school)
                .where('category').eq(CONSTANTS.CATEGORIES.PTO).exec();

            const newSchoolPto = await OrganizationModel
                .query('parentOrganization').eq(schoolId)
                .where('category').eq(CONSTANTS.CATEGORIES.PTO).exec();

            const oldSchoolPtoIndex = child.associatedOrganizations.indexOf(oldSchoolPto[0].id);
            if (oldSchoolPtoIndex !== -1) {
                child.associatedOrganizations[oldSchoolPtoIndex] = newSchoolPto[0].id;
            }

            childPtoMapping = await ChildOrganizationMappingModel
                .query('organizationId').eq(oldSchoolPto[0].id).where('childId').eq(childId).exec();

            childOrganizationMapping[0].organizationId = schoolId;
            childPtoMapping[0].organizationId = newSchoolPto[0].id;

            const index = child.associatedOrganizations.indexOf(child.school);
            if (index !== -1) {
                child.associatedOrganizations[index] = schoolId;
            }

            child.school = schoolId;
        }
        return { childOrganizationMapping, childPtoMapping };
    }

    /**
     * @desc This function is being used to update child's homeroom
     * @param {Object} child Child
     * @param {String} homeroomId Homeroom ID
     * @param {String} oldSchoolId Old School ID
     * @param {String} schoolId School ID
     * @param {String} childId Child ID
     * @param {Object} locale Locale
    */
    static async updateHomeroom (child, homeroomId, oldSchoolId, schoolId, childId) {
        let childHomeroomMapping;
        let isUpdateHomeRoom = false;
        if (child.homeRoom !== homeroomId) {
            const homeroom = await OrganizationModel.get({ id: homeroomId });
            const school = oldSchoolId !== schoolId ? schoolId : oldSchoolId;
            if (!homeroom || homeroom.category !== CONSTANTS.CATEGORIES.HOME_ROOM || homeroom.parentOrganization !== school) {
                throw new GeneralError(MESSAGES.HOMEROOM_DOES_NOT_EXIST, 400);
            }
            childHomeroomMapping = child.homeRoom && await ChildOrganizationMappingModel
                .query('organizationId').eq(child.homeRoom).where('childId').eq(childId).exec();
            if (childHomeroomMapping && childHomeroomMapping.length > 0) {
                isUpdateHomeRoom = true;
                childHomeroomMapping[0].organizationId = homeroomId;
            } else {
                childHomeroomMapping = [new ChildOrganizationMappingModel({
                    childId,
                    organizationId: homeroomId
                })];
            }
            const index = child.associatedOrganizations.indexOf(child.homeRoom);
            if (index !== -1) {
                child.associatedOrganizations[index] = homeroomId;
            } else {
                child.associatedOrganizations.push(homeroomId);
            }

            child.homeRoom = homeroomId;
        }
        return { childHomeroomMapping, isUpdateHomeRoom };
    }

    /**
     * @desc This function is being used to upload child's profile picture
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {String} childId Child ID
     * @returns {String} File name
    */
    static async uploadProfilePicture (req, loggedInUser, childId) {
        let fileName = req.body.fileName;

        if (req.file) {
            fileName = `${process.env.NODE_ENV}-profile-pictures/${loggedInUser.id}/${childId}`;
            await UploadService.uploadFile(req.file, fileName, true);
        }

        return fileName;
    }

    /**
     * @desc This function is being used to update child properties
     * @param {Object} child Child
     * @param {Object} properties Properties
    */
    static updateChildProperties (child, { firstName, lastName, dob, zipCode, associatedColor, fileName, loggedInUser }) {
        child.firstName = firstName;
        child.lastName = lastName;
        if (dob) {
            child.dob = MOMENT(dob, 'MM/DD/YYYY').utc().toDate();
        }
        child.zipCode = zipCode;
        child.associatedColor = associatedColor;
        child.photoURL = fileName ? fileName : child.photoURL;
        child.updatedBy = loggedInUser.id;
    }

    /**
     * @desc This function is being used to get associated organizations of a child
     * <AUTHOR>
     * @since 22/02/2024
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @param {Object} locale Locale
     * @returns {Array} List of associated organizations
    */
    static async getAssociatedOrganizations (req, loggedInUser, locale) {
        const { childId } = req.query;

        if (childId) {
            if (loggedInUser.children.indexOf(childId) === -1) {
                throw new GeneralError(locale(MESSAGES.NO_CHILD_FOUND_WITH_ID, 'child'), 400);
            }

            const child = await ChildModel.get(
                childId,
                { attributes: ['id', 'firstName', 'lastName', 'associatedColor',
                    'photoURL', 'associatedOrganizations', 'school', 'homeRoom'] }
            );

            let associatedOrganizations = await this.batchGetInChunks({
                ids: child.associatedOrganizations,
                model: OrganizationModel,
                attributes: ['id', 'name', 'zipCode', 'category', 'address', 'country', 'state', 'city', 'parentOrganization']
            });

            associatedOrganizations = associatedOrganizations.filter(org =>
                !CONSTANTS.ORGANIZATION_NOT_ALLOWED.includes(org.category)
            );

            const organizationMap = new Map(associatedOrganizations.map(org => [org.id, org]));

            const photoURL = child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null;
            const childData = {
                ...child,
                photoURL,
                associatedOrganizations: undefined,
                school: undefined,
                homeRoom: undefined
            };
            const organizations = Array.from(organizationMap.values());

            return { child: childData, organizations };
        } else {
            if (loggedInUser.children.length === 0) {
                return [];
            }
            const children = await this.batchGetInChunks({
                ids: loggedInUser.children,
                model: ChildModel,
                attributes: ['id', 'firstName', 'lastName', 'associatedColor',
                    'photoURL', 'associatedOrganizations', 'school', 'homeRoom']
            });
            const childOrgs = children.flatMap(child => child.associatedOrganizations);
            const uniqueChildOrgs = [...new Set(childOrgs)];

            let associatedOrganizations = await this.batchGetInChunks({
                ids: uniqueChildOrgs,
                model: OrganizationModel,
                attributes: ['id', 'name', 'zipCode', 'category', 'address', 'country', 'state', 'city', 'parentOrganization']
            });

            associatedOrganizations = associatedOrganizations.filter(org =>
                !CONSTANTS.ORGANIZATION_NOT_ALLOWED.includes(org.category)
            );

            return await Promise.all(children.map(async child => {
                const childOrganizationMap = new Map(associatedOrganizations
                    .filter(org => child.associatedOrganizations.includes(org.id))
                    .map(org => [org.id, { ...org }]));

                const photoURL = child.photoURL ? await UploadService.getSignedUrl(child.photoURL) : null;
                const childData = {
                    ...child,
                    photoURL,
                    associatedOrganizations: undefined,
                    school: undefined,
                    homeRoom: undefined
                };

                return { child: childData, organizations: Array.from(childOrganizationMap.values()) };
            }));
        }
    }

    /**
     * @desc This function used to generate presigned url for child
     * @name generatePresignedUrl
     * <AUTHOR>
     * @since 30/12/2024
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged in user
     * @returns {Object} Presigned url
    */
    static async generatePresignedUrl (req, loggedInUser) {
        const { childId } = req.query;
        const checkedChildId = childId ?? uuidv4();

        const generatedFileName = `${process.env.NODE_ENV}-profile-pictures/${loggedInUser.id}/${checkedChildId}`;

        const presignedUrl = await UploadService.getPreSignedUrlForUpload(
            generatedFileName
        );

        return {
            presignedUrl,
            fileName: generatedFileName,
            childId: checkedChildId
        };
    }
}

module.exports = ChildService;
