function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const eventDetails = stryMutAct_9fa48("239") ? {} : (stryCov_9fa48("239"), {
  'details': stryMutAct_9fa48("240") ? {} : (stryCov_9fa48("240"), {
    'type': String
  }),
  'startDateTime': stryMutAct_9fa48("241") ? {} : (stryCov_9fa48("241"), {
    'type': Date,
    'required': stryMutAct_9fa48("242") ? false : (stryCov_9fa48("242"), true)
  }),
  'endDateTime': stryMutAct_9fa48("243") ? {} : (stryCov_9fa48("243"), {
    'type': Date,
    'required': stryMutAct_9fa48("244") ? false : (stryCov_9fa48("244"), true)
  }),
  'venue': stryMutAct_9fa48("245") ? {} : (stryCov_9fa48("245"), {
    'type': String,
    'required': stryMutAct_9fa48("246") ? false : (stryCov_9fa48("246"), true)
  }),
  'isRecurring': stryMutAct_9fa48("247") ? {} : (stryCov_9fa48("247"), {
    'type': Boolean,
    'default': stryMutAct_9fa48("248") ? true : (stryCov_9fa48("248"), false)
  }),
  'recurringFrequency': stryMutAct_9fa48("249") ? {} : (stryCov_9fa48("249"), {
    'type': String
  }),
  'notes': stryMutAct_9fa48("250") ? {} : (stryCov_9fa48("250"), {
    'type': Set,
    'schema': stryMutAct_9fa48("251") ? [] : (stryCov_9fa48("251"), [String])
  }),
  'faq': stryMutAct_9fa48("252") ? {} : (stryCov_9fa48("252"), {
    'type': Array,
    'schema': stryMutAct_9fa48("253") ? [] : (stryCov_9fa48("253"), [stryMutAct_9fa48("254") ? {} : (stryCov_9fa48("254"), {
      'type': Object,
      'schema': stryMutAct_9fa48("255") ? {} : (stryCov_9fa48("255"), {
        'question': stryMutAct_9fa48("256") ? {} : (stryCov_9fa48("256"), {
          'type': String,
          'required': stryMutAct_9fa48("257") ? false : (stryCov_9fa48("257"), true)
        }),
        'answer': stryMutAct_9fa48("258") ? {} : (stryCov_9fa48("258"), {
          'type': String,
          'required': stryMutAct_9fa48("259") ? false : (stryCov_9fa48("259"), true)
        })
      })
    })]),
    'default': stryMutAct_9fa48("260") ? ["Stryker was here"] : (stryCov_9fa48("260"), [])
  }),
  'documentURLs': stryMutAct_9fa48("261") ? {} : (stryCov_9fa48("261"), {
    'type': Set,
    'schema': stryMutAct_9fa48("262") ? [] : (stryCov_9fa48("262"), [String])
  })
});
const comments = stryMutAct_9fa48("263") ? {} : (stryCov_9fa48("263"), {
  id: stryMutAct_9fa48("264") ? {} : (stryCov_9fa48("264"), {
    type: String,
    default: stryMutAct_9fa48("265") ? () => undefined : (stryCov_9fa48("265"), () => uuidv4())
  }),
  parentId: stryMutAct_9fa48("266") ? {} : (stryCov_9fa48("266"), {
    type: String,
    required: stryMutAct_9fa48("267") ? false : (stryCov_9fa48("267"), true)
  }),
  childId: stryMutAct_9fa48("268") ? {} : (stryCov_9fa48("268"), {
    type: String,
    required: stryMutAct_9fa48("269") ? false : (stryCov_9fa48("269"), true)
  }),
  message: stryMutAct_9fa48("270") ? {} : (stryCov_9fa48("270"), {
    type: String,
    required: stryMutAct_9fa48("271") ? false : (stryCov_9fa48("271"), true)
  }),
  isDeleted: stryMutAct_9fa48("272") ? {} : (stryCov_9fa48("272"), {
    type: Number,
    enum: stryMutAct_9fa48("273") ? [] : (stryCov_9fa48("273"), [0, 1]),
    default: 0
  })
});
const product = stryMutAct_9fa48("274") ? {} : (stryCov_9fa48("274"), {
  itemId: Number,
  options: stryMutAct_9fa48("275") ? {} : (stryCov_9fa48("275"), {
    type: Array,
    schema: stryMutAct_9fa48("276") ? [] : (stryCov_9fa48("276"), [stryMutAct_9fa48("277") ? {} : (stryCov_9fa48("277"), {
      type: Object,
      schema: stryMutAct_9fa48("278") ? {} : (stryCov_9fa48("278"), {
        optionId: Number,
        variants: stryMutAct_9fa48("279") ? {} : (stryCov_9fa48("279"), {
          type: Array,
          schema: stryMutAct_9fa48("280") ? [] : (stryCov_9fa48("280"), [String])
        }),
        name: String
      })
    })])
  }),
  optionPrices: stryMutAct_9fa48("281") ? {} : (stryCov_9fa48("281"), {
    type: Array,
    schema: stryMutAct_9fa48("282") ? [] : (stryCov_9fa48("282"), [stryMutAct_9fa48("283") ? {} : (stryCov_9fa48("283"), {
      type: Object,
      schema: stryMutAct_9fa48("284") ? {} : (stryCov_9fa48("284"), {
        itemName: String,
        id: String,
        itemCost: Number
      })
    })])
  }),
  images: stryMutAct_9fa48("285") ? {} : (stryCov_9fa48("285"), {
    type: Array,
    schema: stryMutAct_9fa48("286") ? [] : (stryCov_9fa48("286"), [String])
  }),
  itemName: String,
  imageCount: Number
});
const eventSchema = new dynamoose.Schema(stryMutAct_9fa48("287") ? {} : (stryCov_9fa48("287"), {
  id: stryMutAct_9fa48("288") ? {} : (stryCov_9fa48("288"), {
    hashKey: stryMutAct_9fa48("289") ? false : (stryCov_9fa48("289"), true),
    type: String,
    default: stryMutAct_9fa48("290") ? () => undefined : (stryCov_9fa48("290"), () => uuidv4())
  }),
  title: stryMutAct_9fa48("291") ? {} : (stryCov_9fa48("291"), {
    type: String,
    required: stryMutAct_9fa48("292") ? false : (stryCov_9fa48("292"), true)
  }),
  details: stryMutAct_9fa48("293") ? {} : (stryCov_9fa48("293"), {
    type: Object,
    schema: eventDetails,
    required: stryMutAct_9fa48("294") ? false : (stryCov_9fa48("294"), true)
  }),
  photoURL: stryMutAct_9fa48("295") ? {} : (stryCov_9fa48("295"), {
    type: String
  }),
  eventType: stryMutAct_9fa48("296") ? {} : (stryCov_9fa48("296"), {
    type: String,
    enum: stryMutAct_9fa48("297") ? [] : (stryCov_9fa48("297"), [stryMutAct_9fa48("298") ? "" : (stryCov_9fa48("298"), 'event'), stryMutAct_9fa48("299") ? "" : (stryCov_9fa48("299"), 'post'), stryMutAct_9fa48("300") ? "" : (stryCov_9fa48("300"), 'calendar')]),
    default: stryMutAct_9fa48("301") ? "" : (stryCov_9fa48("301"), 'event')
  }),
  eventScope: stryMutAct_9fa48("302") ? {} : (stryCov_9fa48("302"), {
    type: String,
    enum: stryMutAct_9fa48("303") ? [] : (stryCov_9fa48("303"), [stryMutAct_9fa48("304") ? "" : (stryCov_9fa48("304"), 'public'), stryMutAct_9fa48("305") ? "" : (stryCov_9fa48("305"), 'private')]),
    default: stryMutAct_9fa48("306") ? "" : (stryCov_9fa48("306"), 'public')
  }),
  participantsLimit: stryMutAct_9fa48("307") ? {} : (stryCov_9fa48("307"), {
    type: Number
  }),
  volunteerDetails: stryMutAct_9fa48("308") ? {} : (stryCov_9fa48("308"), {
    type: Set,
    schema: stryMutAct_9fa48("309") ? [] : (stryCov_9fa48("309"), [String])
  }),
  volunteerRequired: stryMutAct_9fa48("310") ? {} : (stryCov_9fa48("310"), {
    type: Boolean,
    required: stryMutAct_9fa48("311") ? false : (stryCov_9fa48("311"), true),
    default: stryMutAct_9fa48("312") ? true : (stryCov_9fa48("312"), false)
  }),
  volunteerSignupUrl: stryMutAct_9fa48("313") ? {} : (stryCov_9fa48("313"), {
    type: String
  }),
  isPaid: stryMutAct_9fa48("314") ? {} : (stryCov_9fa48("314"), {
    type: Boolean,
    required: stryMutAct_9fa48("315") ? false : (stryCov_9fa48("315"), true),
    default: stryMutAct_9fa48("316") ? true : (stryCov_9fa48("316"), false)
  }),
  isDonatable: stryMutAct_9fa48("317") ? {} : (stryCov_9fa48("317"), {
    type: Boolean,
    default: stryMutAct_9fa48("318") ? true : (stryCov_9fa48("318"), false)
  }),
  fee: stryMutAct_9fa48("319") ? {} : (stryCov_9fa48("319"), {
    type: Number
  }),
  status: stryMutAct_9fa48("320") ? {} : (stryCov_9fa48("320"), {
    type: String,
    enum: stryMutAct_9fa48("321") ? [] : (stryCov_9fa48("321"), [stryMutAct_9fa48("322") ? "" : (stryCov_9fa48("322"), 'published'), stryMutAct_9fa48("323") ? "" : (stryCov_9fa48("323"), 'draft'), stryMutAct_9fa48("324") ? "" : (stryCov_9fa48("324"), 'cancelled')]),
    default: stryMutAct_9fa48("325") ? "" : (stryCov_9fa48("325"), 'draft')
  }),
  comments: stryMutAct_9fa48("326") ? {} : (stryCov_9fa48("326"), {
    type: Array,
    schema: stryMutAct_9fa48("327") ? [] : (stryCov_9fa48("327"), [stryMutAct_9fa48("328") ? {} : (stryCov_9fa48("328"), {
      type: Object,
      schema: comments
    })]),
    default: stryMutAct_9fa48("329") ? ["Stryker was here"] : (stryCov_9fa48("329"), [])
  }),
  organizationId: stryMutAct_9fa48("330") ? {} : (stryCov_9fa48("330"), {
    type: String,
    required: stryMutAct_9fa48("331") ? false : (stryCov_9fa48("331"), true),
    index: stryMutAct_9fa48("332") ? {} : (stryCov_9fa48("332"), {
      name: stryMutAct_9fa48("333") ? "" : (stryCov_9fa48("333"), 'organizationId-index'),
      global: stryMutAct_9fa48("334") ? false : (stryCov_9fa48("334"), true),
      project: stryMutAct_9fa48("335") ? false : (stryCov_9fa48("335"), true)
    })
  }),
  quantityType: stryMutAct_9fa48("336") ? {} : (stryCov_9fa48("336"), {
    type: String,
    enum: stryMutAct_9fa48("337") ? [] : (stryCov_9fa48("337"), [stryMutAct_9fa48("338") ? "" : (stryCov_9fa48("338"), 'People'), stryMutAct_9fa48("339") ? "" : (stryCov_9fa48("339"), 'Items')]),
    default: null
  }),
  quantityInstruction: stryMutAct_9fa48("340") ? {} : (stryCov_9fa48("340"), {
    type: String,
    default: null
  }),
  membershipBenefitDetails: stryMutAct_9fa48("341") ? {} : (stryCov_9fa48("341"), {
    type: Object,
    schema: stryMutAct_9fa48("342") ? {} : (stryCov_9fa48("342"), {
      'benefitDiscount': stryMutAct_9fa48("343") ? {} : (stryCov_9fa48("343"), {
        'type': String
      }),
      'isOnlyForMembers': stryMutAct_9fa48("344") ? {} : (stryCov_9fa48("344"), {
        'type': Boolean
      })
    })
  }),
  products: stryMutAct_9fa48("345") ? {} : (stryCov_9fa48("345"), {
    type: Array,
    schema: stryMutAct_9fa48("346") ? [] : (stryCov_9fa48("346"), [stryMutAct_9fa48("347") ? {} : (stryCov_9fa48("347"), {
      type: Object,
      schema: product
    })])
  }),
  participantsCount: stryMutAct_9fa48("348") ? {} : (stryCov_9fa48("348"), {
    type: Number,
    default: 0
  }),
  createdBy: stryMutAct_9fa48("349") ? {} : (stryCov_9fa48("349"), {
    type: String,
    required: stryMutAct_9fa48("350") ? false : (stryCov_9fa48("350"), true)
  }),
  updatedBy: stryMutAct_9fa48("351") ? {} : (stryCov_9fa48("351"), {
    type: String
  }),
  isDeleted: stryMutAct_9fa48("352") ? {} : (stryCov_9fa48("352"), {
    type: Number,
    enum: stryMutAct_9fa48("353") ? [] : (stryCov_9fa48("353"), [0, 1]),
    default: 0
  })
}), stryMutAct_9fa48("354") ? {} : (stryCov_9fa48("354"), {
  timestamps: stryMutAct_9fa48("355") ? {} : (stryCov_9fa48("355"), {
    createdAt: stryMutAct_9fa48("356") ? "" : (stryCov_9fa48("356"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("357") ? "" : (stryCov_9fa48("357"), 'updatedAt')
  }),
  saveUnknown: stryMutAct_9fa48("358") ? [] : (stryCov_9fa48("358"), [stryMutAct_9fa48("359") ? "" : (stryCov_9fa48("359"), 'products.*.optionPrices.**')])
}));
module.exports = dynamoose.model(stryMutAct_9fa48("360") ? "" : (stryCov_9fa48("360"), 'Events'), eventSchema);