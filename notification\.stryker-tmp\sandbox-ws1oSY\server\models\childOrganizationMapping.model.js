// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const childOrganizationMappingSchema = new dynamoose.Schema(stryMutAct_9fa48("205") ? {} : (stryCov_9fa48("205"), {
  childOrganizationMappingId: stryMutAct_9fa48("206") ? {} : (stryCov_9fa48("206"), {
    hashKey: stryMutAct_9fa48("207") ? false : (stryCov_9fa48("207"), true),
    type: String,
    default: stryMutAct_9fa48("208") ? () => undefined : (stryCov_9fa48("208"), () => uuidv4())
  }),
  organizationId: stryMutAct_9fa48("209") ? {} : (stryCov_9fa48("209"), {
    type: String,
    index: stryMutAct_9fa48("210") ? {} : (stryCov_9fa48("210"), {
      global: stryMutAct_9fa48("211") ? false : (stryCov_9fa48("211"), true),
      name: stryMutAct_9fa48("212") ? "" : (stryCov_9fa48("212"), 'organizationId-index'),
      project: stryMutAct_9fa48("213") ? false : (stryCov_9fa48("213"), true)
    }),
    required: stryMutAct_9fa48("214") ? false : (stryCov_9fa48("214"), true)
  }),
  childId: stryMutAct_9fa48("215") ? {} : (stryCov_9fa48("215"), {
    type: String,
    required: stryMutAct_9fa48("216") ? false : (stryCov_9fa48("216"), true),
    index: stryMutAct_9fa48("217") ? {} : (stryCov_9fa48("217"), {
      global: stryMutAct_9fa48("218") ? false : (stryCov_9fa48("218"), true),
      name: stryMutAct_9fa48("219") ? "" : (stryCov_9fa48("219"), 'childId-index'),
      project: stryMutAct_9fa48("220") ? false : (stryCov_9fa48("220"), true)
    })
  })
}), stryMutAct_9fa48("221") ? {} : (stryCov_9fa48("221"), {
  timestamps: stryMutAct_9fa48("222") ? {} : (stryCov_9fa48("222"), {
    createdAt: stryMutAct_9fa48("223") ? "" : (stryCov_9fa48("223"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("224") ? "" : (stryCov_9fa48("224"), 'updatedAt')
  })
}));
module.exports = dynamoose.model(stryMutAct_9fa48("225") ? "" : (stryCov_9fa48("225"), 'ChildOrganizationMapping'), childOrganizationMappingSchema);