function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const Constants = require('../util/constants');
const {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand
} = require('@aws-sdk/client-s3');
const {
  getSignedUrl
} = require('@aws-sdk/s3-request-presigner');
const s3 = new S3Client(stryMutAct_9fa48("1938") ? {} : (stryCov_9fa48("1938"), {
  region: stryMutAct_9fa48("1939") ? "" : (stryCov_9fa48("1939"), 'us-east-2')
}));
class UploadService {
  /**
   * @desc This function is used to upload file to S3
   * <AUTHOR>
   * @param {Object} file file object
   * @param {String} filename file name in S3
   * @since 26/09/2023
   */
  static async uploadFile(file, filename) {
    if (stryMutAct_9fa48("1940")) {
      {}
    } else {
      stryCov_9fa48("1940");
      const data = stryMutAct_9fa48("1941") ? {} : (stryCov_9fa48("1941"), {
        Key: filename,
        Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
        Body: file.buffer,
        ContentType: file.mimetype,
        ContentEncoding: stryMutAct_9fa48("1942") ? "" : (stryCov_9fa48("1942"), 'base64')
      });
      if (stryMutAct_9fa48("1945") ? process.env.NODE_ENV === 'testing' : stryMutAct_9fa48("1944") ? false : stryMutAct_9fa48("1943") ? true : (stryCov_9fa48("1943", "1944", "1945"), process.env.NODE_ENV !== (stryMutAct_9fa48("1946") ? "" : (stryCov_9fa48("1946"), 'testing')))) {
        if (stryMutAct_9fa48("1947")) {
          {}
        } else {
          stryCov_9fa48("1947");
          const command = new PutObjectCommand(data);
          return s3.send(command);
        }
      } else {
        if (stryMutAct_9fa48("1948")) {
          {}
        } else {
          stryCov_9fa48("1948");
          return Promise.resolve();
        }
      }
    }
  }

  /**
   * @desc This function is used to delete file in S3
   * <AUTHOR>
   * @param {String} filename file name in S3
   * @since 26/09/2023
   */
  static async deleteObject(filename) {
    if (stryMutAct_9fa48("1949")) {
      {}
    } else {
      stryCov_9fa48("1949");
      const data = stryMutAct_9fa48("1950") ? {} : (stryCov_9fa48("1950"), {
        Key: filename,
        Bucket: Constants.AWS_S3_PUBLIC_BUCKET
      });
      if (stryMutAct_9fa48("1953") ? process.env.NODE_ENV === 'testing' : stryMutAct_9fa48("1952") ? false : stryMutAct_9fa48("1951") ? true : (stryCov_9fa48("1951", "1952", "1953"), process.env.NODE_ENV !== (stryMutAct_9fa48("1954") ? "" : (stryCov_9fa48("1954"), 'testing')))) {
        if (stryMutAct_9fa48("1955")) {
          {}
        } else {
          stryCov_9fa48("1955");
          const command = new DeleteObjectCommand(data);
          return s3.send(command);
        }
      } else {
        if (stryMutAct_9fa48("1956")) {
          {}
        } else {
          stryCov_9fa48("1956");
          return Promise.resolve();
        }
      }
    }
  }

  /**
   * @desc This function is used to get signed url of the S3 file
   * <AUTHOR>
   * @param {String} filename file name in S3
   * @since 26/09/2023
   */
  static async getSignedUrl(filename) {
    if (stryMutAct_9fa48("1957")) {
      {}
    } else {
      stryCov_9fa48("1957");
      if (stryMutAct_9fa48("1960") ? process.env.NODE_ENV === 'testing' : stryMutAct_9fa48("1959") ? false : stryMutAct_9fa48("1958") ? true : (stryCov_9fa48("1958", "1959", "1960"), process.env.NODE_ENV !== (stryMutAct_9fa48("1961") ? "" : (stryCov_9fa48("1961"), 'testing')))) {
        if (stryMutAct_9fa48("1962")) {
          {}
        } else {
          stryCov_9fa48("1962");
          const command = new GetObjectCommand(stryMutAct_9fa48("1963") ? {} : (stryCov_9fa48("1963"), {
            Bucket: Constants.AWS_S3_PUBLIC_BUCKET,
            Key: filename
          }));
          return getSignedUrl(s3, command, stryMutAct_9fa48("1964") ? {} : (stryCov_9fa48("1964"), {
            expiresIn: stryMutAct_9fa48("1965") ? 60 * 60 * 24 / 7 : (stryCov_9fa48("1965"), (stryMutAct_9fa48("1966") ? 60 * 60 / 24 : (stryCov_9fa48("1966"), (stryMutAct_9fa48("1967") ? 60 / 60 : (stryCov_9fa48("1967"), 60 * 60)) * 24)) * 7)
          }));
        }
      } else {
        if (stryMutAct_9fa48("1968")) {
          {}
        } else {
          stryCov_9fa48("1968");
          return Promise.resolve();
        }
      }
    }
  }
}
module.exports = UploadService;