function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
module.exports = stryMutAct_9fa48("1799") ? {} : (stryCov_9fa48("1799"), {
  100: stryMutAct_9fa48("1800") ? "" : (stryCov_9fa48("1800"), 'Continue'),
  101: stryMutAct_9fa48("1801") ? "" : (stryCov_9fa48("1801"), 'Switching Protocols'),
  200: stryMutAct_9fa48("1802") ? "" : (stryCov_9fa48("1802"), 'OK'),
  201: stryMutAct_9fa48("1803") ? "" : (stryCov_9fa48("1803"), 'Created'),
  202: stryMutAct_9fa48("1804") ? "" : (stryCov_9fa48("1804"), 'Accepted'),
  203: stryMutAct_9fa48("1805") ? "" : (stryCov_9fa48("1805"), 'Non-Authoritative Information'),
  204: stryMutAct_9fa48("1806") ? "" : (stryCov_9fa48("1806"), 'No Content'),
  205: stryMutAct_9fa48("1807") ? "" : (stryCov_9fa48("1807"), 'Reset Content'),
  206: stryMutAct_9fa48("1808") ? "" : (stryCov_9fa48("1808"), 'Partial Content'),
  207: stryMutAct_9fa48("1809") ? "" : (stryCov_9fa48("1809"), 'Multi Status'),
  208: stryMutAct_9fa48("1810") ? "" : (stryCov_9fa48("1810"), 'Already Reported'),
  226: stryMutAct_9fa48("1811") ? "" : (stryCov_9fa48("1811"), 'IM Used'),
  300: stryMutAct_9fa48("1812") ? "" : (stryCov_9fa48("1812"), 'Multiple Choices'),
  301: stryMutAct_9fa48("1813") ? "" : (stryCov_9fa48("1813"), 'Moved Permanently'),
  302: stryMutAct_9fa48("1814") ? "" : (stryCov_9fa48("1814"), 'Found'),
  303: stryMutAct_9fa48("1815") ? "" : (stryCov_9fa48("1815"), 'See Other'),
  304: stryMutAct_9fa48("1816") ? "" : (stryCov_9fa48("1816"), 'Not Modified'),
  305: stryMutAct_9fa48("1817") ? "" : (stryCov_9fa48("1817"), 'Use Proxy'),
  306: stryMutAct_9fa48("1818") ? "" : (stryCov_9fa48("1818"), 'Switch Proxy'),
  307: stryMutAct_9fa48("1819") ? "" : (stryCov_9fa48("1819"), 'Temporary Redirect'),
  308: stryMutAct_9fa48("1820") ? "" : (stryCov_9fa48("1820"), 'Permanent Redirect'),
  400: stryMutAct_9fa48("1821") ? "" : (stryCov_9fa48("1821"), 'Bad Request'),
  401: stryMutAct_9fa48("1822") ? "" : (stryCov_9fa48("1822"), 'Unauthorized'),
  402: stryMutAct_9fa48("1823") ? "" : (stryCov_9fa48("1823"), 'Payment Required'),
  403: stryMutAct_9fa48("1824") ? "" : (stryCov_9fa48("1824"), 'Forbidden'),
  404: stryMutAct_9fa48("1825") ? "" : (stryCov_9fa48("1825"), 'Not Found'),
  405: stryMutAct_9fa48("1826") ? "" : (stryCov_9fa48("1826"), 'Method Not Allowed'),
  406: stryMutAct_9fa48("1827") ? "" : (stryCov_9fa48("1827"), 'Not Acceptable'),
  407: stryMutAct_9fa48("1828") ? "" : (stryCov_9fa48("1828"), 'Proxy Authentication Required'),
  408: stryMutAct_9fa48("1829") ? "" : (stryCov_9fa48("1829"), 'Request Time-out'),
  409: stryMutAct_9fa48("1830") ? "" : (stryCov_9fa48("1830"), 'Conflict'),
  410: stryMutAct_9fa48("1831") ? "" : (stryCov_9fa48("1831"), 'Gone'),
  411: stryMutAct_9fa48("1832") ? "" : (stryCov_9fa48("1832"), 'Length Required'),
  412: stryMutAct_9fa48("1833") ? "" : (stryCov_9fa48("1833"), 'Precondition Failed'),
  413: stryMutAct_9fa48("1834") ? "" : (stryCov_9fa48("1834"), 'Request Entity Too Large'),
  414: stryMutAct_9fa48("1835") ? "" : (stryCov_9fa48("1835"), 'Request-URI Too Large'),
  415: stryMutAct_9fa48("1836") ? "" : (stryCov_9fa48("1836"), 'Unsupported Media Type'),
  416: stryMutAct_9fa48("1837") ? "" : (stryCov_9fa48("1837"), 'Requested Range not Satisfiable'),
  417: stryMutAct_9fa48("1838") ? "" : (stryCov_9fa48("1838"), 'Expectation Failed'),
  418: stryMutAct_9fa48("1839") ? "" : (stryCov_9fa48("1839"), 'I\'m a teapot'),
  421: stryMutAct_9fa48("1840") ? "" : (stryCov_9fa48("1840"), 'Misdirected Request'),
  422: stryMutAct_9fa48("1841") ? "" : (stryCov_9fa48("1841"), 'Unprocessable Entity'),
  423: stryMutAct_9fa48("1842") ? "" : (stryCov_9fa48("1842"), 'Locked'),
  424: stryMutAct_9fa48("1843") ? "" : (stryCov_9fa48("1843"), 'Failed Dependency'),
  426: stryMutAct_9fa48("1844") ? "" : (stryCov_9fa48("1844"), 'Upgrade Required'),
  428: stryMutAct_9fa48("1845") ? "" : (stryCov_9fa48("1845"), 'Precondition Required'),
  429: stryMutAct_9fa48("1846") ? "" : (stryCov_9fa48("1846"), 'Too Many Requests'),
  431: stryMutAct_9fa48("1847") ? "" : (stryCov_9fa48("1847"), 'Request Header Fields Too Large'),
  451: stryMutAct_9fa48("1848") ? "" : (stryCov_9fa48("1848"), 'Unavailable For Legal Reasons'),
  500: stryMutAct_9fa48("1849") ? "" : (stryCov_9fa48("1849"), 'Internal Server Error'),
  501: stryMutAct_9fa48("1850") ? "" : (stryCov_9fa48("1850"), 'Not Implemented'),
  502: stryMutAct_9fa48("1851") ? "" : (stryCov_9fa48("1851"), 'Bad Gateway'),
  503: stryMutAct_9fa48("1852") ? "" : (stryCov_9fa48("1852"), 'Service Unavailable'),
  504: stryMutAct_9fa48("1853") ? "" : (stryCov_9fa48("1853"), 'Gateway Time-out'),
  505: stryMutAct_9fa48("1854") ? "" : (stryCov_9fa48("1854"), 'HTTP Version not Supported'),
  506: stryMutAct_9fa48("1855") ? "" : (stryCov_9fa48("1855"), 'Variant Also Negotiates'),
  507: stryMutAct_9fa48("1856") ? "" : (stryCov_9fa48("1856"), 'Insufficient Storage'),
  508: stryMutAct_9fa48("1857") ? "" : (stryCov_9fa48("1857"), 'Loop Detected'),
  510: stryMutAct_9fa48("1858") ? "" : (stryCov_9fa48("1858"), 'Not Extended'),
  511: stryMutAct_9fa48("1859") ? "" : (stryCov_9fa48("1859"), 'Network Authentication Required'),
  CONTINUE: 100,
  SWITCHING_PROTOCOLS: 101,
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NON_AUTHORITATIVE_INFORMATION: 203,
  NO_CONTENT: 204,
  RESET_CONTENT: 205,
  PARTIAL_CONTENT: 206,
  MULTI_STATUS: 207,
  ALREADY_REPORTED: 208,
  IM_USED: 226,
  MULTIPLE_CHOICES: 300,
  MOVED_PERMANENTLY: 301,
  FOUND: 302,
  SEE_OTHER: 303,
  NOT_MODIFIED: 304,
  USE_PROXY: 305,
  SWITCH_PROXY: 306,
  TEMPORARY_REDIRECT: 307,
  PERMANENT_REDIRECT: 308,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  PAYMENT_REQUIRED: 402,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  NOT_ACCEPTABLE: 406,
  PROXY_AUTHENTICATION_REQUIRED: 407,
  REQUEST_TIMEOUT: 408,
  CONFLICT: 409,
  GONE: 410,
  LENGTH_REQUIRED: 411,
  PRECONDITION_FAILED: 412,
  REQUEST_ENTITY_TOO_LARGE: 413,
  REQUEST_URI_TOO_LONG: 414,
  UNSUPPORTED_MEDIA_TYPE: 415,
  REQUESTED_RANGE_NOT_SATISFIABLE: 416,
  EXPECTATION_FAILED: 417,
  IM_A_TEAPOT: 418,
  MISDIRECTED_REQUEST: 421,
  UNPROCESSABLE_ENTITY: 422,
  UPGRADE_REQUIRED: 426,
  PRECONDITION_REQUIRED: 428,
  LOCKED: 423,
  FAILED_DEPENDENCY: 424,
  TOO_MANY_REQUESTS: 429,
  REQUEST_HEADER_FIELDS_TOO_LARGE: 431,
  UNAVAILABLE_FOR_LEGAL_REASONS: 451,
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
  HTTP_VERSION_NOT_SUPPORTED: 505,
  VARIANT_ALSO_NEGOTIATES: 506,
  INSUFFICIENT_STORAGE: 507,
  LOOP_DETECTED: 508,
  NOT_EXTENDED: 510,
  NETWORK_AUTHENTICATION_REQUIRED: 511,
  ACCOUNT_SUSPENDED: 423
});