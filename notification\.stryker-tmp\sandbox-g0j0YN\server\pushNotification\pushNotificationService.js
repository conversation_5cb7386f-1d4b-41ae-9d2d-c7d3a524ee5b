/* eslint-disable max-len */function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const PendingPushNotification = require('../models/pendingPushNotification.model');
const MOMENT = require('moment');
const Notification = require('../util/notification');
const NotificationModel = require('../models/notification.model');
const ChildModel = require('../models/child.model');
const stringify = require('json-stringify-safe');
class PushNotificationService {
  static async pushNotfication() {
    if (stryMutAct_9fa48("907")) {
      {}
    } else {
      stryCov_9fa48("907");
      try {
        if (stryMutAct_9fa48("908")) {
          {}
        } else {
          stryCov_9fa48("908");
          const pendingNotification = await PendingPushNotification.scan().exec();
          CONSOLE_LOGGER.info(stryMutAct_9fa48("909") ? "" : (stryCov_9fa48("909"), 'Pending notification'), pendingNotification);
          if (stryMutAct_9fa48("913") ? pendingNotification.length <= 0 : stryMutAct_9fa48("912") ? pendingNotification.length >= 0 : stryMutAct_9fa48("911") ? false : stryMutAct_9fa48("910") ? true : (stryCov_9fa48("910", "911", "912", "913"), pendingNotification.length > 0)) {
            if (stryMutAct_9fa48("914")) {
              {}
            } else {
              stryCov_9fa48("914");
              for (const notification of pendingNotification) {
                if (stryMutAct_9fa48("915")) {
                  {}
                } else {
                  stryCov_9fa48("915");
                  const {
                    pushNotificationTime,
                    payload
                  } = notification;
                  const currentTime = MOMENT().utc();
                  CONSOLE_LOGGER.info(stryMutAct_9fa48("916") ? "" : (stryCov_9fa48("916"), 'Current time'), currentTime, stryMutAct_9fa48("917") ? "" : (stryCov_9fa48("917"), 'Push notification time'), pushNotificationTime, MOMENT(pushNotificationTime).isBefore(currentTime));
                  if (stryMutAct_9fa48("919") ? false : stryMutAct_9fa48("918") ? true : (stryCov_9fa48("918", "919"), MOMENT(pushNotificationTime).isBefore(currentTime))) {
                    if (stryMutAct_9fa48("920")) {
                      {}
                    } else {
                      stryCov_9fa48("920");
                      await this.sendEventNotification(notification, payload.triggerAt);
                      await PendingPushNotification.delete(stryMutAct_9fa48("921") ? {} : (stryCov_9fa48("921"), {
                        id: notification.id
                      }));
                      CONSOLE_LOGGER.info(stryMutAct_9fa48("922") ? "" : (stryCov_9fa48("922"), 'Notification sent and deleted from pending notification table'), notification.id);
                    }
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        if (stryMutAct_9fa48("923")) {
          {}
        } else {
          stryCov_9fa48("923");
          CONSOLE_LOGGER.error(error.message, stryMutAct_9fa48("924") ? "" : (stryCov_9fa48("924"), 'Error sending notification'));
        }
      }
    }
  }
  static async sendEventNotification(notification, hoursUntilNotification) {
    if (stryMutAct_9fa48("925")) {
      {}
    } else {
      stryCov_9fa48("925");
      const {
        notificationAction,
        associatedChildId,
        payload
      } = notification;
      const notificationMsg = await this.getNotificationMessage(payload, hoursUntilNotification);
      const child = await ChildModel.get(stryMutAct_9fa48("926") ? {} : (stryCov_9fa48("926"), {
        id: associatedChildId
      }));
      const notificationData = stryMutAct_9fa48("927") ? {} : (stryCov_9fa48("927"), {
        details: JSON.stringify(stryMutAct_9fa48("928") ? {} : (stryCov_9fa48("928"), {
          associatedChild: stryMutAct_9fa48("929") ? {} : (stryCov_9fa48("929"), {
            id: associatedChildId
          }),
          score: payload.score,
          id: payload.id,
          startDateTime: payload.startDateTime,
          endDateTime: payload.endDateTime
        })),
        child: JSON.stringify(stryMutAct_9fa48("930") ? {} : (stryCov_9fa48("930"), {
          id: child.id,
          firstName: child.firstName,
          lastName: child.lastName,
          photoURL: child.photoURL,
          associatedColor: child.associatedColor
        })),
        route: stryMutAct_9fa48("931") ? "" : (stryCov_9fa48("931"), 'eventDetails')
      });
      const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("932") ? {} : (stryCov_9fa48("932"), {
        title: stryMutAct_9fa48("933") ? "" : (stryCov_9fa48("933"), 'Reminder'),
        topic: stryMutAct_9fa48("934") ? `` : (stryCov_9fa48("934"), `childId-${associatedChildId}`),
        body: notificationMsg,
        data: notificationData,
        clickAction: notificationAction
      }));
      CONSOLE_LOGGER.info(stryMutAct_9fa48("935") ? "" : (stryCov_9fa48("935"), 'Sending notification for event signup with object: '), stringify(notificationObject));
      await Notification.sendNotification(notificationObject);
      await this.pushInNotificationTable(stryMutAct_9fa48("936") ? {} : (stryCov_9fa48("936"), {
        ...notification,
        title: stryMutAct_9fa48("937") ? "" : (stryCov_9fa48("937"), 'Reminder')
      }), notificationData, notificationMsg);
    }
  }
  static async pushInNotificationTable(notification, payload, description, trigger) {
    if (stryMutAct_9fa48("938")) {
      {}
    } else {
      stryCov_9fa48("938");
      CONSOLE_LOGGER.info(stryMutAct_9fa48("939") ? "" : (stryCov_9fa48("939"), 'Pushing notification in notification table'), notification, payload, description);
      const {
        associatedChildId,
        notificationAction,
        title
      } = notification;
      const child = await ChildModel.get(stryMutAct_9fa48("940") ? {} : (stryCov_9fa48("940"), {
        id: associatedChildId
      }));
      for (const guardian of child.guardians) {
        if (stryMutAct_9fa48("941")) {
          {}
        } else {
          stryCov_9fa48("941");
          const notificationObject = stryMutAct_9fa48("942") ? {} : (stryCov_9fa48("942"), {
            description,
            associatedChildId,
            notificationAction,
            payload,
            title,
            userId: guardian,
            readStatus: stryMutAct_9fa48("943") ? true : (stryCov_9fa48("943"), false)
          });
          await NotificationModel.create(notificationObject);
          if (stryMutAct_9fa48("946") ? trigger !== CONSTANTS.TRIGGER.POST_CREATED : stryMutAct_9fa48("945") ? false : stryMutAct_9fa48("944") ? true : (stryCov_9fa48("944", "945", "946"), trigger === CONSTANTS.TRIGGER.POST_CREATED)) {
            if (stryMutAct_9fa48("947")) {
              {}
            } else {
              stryCov_9fa48("947");
              const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("948") ? {} : (stryCov_9fa48("948"), {
                title,
                data: payload,
                body: description,
                topic: stryMutAct_9fa48("949") ? `` : (stryCov_9fa48("949"), `userId-${guardian}`),
                collapseKey: stryMutAct_9fa48("950") ? `` : (stryCov_9fa48("950"), `${guardian}-${trigger}`),
                clickAction: stryMutAct_9fa48("951") ? "" : (stryCov_9fa48("951"), 'vaalee://routeName=postDetails')
              }));
              await Notification.sendNotification(notificationObject);
            }
          }
        }
      }
    }
  }
  static async getNotificationMessage(payload, hoursUntilNotification) {
    if (stryMutAct_9fa48("952")) {
      {}
    } else {
      stryCov_9fa48("952");
      const {
        startDateTime,
        endDateTime,
        title
      } = payload;
      const start = MOMENT(startDateTime);
      const end = MOMENT(endDateTime);
      const isMultiDayEvent = stryMutAct_9fa48("953") ? start.isSame(end, 'day') : (stryCov_9fa48("953"), !start.isSame(end, stryMutAct_9fa48("954") ? "" : (stryCov_9fa48("954"), 'day')));
      let message = stryMutAct_9fa48("955") ? "Stryker was here!" : (stryCov_9fa48("955"), '');
      if (stryMutAct_9fa48("958") ? hoursUntilNotification !== '24' : stryMutAct_9fa48("957") ? false : stryMutAct_9fa48("956") ? true : (stryCov_9fa48("956", "957", "958"), hoursUntilNotification === (stryMutAct_9fa48("959") ? "" : (stryCov_9fa48("959"), '24')))) {
        if (stryMutAct_9fa48("960")) {
          {}
        } else {
          stryCov_9fa48("960");
          if (stryMutAct_9fa48("962") ? false : stryMutAct_9fa48("961") ? true : (stryCov_9fa48("961", "962"), isMultiDayEvent)) {
            if (stryMutAct_9fa48("963")) {
              {}
            } else {
              stryCov_9fa48("963");
              message = stryMutAct_9fa48("964") ? `` : (stryCov_9fa48("964"), `${title} begins in 24 hours! Get ready for an immersive multi-day experience!`);
            }
          } else {
            if (stryMutAct_9fa48("965")) {
              {}
            } else {
              stryCov_9fa48("965");
              message = stryMutAct_9fa48("966") ? `` : (stryCov_9fa48("966"), `${title} is starting in the next 24 hours! Don't miss out!`);
            }
          }
        }
      } else if (stryMutAct_9fa48("969") ? hoursUntilNotification !== '72' : stryMutAct_9fa48("968") ? false : stryMutAct_9fa48("967") ? true : (stryCov_9fa48("967", "968", "969"), hoursUntilNotification === (stryMutAct_9fa48("970") ? "" : (stryCov_9fa48("970"), '72')))) {
        if (stryMutAct_9fa48("971")) {
          {}
        } else {
          stryCov_9fa48("971");
          message = stryMutAct_9fa48("972") ? `` : (stryCov_9fa48("972"), `${title} starts in the next 3 days. Don't miss out!`);
        }
      }
      return message;
    }
  }
}
module.exports = PushNotificationService;