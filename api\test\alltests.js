const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'testing';
dotenv.config({ path: process.env.PWD + '/' + env + '.env' });
global.logger = require('../server/util/logger');
const chai = require('chai');
const chaiHttp = require('chai-http');
const app = require('../index');
chai.use(chaiHttp);
const request = require('supertest');
request(app);

// Start testing
require('./init.test');

// Auth
require('../server/services/logout/test/logout.test');

require('../server/services/userProfile/test/userProfile.test');
require('../server/services/userFeed/test/userFeed.test');

// Parent
require('../server/services/parent/test/parent.test');

// Payment
require('../server/services/payment/test/payment.test');

// Organization
require('../server/services/organization/test/organization.test');
require('../server/services/organization/test/organizationBulkService.test');

// Child
require('../server/services/child/test/child.test');

// utils test
require('../server/util/test/stripe.test');
require('../server/util/test/server.test');
require('../server/services/userProfile/test/ConversationService.test');

require('../server/services/organization/test/organization.unitTests');

describe('Stop server in end', () => {
    it('Server should stop manually to get code coverage', done => {
        app.close();
        done();
    });
});
