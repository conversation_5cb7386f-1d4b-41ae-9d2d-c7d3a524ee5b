function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const childSchema = new dynamoose.Schema(stryMutAct_9fa48("98") ? {} : (stryCov_9fa48("98"), {
  id: stryMutAct_9fa48("99") ? {} : (stryCov_9fa48("99"), {
    hashKey: stryMutAct_9fa48("100") ? false : (stryCov_9fa48("100"), true),
    type: String,
    default: stryMutAct_9fa48("101") ? () => undefined : (stryCov_9fa48("101"), () => uuidv4())
  }),
  firstName: stryMutAct_9fa48("102") ? {} : (stryCov_9fa48("102"), {
    type: String,
    required: stryMutAct_9fa48("103") ? false : (stryCov_9fa48("103"), true)
  }),
  lastName: stryMutAct_9fa48("104") ? {} : (stryCov_9fa48("104"), {
    type: String,
    required: stryMutAct_9fa48("105") ? false : (stryCov_9fa48("105"), true)
  }),
  dob: stryMutAct_9fa48("106") ? {} : (stryCov_9fa48("106"), {
    type: Date
  }),
  zipCode: stryMutAct_9fa48("107") ? {} : (stryCov_9fa48("107"), {
    type: String
  }),
  gender: stryMutAct_9fa48("108") ? {} : (stryCov_9fa48("108"), {
    type: String,
    enum: stryMutAct_9fa48("109") ? [] : (stryCov_9fa48("109"), [stryMutAct_9fa48("110") ? "" : (stryCov_9fa48("110"), 'boy'), stryMutAct_9fa48("111") ? "" : (stryCov_9fa48("111"), 'girl')])
  }),
  associatedColor: stryMutAct_9fa48("112") ? {} : (stryCov_9fa48("112"), {
    type: String,
    enum: stryMutAct_9fa48("113") ? [] : (stryCov_9fa48("113"), [stryMutAct_9fa48("114") ? "" : (stryCov_9fa48("114"), '#2772ED'), stryMutAct_9fa48("115") ? "" : (stryCov_9fa48("115"), '#FACD01'), stryMutAct_9fa48("116") ? "" : (stryCov_9fa48("116"), '#90C33A'), stryMutAct_9fa48("117") ? "" : (stryCov_9fa48("117"), '#FF82A9'), stryMutAct_9fa48("118") ? "" : (stryCov_9fa48("118"), '#CB66D9'), stryMutAct_9fa48("119") ? "" : (stryCov_9fa48("119"), '#FF8F7C')])
  }),
  photoURL: stryMutAct_9fa48("120") ? {} : (stryCov_9fa48("120"), {
    type: String
  }),
  associatedOrganizations: stryMutAct_9fa48("121") ? {} : (stryCov_9fa48("121"), {
    type: Array,
    schema: stryMutAct_9fa48("122") ? [] : (stryCov_9fa48("122"), [String]),
    default: stryMutAct_9fa48("123") ? ["Stryker was here"] : (stryCov_9fa48("123"), [])
  }),
  isDeleted: stryMutAct_9fa48("124") ? {} : (stryCov_9fa48("124"), {
    type: Number,
    enum: stryMutAct_9fa48("125") ? [] : (stryCov_9fa48("125"), [0, 1]),
    default: 0
  }),
  homeRoom: stryMutAct_9fa48("126") ? {} : (stryCov_9fa48("126"), {
    type: String
  }),
  school: stryMutAct_9fa48("127") ? {} : (stryCov_9fa48("127"), {
    type: String,
    index: stryMutAct_9fa48("128") ? {} : (stryCov_9fa48("128"), {
      name: stryMutAct_9fa48("129") ? "" : (stryCov_9fa48("129"), 'school-index'),
      global: stryMutAct_9fa48("130") ? false : (stryCov_9fa48("130"), true),
      project: stryMutAct_9fa48("131") ? false : (stryCov_9fa48("131"), true)
    })
  }),
  childEvents: stryMutAct_9fa48("132") ? {} : (stryCov_9fa48("132"), {
    type: Set,
    schema: stryMutAct_9fa48("133") ? [] : (stryCov_9fa48("133"), [String])
  }),
  guardians: stryMutAct_9fa48("134") ? {} : (stryCov_9fa48("134"), {
    type: Array,
    schema: stryMutAct_9fa48("135") ? [] : (stryCov_9fa48("135"), [String]),
    default: stryMutAct_9fa48("136") ? ["Stryker was here"] : (stryCov_9fa48("136"), [])
  }),
  followers: stryMutAct_9fa48("137") ? {} : (stryCov_9fa48("137"), {
    type: Array,
    schema: stryMutAct_9fa48("138") ? [] : (stryCov_9fa48("138"), [stryMutAct_9fa48("139") ? {} : (stryCov_9fa48("139"), {
      type: Object,
      schema: stryMutAct_9fa48("140") ? {} : (stryCov_9fa48("140"), {
        childId: stryMutAct_9fa48("141") ? {} : (stryCov_9fa48("141"), {
          type: String,
          required: stryMutAct_9fa48("142") ? false : (stryCov_9fa48("142"), true)
        }),
        status: stryMutAct_9fa48("143") ? {} : (stryCov_9fa48("143"), {
          type: String,
          enum: stryMutAct_9fa48("144") ? [] : (stryCov_9fa48("144"), [stryMutAct_9fa48("145") ? "" : (stryCov_9fa48("145"), 'requested'), stryMutAct_9fa48("146") ? "" : (stryCov_9fa48("146"), 'accepted'), stryMutAct_9fa48("147") ? "" : (stryCov_9fa48("147"), 'rejected')]),
          default: stryMutAct_9fa48("148") ? "" : (stryCov_9fa48("148"), 'requested')
        })
      })
    })])
  }),
  followings: stryMutAct_9fa48("149") ? {} : (stryCov_9fa48("149"), {
    type: Array,
    schema: stryMutAct_9fa48("150") ? [] : (stryCov_9fa48("150"), [stryMutAct_9fa48("151") ? {} : (stryCov_9fa48("151"), {
      type: Object,
      schema: stryMutAct_9fa48("152") ? {} : (stryCov_9fa48("152"), {
        childId: stryMutAct_9fa48("153") ? {} : (stryCov_9fa48("153"), {
          type: String,
          required: stryMutAct_9fa48("154") ? false : (stryCov_9fa48("154"), true)
        }),
        status: stryMutAct_9fa48("155") ? {} : (stryCov_9fa48("155"), {
          type: String,
          enum: stryMutAct_9fa48("156") ? [] : (stryCov_9fa48("156"), [stryMutAct_9fa48("157") ? "" : (stryCov_9fa48("157"), 'requested'), stryMutAct_9fa48("158") ? "" : (stryCov_9fa48("158"), 'accepted'), stryMutAct_9fa48("159") ? "" : (stryCov_9fa48("159"), 'rejected')]),
          default: stryMutAct_9fa48("160") ? "" : (stryCov_9fa48("160"), 'requested')
        })
      })
    })])
  }),
  connections: stryMutAct_9fa48("161") ? {} : (stryCov_9fa48("161"), {
    type: Array,
    schema: stryMutAct_9fa48("162") ? [] : (stryCov_9fa48("162"), [stryMutAct_9fa48("163") ? {} : (stryCov_9fa48("163"), {
      type: Object,
      schema: stryMutAct_9fa48("164") ? {} : (stryCov_9fa48("164"), {
        childId: stryMutAct_9fa48("165") ? {} : (stryCov_9fa48("165"), {
          type: String,
          required: stryMutAct_9fa48("166") ? false : (stryCov_9fa48("166"), true)
        }),
        status: stryMutAct_9fa48("167") ? {} : (stryCov_9fa48("167"), {
          type: String,
          enum: stryMutAct_9fa48("168") ? [] : (stryCov_9fa48("168"), [stryMutAct_9fa48("169") ? "" : (stryCov_9fa48("169"), 'requestedTo'), stryMutAct_9fa48("170") ? "" : (stryCov_9fa48("170"), 'requestedBy'), stryMutAct_9fa48("171") ? "" : (stryCov_9fa48("171"), 'connected')]),
          required: stryMutAct_9fa48("172") ? false : (stryCov_9fa48("172"), true)
        }),
        notificationIds: stryMutAct_9fa48("173") ? {} : (stryCov_9fa48("173"), {
          type: Array,
          schema: stryMutAct_9fa48("174") ? [] : (stryCov_9fa48("174"), [String]),
          default: stryMutAct_9fa48("175") ? ["Stryker was here"] : (stryCov_9fa48("175"), [])
        })
      })
    })]),
    default: stryMutAct_9fa48("176") ? ["Stryker was here"] : (stryCov_9fa48("176"), [])
  }),
  membershipsPurchased: stryMutAct_9fa48("177") ? {} : (stryCov_9fa48("177"), {
    type: Array,
    schema: stryMutAct_9fa48("178") ? [] : (stryCov_9fa48("178"), [stryMutAct_9fa48("179") ? {} : (stryCov_9fa48("179"), {
      type: Object,
      schema: stryMutAct_9fa48("180") ? {} : (stryCov_9fa48("180"), {
        organizationId: stryMutAct_9fa48("181") ? {} : (stryCov_9fa48("181"), {
          type: String,
          required: stryMutAct_9fa48("182") ? false : (stryCov_9fa48("182"), true)
        }),
        fundraiserSignupId: stryMutAct_9fa48("183") ? {} : (stryCov_9fa48("183"), {
          type: String,
          required: stryMutAct_9fa48("184") ? false : (stryCov_9fa48("184"), true)
        }),
        membershipType: stryMutAct_9fa48("185") ? {} : (stryCov_9fa48("185"), {
          type: String,
          required: stryMutAct_9fa48("186") ? false : (stryCov_9fa48("186"), true),
          enum: stryMutAct_9fa48("187") ? [] : (stryCov_9fa48("187"), [stryMutAct_9fa48("188") ? "" : (stryCov_9fa48("188"), 'child'), stryMutAct_9fa48("189") ? "" : (stryCov_9fa48("189"), 'family')])
        }),
        startDate: stryMutAct_9fa48("190") ? {} : (stryCov_9fa48("190"), {
          type: Date,
          required: stryMutAct_9fa48("191") ? false : (stryCov_9fa48("191"), true)
        }),
        membershipId: stryMutAct_9fa48("192") ? {} : (stryCov_9fa48("192"), {
          type: String,
          required: stryMutAct_9fa48("193") ? false : (stryCov_9fa48("193"), true)
        }),
        endDate: stryMutAct_9fa48("194") ? {} : (stryCov_9fa48("194"), {
          type: Date,
          required: stryMutAct_9fa48("195") ? false : (stryCov_9fa48("195"), true)
        })
      })
    })]),
    default: stryMutAct_9fa48("196") ? ["Stryker was here"] : (stryCov_9fa48("196"), [])
  }),
  isGuestChild: stryMutAct_9fa48("197") ? {} : (stryCov_9fa48("197"), {
    type: Boolean
  }),
  createdBy: stryMutAct_9fa48("198") ? {} : (stryCov_9fa48("198"), {
    type: String
  }),
  updatedBy: stryMutAct_9fa48("199") ? {} : (stryCov_9fa48("199"), {
    type: String
  })
}), stryMutAct_9fa48("200") ? {} : (stryCov_9fa48("200"), {
  timestamps: stryMutAct_9fa48("201") ? {} : (stryCov_9fa48("201"), {
    createdAt: stryMutAct_9fa48("202") ? "" : (stryCov_9fa48("202"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("203") ? "" : (stryCov_9fa48("203"), 'updatedAt')
  })
}));
module.exports = dynamoose.model(stryMutAct_9fa48("204") ? "" : (stryCov_9fa48("204"), 'Child'), childSchema);