/**
 * This file contains services for conversation.
 * Created by Growexx on 09/10/2024.
 * @name conversationService
 */

const GroupsModel = require('../../../models/groups.model');
const GroupMembersModel = require('../../../models/groupMembers.model');
const UserModel = require('../../../models/user.model');
const UploadService = require('../../../util/uploadService');
const ConversationValidator = require('./conversationValidator');
const GeneralError = require('../../../util/GeneralError');
const CONSTANTS = require('../../../util/constants');
/**
 * Class represents services for conversation.
 */
class ConversationService {
    /**
     * @desc This function is being used to get group conversation list
     * <AUTHOR>
     * @since 09/10/2024
     * @param {Object} req Request
     * @param {Object} user User
     * @returns {Promise<Object>} Group conversation list
     */
    static async getGroupConversationList (req, user) {
        let groupIds = [];
        let userParticipations = [];
        if (!req.query.organizationId) {
            return {
                statusCode: 400,
                message: 'Organization id is required'
            };
        }

        const organizationGroups = await GroupsModel.query('organizationId')
            .eq(req.query.organizationId)
            .using('organizationId-index')
            .where('status')
            .eq(CONSTANTS.GROUP_CONVERSATION_STATUS.ACTIVE)
            .exec();

        const organizationGroupIds = organizationGroups.map(group => group.groupId);
        userParticipations = await GroupMembersModel.query('userId')
            .eq(user.id)
            .where('status')
            .in([CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED])
            .where('groupId')
            .in(organizationGroupIds)
            .exec();

        groupIds = userParticipations.map(participation => participation.groupId);

        if (groupIds.length === 0) {
            return {
                organizationConversations: [],
                eventConversations: []
            };
        }

        const groupConversations = (
            await this.batchGetInChunks({
                ids: groupIds,
                model: GroupsModel
            })
        ).filter(
            conversation => conversation.status === CONSTANTS.GROUP_CONVERSATION_STATUS.ACTIVE
        );

        const groupConversationWithDetails = [];
        for (const groupMember of userParticipations) {
            const groupDetails = groupConversations.find(conversation => conversation.groupId === groupMember.groupId);
            if (groupDetails) {
                const isOrganizationGroup =
                    [CONSTANTS.GROUP_TYPES.ORGANIZATION, CONSTANTS.GROUP_TYPES.ORGANIZATION_ADMIN].includes(groupDetails.groupType);
                if (groupDetails.organizationMetaData?.logo) {
                    groupDetails.organizationMetaData.logo = await UploadService.getSignedUrl(groupDetails.organizationMetaData.logo);
                }
                if (groupDetails.eventMetaData?.image) {
                    groupDetails.eventMetaData.image = await UploadService.getSignedUrl(groupDetails.eventMetaData.image);
                }
                const membersCount = await GroupMembersModel.query('groupId')
                    .eq(groupMember.groupId)
                    .using('groupId-index')
                    .where('status')
                    .in([CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED])
                    .count()
                    .exec();
                groupConversationWithDetails.push({
                    groupId: groupMember.groupId,
                    groupType: groupDetails.groupType,
                    groupName: isOrganizationGroup
                        ? groupDetails.organizationMetaData?.name
                        : groupDetails.eventMetaData?.title,
                    groupImage: isOrganizationGroup
                        ? groupDetails.organizationMetaData?.logo
                        : groupDetails.eventMetaData?.image,
                    organizationId: groupDetails.organizationId,
                    eventId: groupDetails.eventId,
                    eventStartDate: groupDetails.eventMetaData?.startDateTime,
                    membersCount: membersCount.count
                });
            }
        }

        const organizationConversations = groupConversationWithDetails.filter(
            conversation =>
                [CONSTANTS.GROUP_TYPES.ORGANIZATION, CONSTANTS.GROUP_TYPES.ORGANIZATION_ADMIN].includes(conversation.groupType)
        );
        const eventConversations = groupConversationWithDetails.filter(
            conversation => [CONSTANTS.GROUP_TYPES.EVENT].includes(conversation.groupType)
        );

        return {
            organizationConversations,
            eventConversations
        };
    }

    /**
      * @desc Generic function to perform batchGet in chunks
      * <AUTHOR>
      * @since 25/07/2025
      * @param {Object} options
      * @param {Array} options.ids - List of IDs to fetch
      * @param {Object} options.model - Dynamoose model to use
      * @param {Array} [options.attributes] - Attributes to fetch (optional)
      * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET] - Max chunk size per request
      * @returns {Promise<Array>} Combined result of all batchGets
    */
    static async batchGetInChunks ({ ids, model, attributes, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET }) {
        if (!Array.isArray(ids)) {
            return [];
        }
        ids = ids.filter(Boolean);
        if (ids.length === 0) {
            return [];
        }

        const result = [];

        for (let i = 0; i < ids.length; i += chunkSize) {
            const chunk = ids.slice(i, i + chunkSize);
            const dataChunk = await model.batchGet(chunk, attributes ? { attributes } : undefined);
            result.push(...dataChunk);
        }

        return result;
    }

    /**
     * @desc This function is being used to get group members
     * <AUTHOR>
     * @since 14/10/2024
     * @param {Object} req Request
     * @param {Object} user User
     * @returns {Promise<Object>} Group members
     */
    static async getGroupMembers (req, user, locale) {
        const { groupId } = req.query;

        const validator = new ConversationValidator(req.body, locale, req.query);
        validator.validateGetConversationMembers();

        const groupMembers = await GroupMembersModel.query('groupId').eq(groupId).using('groupId-index')
            .where('status').in([CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED]).exec();

        if (groupMembers.length === 0) {
            throw new GeneralError(locale(MESSAGES.GROUP_NOT_FOUND), 400);
        }

        const uniqueUserIds = [...new Set(groupMembers.map(member => member.userId))];

        if (!uniqueUserIds.includes(user.id)) {
            throw new GeneralError(locale(MESSAGES.USER_NOT_PART_OF_GROUP), 400);
        }

        const users = [];
        await Promise.all(uniqueUserIds.map(async (userId) => {
            const user = await UserModel.query('id').eq(userId)
                .attributes(['id', 'firstName', 'lastName', 'associatedOrganizations']).exec();
            if (user.length > 0) {
                users.push(user[0]);
            }
        }));
        const userMap = new Map(users.map(user => [user.id, user]));

        groupMembers.forEach(member => {
            const user = userMap.get(member.userId);
            member.userName = user?.firstName + ' ' + user?.lastName;
            member.role = user?.associatedOrganizations?.find(organization => organization.id === member.organizationId)?.role;
        });

        groupMembers.sort((a, b) => {
            const roleOrder = {
                [CONSTANTS.ORGANIZATION_ROLE.SUPER_ADMIN]: 0,
                [CONSTANTS.ORGANIZATION_ROLE.ADMIN]: 1,
                [CONSTANTS.ORGANIZATION_ROLE.EDITOR]: 2
            };
            return (roleOrder[a.role] ?? 3) - (roleOrder[b.role] ?? 3);
        });

        return groupMembers;
    }

    /**
     * @desc This function is being used to generate presigned url
     * <AUTHOR>
     * @since 20/12/2024
     * @param {Object} req Request
     * @param {Object} locale Locale
     * @returns {Promise<Object>} Presigned url
     */
    static async generatePresignedUrl (req, locale) {
        const Validator = new ConversationValidator(req.body, locale, req.query);
        Validator.validateGeneratePresignedUrl();
        const { isGroupMessage, conversationId, messageId, isThumbnail, organizationId, groupId } = req.query;
        const parsedIsGroupMessage = isGroupMessage === 'true';
        const parsedIsThumbnail = isThumbnail === 'true';

        const generatedThumbnailFileName =
            parsedIsGroupMessage
                ? `${process.env.NODE_ENV}-message-media-thumbnail/${organizationId}/${groupId}/${messageId}`
                : `${process.env.NODE_ENV}-personal-message-media-thumbnail/${conversationId}/${messageId}`;

        const generatedFileName =
            parsedIsGroupMessage
                ? `${process.env.NODE_ENV}-message-media/${organizationId}/${groupId}/${messageId}`
                : `${process.env.NODE_ENV}-personal-message-media/${conversationId}/${messageId}`;

        const presignedUrl = await UploadService.getPreSignedUrlForUpload(
            parsedIsThumbnail ? generatedThumbnailFileName : generatedFileName
        );
        return {
            presignedUrl,
            fileName: parsedIsThumbnail ? generatedThumbnailFileName : generatedFileName
        };
    }
}

module.exports = ConversationService;
