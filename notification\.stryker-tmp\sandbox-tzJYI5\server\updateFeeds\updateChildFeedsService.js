/* eslint-disable max-len */
// @ts-nocheck

const MOMENT = require('moment');
const Redis = require('ioredis');
const stringify = require('json-stringify-safe');
const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
const CONSTANTS = require('../util/constants');

const { Client } = require('@opensearch-project/opensearch');
const ConstantModel = require('../models/constant.model');
const Utils = require('../util/utilFunctions');

let client;

if (process.env.NODE_ENV !== 'testing') {
    client = new Client({
        node: process.env.OPENSEARCH_ENDPOINT,
        auth: {
            username: process.env.OPENSEARCH_USERNAME,
            password: process.env.OPENSEARCH_PASSWORD
        },
        ssl: { rejectUnauthorized: false }
    });
}

class UpdateFeedsService {
    static async updateChildFeeds () {
        try {
            const versionPrefixFromDb = await ConstantModel.get(CONSTANTS.FEED_VERSION_PREFIX);
            const versionPrefix = versionPrefixFromDb?.value ?? '';

            const expirationDays = await this.getPostExpirationDays();
            CONSOLE_LOGGER.info(`Expiration days for posts: ${expirationDays}`);

            const initialMemoryUsage = await this.getMemoryUsage();
            CONSOLE_LOGGER.info(`Initial Redis memory usage: ${initialMemoryUsage}`);

            const eventDetailsKey = Utils.getEventDetailsKey({ versionPrefix, shouldGenerateKeyPrefix: true });
            CONSOLE_LOGGER.time(`get: ${eventDetailsKey}:*`);
            const eventHashMapsOfRedis = await this.getEventHashMapsOfRedis(`${eventDetailsKey}:*`);
            CONSOLE_LOGGER.timeEnd(`get: ${eventDetailsKey}:*`);

            const fundraiserDetailsKey = Utils.getFundraiserDetailsKey({ versionPrefix, shouldGenerateKeyPrefix: true });
            CONSOLE_LOGGER.time(`get: ${fundraiserDetailsKey}:*`);
            const fundraiserHashMapOfRedis = await this.getEventHashMapsOfRedis(`${fundraiserDetailsKey}:*`);
            CONSOLE_LOGGER.timeEnd(`get: ${fundraiserDetailsKey}:*`);

            const postDetailsKey = Utils.getPostDetailsKey({ versionPrefix, shouldGenerateKeyPrefix: true });
            CONSOLE_LOGGER.time(`get: ${postDetailsKey}:*`);
            const postHashMapOfRedis = await this.getEventHashMapsOfRedis(`${postDetailsKey}:*`);
            CONSOLE_LOGGER.timeEnd(`get: ${postDetailsKey}:*`);

            const childEventsKey = Utils.getChildKey({ versionPrefix, shouldGenerateKeyPrefix: true });
            CONSOLE_LOGGER.time(`get: ${childEventsKey}:*`);
            const childEventsOfRedis = await this.getEventHashMapsOfRedis(`${childEventsKey}:*`);
            CONSOLE_LOGGER.timeEnd(`get: ${childEventsKey}:*`);

            const userEventsKey = Utils.getUserKey({ versionPrefix, shouldGenerateKeyPrefix: true });
            CONSOLE_LOGGER.time(`get: ${userEventsKey}:*`);
            const userEventsOfRedis = await this.getEventHashMapsOfRedis(`${userEventsKey}:*`);
            CONSOLE_LOGGER.timeEnd(`get: ${userEventsKey}:*`);

            const combinedKeysForFeedsReferences = [
                ...childEventsOfRedis,
                ...userEventsOfRedis
            ];

            const eventMap = new Set();
            const currentTime = MOMENT().utc();
            const pipeline = redis.pipeline();

            const allHashMapsOfRedis = [
                ...eventHashMapsOfRedis,
                ...fundraiserHashMapOfRedis,
                ...postHashMapOfRedis
            ];
            CONSOLE_LOGGER.time('creating eventMap');
            for (const key of allHashMapsOfRedis) {
                const eventData = await redis.hget(key, 'details');
                const event = JSON.parse(eventData);
                if (MOMENT(event.endDateTime).isValid() && MOMENT(event.endDateTime).isBefore(currentTime) && event.eventType === CONSTANTS.EVENT_TYPES.EVENT) {
                    eventMap.add(key.split(':').pop());
                }
                if (MOMENT(event.endDate).isValid() && MOMENT(event.endDate).isBefore(currentTime) && event.isFundraiser === true) {
                    eventMap.add(key.split(':').pop());
                }
                if (MOMENT(event.publishedDate).isValid() && MOMENT(event.publishedDate).add(expirationDays, 'days').isBefore(currentTime) && event.isPost === true) {
                    eventMap.add(key.split(':').pop());
                }
            }

            CONSOLE_LOGGER.timeEnd('creating eventMap');
            CONSOLE_LOGGER.time('Remove event from the child feeds');
            for (const key of combinedKeysForFeedsReferences) {
                const isChildFeed = key.includes('child-events');
                const feedsReferences = await redis.zrevrangebyscore(key, '+inf', '-inf');

                for (const feedReference of feedsReferences) {
                    const event = JSON.parse(feedReference);

                    if (eventMap.has(event.postId)) {
                        pipeline.zrem(key, feedReference);
                        CONSOLE_LOGGER.info(`Post Feed removed from key: ${key}`, event);
                        if (isChildFeed) {
                            try {
                                await client.update({
                                    index: 'children',
                                    id: event.childId,
                                    body: {
                                        script: {
                                            source: 'ctx._source.childPosts.removeIf(item -> item == params.tag)',
                                            lang: 'painless',
                                            params: {
                                                tag: event.postId
                                            }
                                        }
                                    }
                                });
                            } catch (error) {
                                CONSOLE_LOGGER.error(`Error updating childPosts for child: ${event.childId}`, error);
                            }
                        }
                    } else if (eventMap.has(event.eventId)) {
                        pipeline.zrem(key, feedReference);
                        CONSOLE_LOGGER.info(`Feed removed from key: ${key}`, event);

                        if (isChildFeed) {
                            try {
                                await client.update({
                                    index: 'children',
                                    id: event.childId,
                                    body: {
                                        script: {
                                            source: 'ctx._source.childFeeds.removeIf(item -> item == params.tag)',
                                            lang: 'painless',
                                            params: {
                                                tag: event.eventId
                                            }
                                        }
                                    }
                                });
                            } catch (error) {
                                CONSOLE_LOGGER.error(`Error updating childFeeds for child: ${event.childId}`, error);
                            }
                        }
                    } else if (eventMap.has(event.fundraiserId)) {
                        pipeline.zrem(key, feedReference);
                        CONSOLE_LOGGER.info(`Fundraiser feed removed from key: ${key}`, event);

                        if (isChildFeed) {
                            try {
                                await client.update({
                                    index: 'children',
                                    id: event.childId,
                                    body: {
                                        script: {
                                            source: 'ctx._source.childFundraiserFeeds.removeIf(item -> item == params.tag)',
                                            lang: 'painless',
                                            params: {
                                                tag: event.fundraiserId
                                            }
                                        }
                                    }
                                });
                            } catch (error) {
                                CONSOLE_LOGGER.error(`Error updating childFundraiserFeeds for child: ${event.childId}`, error);
                            }
                        }
                    }
                }
            }
            await pipeline.exec();
            CONSOLE_LOGGER.timeEnd('Remove event from the child feeds');

            const finalMemoryUsage = await this.getMemoryUsage();
            CONSOLE_LOGGER.info(`Final Redis memory usage: ${finalMemoryUsage}`);
        } catch (error) {
            CONSOLE_LOGGER.error(stringify(error), 'Error updating child feeds');
        }
    }

    static async getEventHashMapsOfRedis (key) {
        return await redis.keys(key);
    }

    static async getMemoryUsage () {
        return await redis.info('memory');
    }

    static async getPostExpirationDays () {
        try {
            const postExpirationConstant = await ConstantModel.get(CONSTANTS.POST_EXPIRATION_DAYS);
            const expirationDays = parseInt(postExpirationConstant?.value, 10);

            if (isNaN(expirationDays) || expirationDays <= 0) {
                throw new Error('Invalid expiration days value');
            }

            return expirationDays;
        } catch (error) {
            CONSOLE_LOGGER.info('Error retrieving post expiration days constant:', error.message);
            return 15;
        }
    }
}

module.exports = UpdateFeedsService;
