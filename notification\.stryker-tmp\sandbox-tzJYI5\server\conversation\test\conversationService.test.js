// @ts-nocheck
const sinon = require('sinon');
const CONSTANTS = require('../../util/constants');
const ConversationService = require('../ConversationService');
const Groups = require('../../models/groups.model');
const MOMENT = require('moment');

describe('ConversationService', () => {
    it('should update conversation groups', async () => {
        sinon.stub(Groups, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([
                                {
                                    groupId: '1',
                                    groupType: CONSTANTS.GROUP_CONVERSATION_TYPES.EVENT,
                                    eventMetaData: {
                                        endDateTime: MOMENT().subtract(1, 'day').toISOString()
                                    }
                                },
                                {
                                    groupId: '2',
                                    groupType: CONSTANTS.GROUP_CONVERSATION_TYPES.EVENT,
                                    eventMetaData: {
                                        endDateTime: MOMENT().subtract(4, 'day').toISOString()
                                    }
                                }
                            ])
                        })
                    })
                })
            })
        });

        sinon.stub(Groups, 'batchPut').resolves();

        await ConversationService.updateConversationGroups();

        sinon.assert.calledOnce(Groups.batchPut);

        Groups.query.restore();
        Groups.batchPut.restore();
    });

    it('should not update conversation groups if no expired groups', async () => {
        sinon.stub(Groups, 'query').returns({
            eq: sinon.stub().returns({
                using: sinon.stub().returns({
                    where: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([])
                        })
                    })
                })
            })
        });

        await ConversationService.updateConversationGroups();

        sinon.stub(Groups, 'batchPut').resolves();

        sinon.assert.notCalled(Groups.batchPut);

        Groups.query.restore();
        Groups.batchPut.restore();
    });

    it('should handle error when updating conversation groups', async () => {
        sinon.stub(Groups, 'query').throws(new Error('Database error'));

        await ConversationService.updateConversationGroups();

        sinon.stub(Groups, 'batchPut').resolves();
        sinon.assert.notCalled(Groups.batchPut);

        Groups.query.restore();
        Groups.batchPut.restore();
    });
});
