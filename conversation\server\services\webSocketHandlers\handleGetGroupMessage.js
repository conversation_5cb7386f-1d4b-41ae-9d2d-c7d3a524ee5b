const Message = require('../../models/message.model');
const UploadService = require('../../util/uploadService');
const GroupMember = require('../../models/groupMembers.model');
const User = require('../../models/user.model');
const Children = require('../../models/child.model');
const Organization = require('../../models/organization.model');
const EnrichmentService = require('../enrichmentService');
const MessageReactionsHandler = require('./handlers/messageReactionsHandler');
const CONSTANTS = require('../../util/constants');
const { filterMessagesFromDeletedMessage, getCalculatedLastEvaluatedKey } = require('./conversationUtils');
const RedisUtil = require('../../util/redisUtil');
const Utils = require('../../util/utilFunctions');
const ConstantModel = require('../../models/constant.model');

module.exports = async (event) => {
    switch (event.body.actionType) {
        case CONSTANTS.ACTION_TYPES.GET_GROUP_MESSAGES.LAZY_LOAD_GROUP_MESSAGES:
            return await getLazyLoadGroupMessages(event);
        case CONSTANTS.ACTION_TYPES.GET_GROUP_MESSAGES.GET_GROUP_LIST_AND_MEMBERS:
            return await getGroupsMembersAndMessages(event);
        case CONSTANTS.ACTION_TYPES.GET_GROUP_MESSAGES.GET_USER_CHILDREN_LIST:
            return await getUserChildrenList(event);
        case CONSTANTS.ACTION_TYPES.GET_GROUP_MESSAGES.GET_GROUP_MEMBERS_AND_MESSAGES:
            return await getGroupMembersAndMessages(event);
        default:
            return {
                statusCode: 400,
                body: 'Invalid action type'
            };
    }
};

/**
* @description Gets the version prefix for redis keys
* <AUTHOR>
* @returns {Promise<String>} The version prefix
*/
const getVersionPrefixForRedisKeys = async () => {
    const versionPrefixConstant = await ConstantModel.get(
        CONSTANTS.FEED_VERSION_PREFIX
    );
    return versionPrefixConstant?.value ?? '';
};

async function getLazyLoadGroupMessages (event) {
    try {
        const groupId = event.body.groupId;
        const lastEvaluatedKey = event.body.lastEvaluatedKey;
        const clientLastSyncedMessage = event.body.clientLastSyncedMessage;
        const limit = event.body.limit;
        const result = await syncMessages(groupId, lastEvaluatedKey, clientLastSyncedMessage, limit);
        const processedResult = { ...result };
        const messagesWithSignedUrlPromise = processedResult.messages.map(
            async (message) => await EnrichmentService.enrichMessageWithMediaUrls(message)
        );
        const messagesWithSignedUrl = await Promise.all(messagesWithSignedUrlPromise);
        processedResult.groupId = groupId;
        processedResult.messages = messagesWithSignedUrl;
        return {
            statusCode: 200,
            message: 'Get message successful!',
            data: processedResult,
            actionType: 'LAZY_LOAD_GROUP_MESSAGES',
            action: 'getMessage'
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while get lazy load messages --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to get message!',
            actionType: 'LAZY_LOAD_GROUP_MESSAGES',
            action: 'getMessage'
        };
    }

    async function syncMessages (groupId, lastEvaluatedKey, clientLastSyncedMessage, limit = CONSTANTS.MESSAGE_LAZY_LOADING_LIMIT) {
        let messages = [];
        let newLastEvaluatedKey;
        let isRecentFetchedMessageFound = false;
        const recentFetchedMessage = clientLastSyncedMessage?.recentFetchedMessage;
        const lastFetchedMessage = clientLastSyncedMessage?.lastFetchedMessage;

        let query = Message.query('groupId').eq(groupId)
            .using('groupId-createdAt-index')
            .sort('descending')
            .limit(limit);

        if (lastEvaluatedKey) {
            query = query.startAt(lastEvaluatedKey);
        }

        const result = await query.exec();
        messages = result;
        const reactions = await MessageReactionsHandler.getReactionsForMessage({ messageIds: messages.map(message => message.id) });

        if (!recentFetchedMessage) {
            newLastEvaluatedKey = result.lastKey;
        } else {
            isRecentFetchedMessageFound = messages.find(message => message.id === recentFetchedMessage.messageId);
            isRecentFetchedMessageFound = !!isRecentFetchedMessageFound;
            newLastEvaluatedKey = isRecentFetchedMessageFound ?
                lastFetchedMessage ? { id: lastFetchedMessage.messageId, createdAt: lastFetchedMessage.createdAt, groupId } : undefined
                : result.lastKey;
        }

        return {
            messages,
            reactions,
            lastEvaluatedKey: newLastEvaluatedKey,
            groupId,
            isRecentFetchedMessageFound: recentFetchedMessage ? isRecentFetchedMessageFound : undefined
        };
    }
}

async function fetchMessages (groupId, lastEvaluatedKey, limit = CONSTANTS.MESSAGE_LAZY_LOADING_LIMIT) {
    let query = Message.query('groupId').eq(groupId)
        .using('groupId-createdAt-index')
        .sort('descending')
        .limit(limit);

    if (lastEvaluatedKey) {
        query = query.startAt(lastEvaluatedKey);
    }

    const result = await query.exec();
    const messagesPromise = result.map(async (message) => await EnrichmentService.enrichMessageWithMediaUrls(message));
    const messages = await Promise.all(messagesPromise);

    const reactions = await MessageReactionsHandler.getReactionsForMessage({ messageIds: messages.map(message => message.id) });

    return {
        messages,
        reactions,
        groupId,
        lastEvaluatedKey: result.lastKey
    };
}

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Parses the value from the cache
 * @param {string} value - The value to parse
 * @returns {Object} The parsed value
 */
const getParsedValue = (value) => {
    try {
        return JSON.parse(value);
    } catch (error) {
        return null;
    }
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Gets the conversations user is part of
 * @param {string} userId - The user id
 * @returns {Object} The conversations user is part of
 */
const getConversationsUserIsPartOf = async (userId, versionPrefix) => {
    const userConversationsKey = Utils.getUserConversationsKey({ versionPrefix, userId });
    const userConversations = await RedisUtil.getAllHashValues(userConversationsKey);
    const values = Object.values(userConversations).filter(Boolean);

    const conversations = [];
    for (const value of values) {
        const groupDetails = getParsedValue(value);

        if (
            groupDetails
            && (
                [CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED].includes(groupDetails?.status)
                || groupDetails?.isPersonalConversation
            )
        ) {
            conversations.push(groupDetails);
        }
    }

    return { usersConversations: conversations };
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Gets the conversations details
 * @param {Array} userConversations - The user conversations
 * @param {string} loggedInUserId - The logged in user id
 * @param {string} versionPrefix - The version prefix
 * @returns {Object} The conversations details
 */
const getUserConversationsDetails = async (userConversations, loggedInUserId, versionPrefix) => {
    const conversations = {};

    for (const conversation of userConversations) {
        const { groupId, conversationId, isPersonalConversation } = conversation;
        const currentConversationId = isPersonalConversation ? conversationId : groupId;
        const field = isPersonalConversation ? loggedInUserId : 'details';

        const conversationDetailsKey = Utils.getConversationDetailsKey({ versionPrefix, conversationId: currentConversationId });
        const conversationDetails = await RedisUtil.getHashValue(conversationDetailsKey, field);
        const parsedConversationDetails = getParsedValue(conversationDetails);

        if (
            parsedConversationDetails
            && (
                parsedConversationDetails?.status === CONSTANTS.GROUP_CONVERSATION_STATUS.ACTIVE
                || parsedConversationDetails?.isPersonalConversation
            )
        ) {
            conversations[currentConversationId] = parsedConversationDetails;
        }
    }
    return conversations;
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Gets the group members
 * @param {Array} usersConversations - The users conversations
 * @param {string} versionPrefix - The version prefix
 * @returns {Object} The group members
 */
const getGroupMembers = async (usersConversations, versionPrefix) => {
    const allGroupMembers = {};
    const uniqueUserIds = new Set();
    const groupMembersMap = new Map();

    for (const conversation of usersConversations) {
        const { groupId, isPersonalConversation, userBId } = conversation;

        if (isPersonalConversation) {
            uniqueUserIds.add(userBId);
        } else {
            const groupMembersKey = Utils.getConversationMembersKey({ versionPrefix, groupId });
            const groupMembers = await RedisUtil.getAllHashValues(groupMembersKey);
            const members = Object.values(groupMembers)
                .filter(Boolean)
                .map(member => JSON.parse(member));

            const membersMap = members.reduce((acc, member) => {
                acc[member.userId] = member;
                return acc;
            }, {});
            groupMembersMap.set(groupId, membersMap);

            allGroupMembers[groupId] = members;
            members.forEach(member => uniqueUserIds.add(member.userId));
        }
    }

    return { groupMembers: allGroupMembers, uniqueUserIds, groupMembersMap };
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Gets the group users details
 * @param {Array} groupMembersIds - The group members ids
 * @param {string} versionPrefix - The version prefix
 * @returns {Object} The group users details
 */
const getGroupUsersDetails = async (groupMembersIds, versionPrefix) => {
    const groupUsersDetails = {};
    for (const userId of groupMembersIds) {
        const userDetailsKey = Utils.getUserDetailsKey({ versionPrefix, userId });
        const userDetails = await RedisUtil.getHashValue(userDetailsKey, 'details');
        const parsedUserDetails = getParsedValue(userDetails);

        if (parsedUserDetails) {
            groupUsersDetails[userId] = parsedUserDetails;
        }
    }

    return groupUsersDetails;
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Gets the initial conversation messages
 * @param {string} conversationId - The conversation id
 * @param {string} versionPrefix - The version prefix
 * @returns {Object} The initial conversation messages
 */
const getInitialConversationMessages = async (conversationId, versionPrefix) => {
    const conversationMessagesKey = Utils.getConversationMessagesKey({ versionPrefix, conversationId });
    const messagesFromCache = await RedisUtil.getAllHashValues(conversationMessagesKey);
    const sortedMessageIds = messagesFromCache.messageIds;
    const parsedMessageIds = sortedMessageIds ? getParsedValue(sortedMessageIds) : [];

    const sortedMessages = parsedMessageIds?.map(messageId => {
        const parsedMessageDetails = getParsedValue(messagesFromCache[messageId]);
        if (parsedMessageDetails) {
            return parsedMessageDetails;
        }
        return null;
    }).filter(Boolean);

    const messagesPromise = sortedMessages.map(async (message) => await EnrichmentService.enrichMessageWithMediaUrls(message));
    const messages = await Promise.all(messagesPromise);

    return {
        messages,
        conversationId
    };
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Formats the receivers details
 * @param {Object} receiversDetails - The receivers details
 * @returns {Object} The formatted receivers details
 */
const formatReceiversDetails = (receiversDetails) => {
    return {
        id: receiversDetails?.id,
        email: receiversDetails?.email,
        firstName: receiversDetails?.firstName,
        lastName: receiversDetails?.lastName
    };
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Gets the reactions for message
 * @param {Object} messages - The messages
 * @returns {Object} The reactions for message
 */
const getReactionsForMessage = async ({ messages, isPersonalConversation }) => {
    return await MessageReactionsHandler.getReactionsForMessage(
        {
            messageIds: messages.map(
                message => isPersonalConversation
                    ? message.messageId
                    : message.id
            )
        }
    );
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Generates the personal conversation details
 * @param {Object} messagesData - The messages data
 * @param {Object} usersDetails - The users details
 * @param {Object} conversation - The conversation
 * @param {Object} personalConversations - The personal conversations
 * @param {Object} conversationDetails - The conversation details
 * @param {string} versionPrefix - The version prefix
 */
const generatePersonalConversationDetails = async ({
    messagesData,
    usersDetails,
    conversation,
    personalConversations,
    conversationDetails,
    versionPrefix
}) => {
    const { conversationId } = conversation;
    if (!conversationId) {
        return;
    }

    const { lastMessageIdBeforeDeleted, userBId } = conversationDetails;

    const { filteredMessages } = filterMessagesFromDeletedMessage({
        lastMessageIdBeforeDeleted,
        messages: messagesData.messages
    });

    const { calculatedLastEvaluatedKey } = getCalculatedLastEvaluatedKey({
        messages: filteredMessages,
        isPersonalConversation: true,
        conversationId
    });

    const messages = filteredMessages;

    messagesData.reactions = await getReactionsForMessage({
        messages,
        isPersonalConversation: true
    });

    const receiversDetails = usersDetails[userBId];
    const receiverPersonalConversation = await RedisUtil.getHashValue(
        Utils.getConversationDetailsKey({ versionPrefix, conversationId }), userBId
    );

    personalConversations.push({
        conversationId,
        messages,
        reactions: messagesData.reactions,
        receiversDetails: formatReceiversDetails(receiversDetails),
        lastEvaluatedKey: calculatedLastEvaluatedKey,
        lastReadMessage: conversationDetails.lastReadMessage,
        isMuted: conversationDetails.isMuted,
        isBlocked: conversationDetails.isBlocked,
        isBlockedByReceiver: receiverPersonalConversation?.isBlocked
    });
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Creates the conversation
 * @param {Object} membership - The membership
 * @param {Object} groupDetails - The group details
 */
const createConversation = ({
    membership,
    groupDetails,
    isOrganizationGroup,
    groupMembers,
    messagesData,
    calculatedLastEvaluatedKey,
    loggedInUserDetailsForGroup
}) => {
    const formatedGroupDetails = {
        groupId: membership.groupId,
        groupType: groupDetails.groupType,
        groupName: isOrganizationGroup
            ? groupDetails.organizationMetaData?.name
            : groupDetails.eventMetaData?.title,
        groupImage: isOrganizationGroup
            ? groupDetails.organizationMetaData?.logo
            : groupDetails.eventMetaData?.image,
        groupMemberId: membership?.id,
        status: loggedInUserDetailsForGroup?.status,
        isAdmin: loggedInUserDetailsForGroup?.isAdmin,
        childrenIds: loggedInUserDetailsForGroup?.childrenIds,
        muteConversation: loggedInUserDetailsForGroup?.muteConversation,
        organizationId: groupDetails.organizationId,
        eventId: groupDetails.eventId,
        eventStartDate: groupDetails.eventMetaData?.startDateTime,
        eventEndDate: groupDetails.eventMetaData?.endDateTime,
        groupMemberCount: groupMembers.length,
        lastReadMessage: loggedInUserDetailsForGroup?.lastReadMessage
    };
    return {
        groupMembers,
        groupDetails: formatedGroupDetails,
        groupMessages: messagesData.messages,
        lastEvaluatedKey: calculatedLastEvaluatedKey,
        reactions: messagesData.reactions
    };
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Generates the group conversation details
 * @param {Object} messagesData - The messages data
 * @param {Object} usersDetails - The users details
 * @param {Object} conversation - The conversation
 * @param {Object} conversationDetails - The conversation details
 * @param {Object} groupMembers - The group members
 * @param {Object} groupMembersMap - The group members map
 * @param {Object} eventConversations - The event conversations
 * @param {Object} organizationConversations - The organization conversations
 * @param {Object} loggedInUserId - The logged in user id
 */
const generateGroupConversationDetails = async ({
    messagesData,
    usersDetails,
    conversation,
    conversationDetails,
    groupMembers,
    groupMembersMap,
    eventConversations,
    organizationConversations,
    loggedInUserId
}) => {
    const { groupId } = conversation;
    if (!groupId) {
        return;
    }

    const { groupType, organizationMetaData, eventMetaData } = conversationDetails;

    const isOrgGroup =
        [CONSTANTS.GROUP_TYPES.ORGANIZATION, CONSTANTS.GROUP_TYPES.ORGANIZATION_ADMIN]
            .includes(groupType);

    if (isOrgGroup && organizationMetaData?.logo) {
        conversationDetails.organizationMetaData.logo =
            await UploadService.getSignedUrl(organizationMetaData.logo);
    }
    if (!isOrgGroup && eventMetaData?.image) {
        conversationDetails.eventMetaData.image = await UploadService.getSignedUrl(eventMetaData.image);
    }

    const currentGroupMembers = groupMembers[groupId];
    const currentGroupMembersMap = groupMembersMap.get(groupId);

    const loggedInUserDetailsForGroup = currentGroupMembersMap[loggedInUserId];

    const groupMembersDetails = currentGroupMembers.map(groupMember => {
        const { userId } = groupMember;

        const user = usersDetails[userId];
        if (!user) {
            CONSOLE_LOGGER.error(`User not found for userId: ${userId}`);
            return null;
        }

        return {
            isAdmin: groupMember?.isAdmin ?? false,
            status: groupMember?.status ?? 'disabled',
            id: groupMember?.id ?? '',
            userId: groupMember?.userId ?? '',
            firstName: user.firstName,
            lastName: user.lastName
        };
    }).filter(Boolean);

    const { calculatedLastEvaluatedKey } = getCalculatedLastEvaluatedKey({
        messages: messagesData.messages,
        isPersonalConversation: false,
        groupId
    });

    messagesData.reactions = await getReactionsForMessage({
        messages: messagesData.messages,
        isPersonalConversation: false
    });

    if (isOrgGroup) {
        organizationConversations.push(
            createConversation({
                membership: conversation,
                groupDetails: conversationDetails,
                isOrganizationGroup: isOrgGroup,
                groupMembers: groupMembersDetails,
                messagesData,
                calculatedLastEvaluatedKey,
                loggedInUserDetailsForGroup
            })
        );
    } else {
        eventConversations.push(
            createConversation({
                membership: conversation,
                groupDetails: conversationDetails,
                isOrganizationGroup: isOrgGroup,
                groupMembers: groupMembersDetails,
                messagesData,
                calculatedLastEvaluatedKey,
                loggedInUserDetailsForGroup
            })
        );
    }
};

/**
 * <AUTHOR>
 * @since 04/03/2025
 * @description Gets the groups members and messages
 * @param {Object} event - The event
 * @returns {Object} The groups members and messages
 */
async function getGroupsMembersAndMessages (event) {
    try {
        const [conversationData] =
            await Promise.all([getUserConversations(event)]);

        const data = {
            organizationConversations: conversationData?.organizationConversations,
            eventConversations: conversationData?.eventConversations,
            personalConversations: conversationData?.personalConversations
        };

        return {
            data,
            statusCode: 200,
            message: 'Get group list and members successful',
            action: 'getMessage',
            actionType: 'GET_GROUP_LIST_AND_MEMBERS'
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while get group list, members and messages --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to get group list and members',
            action: 'getMessage',
            actionType: 'GET_GROUP_LIST_AND_MEMBERS'
        };
    }

    async function getUserConversations (event) {
        const organizationConversations = [];
        const eventConversations = [];
        const personalConversations = [];

        const userId = event.body.userId;

        const versionPrefix = await getVersionPrefixForRedisKeys();

        const { usersConversations } = await getConversationsUserIsPartOf(userId, versionPrefix);

        if (usersConversations.length === 0) {
            const data = { organizationConversations, eventConversations, personalConversations };
            CONSOLE_LOGGER.info('--> User has no conversations!');
            return {
                data,
                statusCode: 200,
                message: 'User has no conversations!',
                action: 'getMessage',
                actionType: 'GET_GROUP_LIST_AND_MEMBERS'
            };
        }

        const userConversationsDetails = await getUserConversationsDetails(usersConversations, userId, versionPrefix);

        const { groupMembers, uniqueUserIds, groupMembersMap } = await getGroupMembers(usersConversations, versionPrefix);

        const userIds = [...uniqueUserIds].filter(Boolean);
        const usersDetails = await getGroupUsersDetails(userIds, versionPrefix);

        const conversationPromises = usersConversations.map(async (conversation) => {
            const { groupId, conversationId, isPersonalConversation } = conversation;
            const currentConversationId = isPersonalConversation ? conversationId : groupId;

            const conversationDetails = userConversationsDetails[currentConversationId];
            if (conversationDetails) {
                const messagesData = await getInitialConversationMessages(currentConversationId, versionPrefix);

                if (isPersonalConversation) {
                    await generatePersonalConversationDetails({
                        messagesData,
                        usersDetails,
                        conversation,
                        personalConversations,
                        conversationDetails,
                        versionPrefix
                    });
                } else {
                    await generateGroupConversationDetails({
                        messagesData,
                        usersDetails,
                        conversation,
                        conversationDetails,
                        groupMembers,
                        groupMembersMap,
                        eventConversations,
                        organizationConversations,
                        loggedInUserId: userId
                    });
                }
            }
        });
        await Promise.all(conversationPromises);
        return { organizationConversations, eventConversations, personalConversations };
    }
}

/**
 * @desc Generic function to perform batchGet in chunks
 * <AUTHOR>
 * @since 25/07/2025
 * @param {Object} options
 * @param {Array} options.ids - List of IDs to fetch
 * @param {Object} options.model - Dynamoose model to use
 * @param {Array} [options.attributes] - Attributes to fetch (optional)
 * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET] - Max chunk size per request
 * @returns {Promise<Array>} Combined result of all batchGets
*/
const batchGetInChunks = async ({ ids, model, attributes, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET }) => {
    if (!Array.isArray(ids)) {
        return [];
    }
    ids = ids.filter(Boolean);
    if (ids.length === 0) {
        return [];
    }

    const result = [];

    for (let i = 0; i < ids.length; i += chunkSize) {
        const chunk = ids.slice(i, i + chunkSize);
        const dataChunk = await model.batchGet(chunk, attributes ? { attributes } : undefined);
        result.push(...dataChunk);
    }

    return result;
};

const getUserChildrenList = async (event) => {
    try {
        const groupMemberId = event.body.groupMemberId;
        const groupMember = await GroupMember.get(groupMemberId);
        if (!groupMember) {
            return {
                statusCode: 404,
                message: 'Group member not found',
                action: 'getMessage',
                actionType: 'GET_USER_CHILDREN_LIST'
            };
        }

        const childrenIds = [...new Set(groupMember?.childrenIds ?? [])];
        let children = [];

        if (childrenIds.length > 0) {
            children = await batchGetInChunks({
                ids: childrenIds,
                model: Children,
                attributes: ['id', 'firstName', 'lastName', 'associatedColor', 'photoURL', 'school', 'homeRoom']
            });
        }

        const uniqueOrganizationIds = new Set();
        children.forEach(child => {
            uniqueOrganizationIds.add(child.school);
            if (child.homeRoom) {
                uniqueOrganizationIds.add(child.homeRoom);
            }
        });

        let organizations = {};
        if (uniqueOrganizationIds.size > 0) {
            const organizationsData = await batchGetInChunks({
                ids: Array.from(uniqueOrganizationIds),
                model: Organization,
                attributes: ['id', 'name']
            });

            organizations = organizationsData.reduce((acc, organization) => {
                acc[organization.id] = organization;
                return acc;
            }, {});
        }

        for (const child of children) {
            child.school = organizations[child.school]?.name;
            if (child.homeRoom) {
                child.homeRoom = organizations[child.homeRoom]?.name;
            }
            if (child.photoURL) {
                child.photoURL = await UploadService.getSignedUrl(child.photoURL);
            }
        }

        const data = {
            children,
            isAdmin: groupMember.isAdmin,
            id: groupMember.id,
            userId: groupMember.userId
        };

        return {
            data,
            statusCode: 200,
            message: 'Get user children list successful',
            action: 'getMessage',
            actionType: 'GET_USER_CHILDREN_LIST'
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while get user children list --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to get user children list',
            action: 'getMessage',
            actionType: 'GET_USER_CHILDREN_LIST'
        };
    }
};

async function getGroupMembersAndMessages (event) {
    try {
        const { groupId, userId } = event.body;

        const groupMember = await GroupMember.query('groupId')
            .eq(groupId)
            .using('groupId-index')
            .where('userId').eq(userId)
            .where('status').in([CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED])
            .exec();

        if (!groupMember.length) {
            return {
                statusCode: 403,
                message: 'User is not a member of this group',
                action: 'getMessage',
                actionType: 'GET_SINGLE_GROUP_DETAILS'
            };
        }

        const groupMembers = await GroupMember.query('groupId')
            .eq(groupId)
            .using('groupId-index')
            .attributes(['groupId', 'userId', 'status', 'isAdmin', 'id'])
            .exec();

        const memberUserIds = [...new Set(groupMembers.map(member => member.userId))];
        const usersDetails = (await Promise.all(memberUserIds
            .map(async (userId) => await User.query('id').eq(userId)
                .attributes(['id', 'firstName', 'lastName'])
                .exec()
            ))).flat();

        const groupMembersDetails = usersDetails.map(user => {
            const memberDetails = groupMembers.find(member => member.userId === user.id);
            return {
                ...user,
                isAdmin: memberDetails?.isAdmin ?? false,
                status: memberDetails?.status ?? 'disabled',
                groupMemberId: memberDetails?.id ?? ''
            };
        });

        const messagesData = await fetchMessages(groupId, undefined, CONSTANTS.INITIAL_MESSAGE_FETCH_LIMIT);

        const data = {
            groupMembers: groupMembersDetails,
            messages: messagesData.messages,
            lastEvaluatedKey: messagesData.lastEvaluatedKey,
            reactions: messagesData.reactions
        };

        return {
            data,
            statusCode: 200,
            message: 'Get group details successful',
            action: 'getMessage',
            actionType: 'GET_GROUP_MEMBERS_AND_MESSAGES'
        };
    } catch (error) {
        CONSOLE_LOGGER.error('--> Error while getting group details --> ', error);
        return {
            statusCode: 500,
            message: 'Failed to get group details',
            action: 'getMessage',
            actionType: 'GET_GROUP_MEMBERS_AND_MESSAGES'
        };
    }
}
