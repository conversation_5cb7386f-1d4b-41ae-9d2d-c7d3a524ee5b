function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const organizationSchema = new dynamoose.Schema(stryMutAct_9fa48("645") ? {} : (stryCov_9fa48("645"), {
  id: stryMutAct_9fa48("646") ? {} : (stryCov_9fa48("646"), {
    hashKey: stryMutAct_9fa48("647") ? false : (stryCov_9fa48("647"), true),
    type: String,
    default: stryMutAct_9fa48("648") ? () => undefined : (stryCov_9fa48("648"), () => uuidv4())
  }),
  name: stryMutAct_9fa48("649") ? {} : (stryCov_9fa48("649"), {
    type: String,
    required: stryMutAct_9fa48("650") ? false : (stryCov_9fa48("650"), true),
    index: stryMutAct_9fa48("651") ? {} : (stryCov_9fa48("651"), {
      global: stryMutAct_9fa48("652") ? false : (stryCov_9fa48("652"), true),
      name: stryMutAct_9fa48("653") ? "" : (stryCov_9fa48("653"), 'name-index'),
      project: stryMutAct_9fa48("654") ? false : (stryCov_9fa48("654"), true)
    })
  }),
  zipCode: stryMutAct_9fa48("655") ? {} : (stryCov_9fa48("655"), {
    type: String,
    required: stryMutAct_9fa48("656") ? false : (stryCov_9fa48("656"), true)
  }),
  applicableAgeRange: stryMutAct_9fa48("657") ? {} : (stryCov_9fa48("657"), {
    type: String
  }),
  organizationType: stryMutAct_9fa48("658") ? {} : (stryCov_9fa48("658"), {
    type: String
  }),
  parentOrganization: stryMutAct_9fa48("659") ? {} : (stryCov_9fa48("659"), {
    type: String,
    index: stryMutAct_9fa48("660") ? {} : (stryCov_9fa48("660"), {
      global: stryMutAct_9fa48("661") ? false : (stryCov_9fa48("661"), true),
      name: stryMutAct_9fa48("662") ? "" : (stryCov_9fa48("662"), 'parentOrganization-index'),
      project: stryMutAct_9fa48("663") ? false : (stryCov_9fa48("663"), true)
    })
  }),
  parentOrganizationName: stryMutAct_9fa48("664") ? {} : (stryCov_9fa48("664"), {
    type: String
  }),
  associatedOrganizations: stryMutAct_9fa48("665") ? {} : (stryCov_9fa48("665"), {
    type: Array,
    schema: stryMutAct_9fa48("666") ? [] : (stryCov_9fa48("666"), [String]),
    default: stryMutAct_9fa48("667") ? ["Stryker was here"] : (stryCov_9fa48("667"), [])
  }),
  archievedAssociatedOrganizations: stryMutAct_9fa48("668") ? {} : (stryCov_9fa48("668"), {
    type: Array,
    schema: stryMutAct_9fa48("669") ? [] : (stryCov_9fa48("669"), [String]),
    default: stryMutAct_9fa48("670") ? ["Stryker was here"] : (stryCov_9fa48("670"), [])
  }),
  logo: stryMutAct_9fa48("671") ? {} : (stryCov_9fa48("671"), {
    type: String
  }),
  paymentDetails: stryMutAct_9fa48("672") ? {} : (stryCov_9fa48("672"), {
    type: Object,
    schema: stryMutAct_9fa48("673") ? {} : (stryCov_9fa48("673"), {
      stripeConnectAccountId: stryMutAct_9fa48("674") ? {} : (stryCov_9fa48("674"), {
        type: String
      }),
      stripeOnboardingStatus: stryMutAct_9fa48("675") ? {} : (stryCov_9fa48("675"), {
        type: String,
        enum: stryMutAct_9fa48("676") ? [] : (stryCov_9fa48("676"), [stryMutAct_9fa48("677") ? "" : (stryCov_9fa48("677"), 'active'), stryMutAct_9fa48("678") ? "" : (stryCov_9fa48("678"), 'inactive'), stryMutAct_9fa48("679") ? "" : (stryCov_9fa48("679"), 'payoutPaused'), stryMutAct_9fa48("680") ? "" : (stryCov_9fa48("680"), 'rejectedFraud'), stryMutAct_9fa48("681") ? "" : (stryCov_9fa48("681"), 'rejectedOther')]),
        default: stryMutAct_9fa48("682") ? "" : (stryCov_9fa48("682"), 'inactive')
      }),
      venmoPaymentURL: stryMutAct_9fa48("683") ? {} : (stryCov_9fa48("683"), {
        type: String
      })
    }),
    default: stryMutAct_9fa48("684") ? {} : (stryCov_9fa48("684"), {
      stripeConnectAccountId: stryMutAct_9fa48("685") ? "Stryker was here!" : (stryCov_9fa48("685"), ''),
      stripeOnboardingStatus: stryMutAct_9fa48("686") ? "" : (stryCov_9fa48("686"), 'inactive'),
      venmoPaymentURL: stryMutAct_9fa48("687") ? "Stryker was here!" : (stryCov_9fa48("687"), '')
    })
  }),
  paymentInstructions: stryMutAct_9fa48("688") ? {} : (stryCov_9fa48("688"), {
    type: Object,
    schema: stryMutAct_9fa48("689") ? {} : (stryCov_9fa48("689"), {
      cashInstruction: stryMutAct_9fa48("690") ? {} : (stryCov_9fa48("690"), {
        type: String
      }),
      chequeInstruction: stryMutAct_9fa48("691") ? {} : (stryCov_9fa48("691"), {
        type: String
      }),
      venmoInstruction: stryMutAct_9fa48("692") ? {} : (stryCov_9fa48("692"), {
        type: String
      })
    }),
    default: stryMutAct_9fa48("693") ? {} : (stryCov_9fa48("693"), {
      cashInstruction: stryMutAct_9fa48("694") ? "Stryker was here!" : (stryCov_9fa48("694"), ''),
      chequeInstruction: stryMutAct_9fa48("695") ? "Stryker was here!" : (stryCov_9fa48("695"), ''),
      venmoInstruction: stryMutAct_9fa48("696") ? "Stryker was here!" : (stryCov_9fa48("696"), '')
    })
  }),
  allowedPaymentType: stryMutAct_9fa48("697") ? {} : (stryCov_9fa48("697"), {
    type: Object,
    schema: stryMutAct_9fa48("698") ? {} : (stryCov_9fa48("698"), {
      cash: stryMutAct_9fa48("699") ? {} : (stryCov_9fa48("699"), {
        type: Boolean
      }),
      cheque: stryMutAct_9fa48("700") ? {} : (stryCov_9fa48("700"), {
        type: Boolean
      }),
      venmo: stryMutAct_9fa48("701") ? {} : (stryCov_9fa48("701"), {
        type: Boolean
      }),
      stripe: stryMutAct_9fa48("702") ? {} : (stryCov_9fa48("702"), {
        type: Boolean
      })
    }),
    default: stryMutAct_9fa48("703") ? {} : (stryCov_9fa48("703"), {
      cash: stryMutAct_9fa48("704") ? false : (stryCov_9fa48("704"), true),
      cheque: stryMutAct_9fa48("705") ? true : (stryCov_9fa48("705"), false),
      venmo: stryMutAct_9fa48("706") ? true : (stryCov_9fa48("706"), false),
      stripe: stryMutAct_9fa48("707") ? true : (stryCov_9fa48("707"), false)
    })
  }),
  platformFee: stryMutAct_9fa48("708") ? {} : (stryCov_9fa48("708"), {
    type: Number
  }),
  platformFeeCoveredBy: stryMutAct_9fa48("709") ? {} : (stryCov_9fa48("709"), {
    type: String,
    enum: stryMutAct_9fa48("710") ? [] : (stryCov_9fa48("710"), [stryMutAct_9fa48("711") ? "" : (stryCov_9fa48("711"), 'organization'), stryMutAct_9fa48("712") ? "" : (stryCov_9fa48("712"), 'parent'), stryMutAct_9fa48("713") ? "" : (stryCov_9fa48("713"), 'optional')])
  }),
  minPlatformFeeAmount: stryMutAct_9fa48("714") ? {} : (stryCov_9fa48("714"), {
    type: Number
  }),
  category: stryMutAct_9fa48("715") ? {} : (stryCov_9fa48("715"), {
    type: String,
    enum: stryMutAct_9fa48("716") ? [] : (stryCov_9fa48("716"), [stryMutAct_9fa48("717") ? "" : (stryCov_9fa48("717"), 'PTO'), stryMutAct_9fa48("718") ? "" : (stryCov_9fa48("718"), 'School'), stryMutAct_9fa48("719") ? "" : (stryCov_9fa48("719"), 'Homeroom'), stryMutAct_9fa48("720") ? "" : (stryCov_9fa48("720"), 'Club'), stryMutAct_9fa48("721") ? "" : (stryCov_9fa48("721"), 'Super Organization'), stryMutAct_9fa48("722") ? "" : (stryCov_9fa48("722"), 'Business')]),
    required: stryMutAct_9fa48("723") ? false : (stryCov_9fa48("723"), true),
    index: stryMutAct_9fa48("724") ? {} : (stryCov_9fa48("724"), {
      global: stryMutAct_9fa48("725") ? false : (stryCov_9fa48("725"), true),
      name: stryMutAct_9fa48("726") ? "" : (stryCov_9fa48("726"), 'category-index'),
      project: stryMutAct_9fa48("727") ? false : (stryCov_9fa48("727"), true)
    })
  }),
  address: stryMutAct_9fa48("728") ? {} : (stryCov_9fa48("728"), {
    type: String,
    required: stryMutAct_9fa48("729") ? false : (stryCov_9fa48("729"), true)
  }),
  country: stryMutAct_9fa48("730") ? {} : (stryCov_9fa48("730"), {
    type: String,
    required: stryMutAct_9fa48("731") ? false : (stryCov_9fa48("731"), true)
  }),
  state: stryMutAct_9fa48("732") ? {} : (stryCov_9fa48("732"), {
    type: String,
    required: stryMutAct_9fa48("733") ? false : (stryCov_9fa48("733"), true)
  }),
  city: stryMutAct_9fa48("734") ? {} : (stryCov_9fa48("734"), {
    type: String,
    required: stryMutAct_9fa48("735") ? false : (stryCov_9fa48("735"), true)
  }),
  isEnabled: stryMutAct_9fa48("736") ? {} : (stryCov_9fa48("736"), {
    type: Number,
    // 0 = disabled
    enum: stryMutAct_9fa48("737") ? [] : (stryCov_9fa48("737"), [0, 1]),
    default: 1
  }),
  isDeleted: stryMutAct_9fa48("738") ? {} : (stryCov_9fa48("738"), {
    type: Number,
    // 1 = deleted
    enum: stryMutAct_9fa48("739") ? [] : (stryCov_9fa48("739"), [0, 1]),
    default: 0
  }),
  createdBy: stryMutAct_9fa48("740") ? {} : (stryCov_9fa48("740"), {
    type: String
  }),
  updatedBy: stryMutAct_9fa48("741") ? {} : (stryCov_9fa48("741"), {
    type: String
  })
}), stryMutAct_9fa48("742") ? {} : (stryCov_9fa48("742"), {
  timestamps: stryMutAct_9fa48("743") ? {} : (stryCov_9fa48("743"), {
    createdAt: stryMutAct_9fa48("744") ? "" : (stryCov_9fa48("744"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("745") ? "" : (stryCov_9fa48("745"), 'updatedAt')
  })
}));
module.exports = dynamoose.model(stryMutAct_9fa48("746") ? "" : (stryCov_9fa48("746"), 'Organization'), organizationSchema);