/**
 * This file contains routes used for notification.
 * Created by Growexx on 31/01/2024.
 * @name notificationRoutes
*/
// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const router = require('express').Router();
const NotificationController = require('../services/notification/notificationController');
const AuthMiddleware = require('../middleware/auth');
const HmacMiddleware = require('../middleware/hmac');
router.get(stryMutAct_9fa48("973") ? "" : (stryCov_9fa48("973"), '/list'), HmacMiddleware, AuthMiddleware, NotificationController.getNotificationList);
router.post(stryMutAct_9fa48("974") ? "" : (stryCov_9fa48("974"), '/'), HmacMiddleware, AuthMiddleware, NotificationController.createNotification);
router.patch(stryMutAct_9fa48("975") ? "" : (stryCov_9fa48("975"), '/'), HmacMiddleware, AuthMiddleware, NotificationController.updateNotification);
router.post(stryMutAct_9fa48("976") ? "" : (stryCov_9fa48("976"), '/send'), HmacMiddleware, AuthMiddleware, NotificationController.sendNotification);
router.post(stryMutAct_9fa48("977") ? "" : (stryCov_9fa48("977"), '/notify'), NotificationController.notify);
router.post(stryMutAct_9fa48("978") ? "" : (stryCov_9fa48("978"), '/contact-us'), NotificationController.contactUs);
module.exports = router;