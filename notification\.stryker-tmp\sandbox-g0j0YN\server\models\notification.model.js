function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const NotificationSchema = new dynamoose.Schema(stryMutAct_9fa48("623") ? {} : (stryCov_9fa48("623"), {
  id: stryMutAct_9fa48("624") ? {} : (stryCov_9fa48("624"), {
    hashKey: stryMutAct_9fa48("625") ? false : (stryCov_9fa48("625"), true),
    type: String,
    default: stryMutAct_9fa48("626") ? () => undefined : (stryCov_9fa48("626"), () => uuidv4())
  }),
  userId: stryMutAct_9fa48("627") ? {} : (stryCov_9fa48("627"), {
    type: String,
    index: stryMutAct_9fa48("628") ? {} : (stryCov_9fa48("628"), {
      name: stryMutAct_9fa48("629") ? "" : (stryCov_9fa48("629"), 'userId-index'),
      global: stryMutAct_9fa48("630") ? false : (stryCov_9fa48("630"), true),
      rangeKey: stryMutAct_9fa48("631") ? "" : (stryCov_9fa48("631"), 'createdAt')
    })
  }),
  title: stryMutAct_9fa48("632") ? {} : (stryCov_9fa48("632"), {
    type: String
  }),
  description: stryMutAct_9fa48("633") ? {} : (stryCov_9fa48("633"), {
    type: String
  }),
  readStatus: stryMutAct_9fa48("634") ? {} : (stryCov_9fa48("634"), {
    type: Boolean,
    default: stryMutAct_9fa48("635") ? true : (stryCov_9fa48("635"), false)
  }),
  associatedChildId: stryMutAct_9fa48("636") ? {} : (stryCov_9fa48("636"), {
    type: String
  }),
  notificationAction: stryMutAct_9fa48("637") ? {} : (stryCov_9fa48("637"), {
    type: String
  }),
  payload: stryMutAct_9fa48("638") ? {} : (stryCov_9fa48("638"), {
    type: Object
  })
}), stryMutAct_9fa48("639") ? {} : (stryCov_9fa48("639"), {
  timestamps: stryMutAct_9fa48("640") ? {} : (stryCov_9fa48("640"), {
    createdAt: stryMutAct_9fa48("641") ? "" : (stryCov_9fa48("641"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("642") ? "" : (stryCov_9fa48("642"), 'updatedAt')
  }),
  saveUnknown: stryMutAct_9fa48("643") ? false : (stryCov_9fa48("643"), true)
}));
module.exports = dynamoose.model(stryMutAct_9fa48("644") ? "" : (stryCov_9fa48("644"), 'Notification'), NotificationSchema);