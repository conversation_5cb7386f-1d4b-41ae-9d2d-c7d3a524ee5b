/**
 * Contains security and schemas for error or success message definitions
 */
// @ts-nocheck


/**
 * @openapi
 * components:
 *   securitySchemes:
 *      bearerAuth:
 *          name: 'Authorization'
 *          type: apiKey
 *          in: header
 *          bearerFormat: JWT
 *
 *   security:
 *      - bearerAuth: []
 *
 *   messageDefinition:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: error/success code (0-error, 1-success)
 *              message:
 *                  type: string
 *                  description: error/success message
 *              data:
 *                  type: string
 *                  description: testing email
 *              email:
 *                  type: string
 *                  description: unique email address
 *              token:
 *                  type: string
 *                  description: token for signup and login
 *              password:
 *                 type: string
 *                 description: password for signup and login
 *              responseData:
 *                  type: object
 *
 *   schemas:
 *
 *      errorBadRequest:
 *          properties:
 *              status:
 *                 $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Request is invalid
 *
 *      unexpectedError:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Something went wrong. please try again.
 *
 *      unauthorisedAccess:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Email & Password do not match.
 *
 *      unauthorisedAccessLogin:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Email & Password do not match.
 *
 *      errorUserNotFound:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Email address/phone number not found. Please enter a registered email address/phone number.
 *
 *      userNotFound:
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: User not found. Please enter a registered email address.
 *
 *      inValidFiledError:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: <Field> is not valid.
 *
 *      validationError:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: <Field> can't be blank.
 *
 *      unauthorisedAccessUser:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: You are not authorized to access this resource.
 *
 */