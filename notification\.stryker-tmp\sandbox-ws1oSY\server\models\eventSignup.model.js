// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const eventSignupSchema = new dynamoose.Schema(stryMutAct_9fa48("361") ? {} : (stryCov_9fa48("361"), {
  id: stryMutAct_9fa48("362") ? {} : (stryCov_9fa48("362"), {
    hashKey: stryMutAct_9fa48("363") ? false : (stryCov_9fa48("363"), true),
    type: String,
    default: stryMutAct_9fa48("364") ? () => undefined : (stryCov_9fa48("364"), () => uuidv4())
  }),
  eventId: stryMutAct_9fa48("365") ? {} : (stryCov_9fa48("365"), {
    type: String,
    required: stryMutAct_9fa48("366") ? false : (stryCov_9fa48("366"), true),
    index: stryMutAct_9fa48("367") ? {} : (stryCov_9fa48("367"), {
      name: stryMutAct_9fa48("368") ? "" : (stryCov_9fa48("368"), 'eventId-index'),
      global: stryMutAct_9fa48("369") ? false : (stryCov_9fa48("369"), true),
      project: stryMutAct_9fa48("370") ? false : (stryCov_9fa48("370"), true)
    })
  }),
  organizationId: stryMutAct_9fa48("371") ? {} : (stryCov_9fa48("371"), {
    type: String,
    required: stryMutAct_9fa48("372") ? false : (stryCov_9fa48("372"), true)
  }),
  parentId: stryMutAct_9fa48("373") ? {} : (stryCov_9fa48("373"), {
    type: String,
    required: stryMutAct_9fa48("374") ? false : (stryCov_9fa48("374"), true)
  }),
  childId: stryMutAct_9fa48("375") ? {} : (stryCov_9fa48("375"), {
    type: String,
    required: stryMutAct_9fa48("376") ? false : (stryCov_9fa48("376"), true),
    index: stryMutAct_9fa48("377") ? {} : (stryCov_9fa48("377"), {
      name: stryMutAct_9fa48("378") ? "" : (stryCov_9fa48("378"), 'childId-index'),
      global: stryMutAct_9fa48("379") ? false : (stryCov_9fa48("379"), true),
      project: stryMutAct_9fa48("380") ? false : (stryCov_9fa48("380"), true)
    })
  }),
  donationAmount: stryMutAct_9fa48("381") ? {} : (stryCov_9fa48("381"), {
    type: Number,
    default: 0
  }),
  volunteerDetails: stryMutAct_9fa48("382") ? {} : (stryCov_9fa48("382"), {
    type: Set,
    schema: stryMutAct_9fa48("383") ? [] : (stryCov_9fa48("383"), [String])
  }),
  stripePaymentIntentId: stryMutAct_9fa48("384") ? {} : (stryCov_9fa48("384"), {
    type: String,
    index: stryMutAct_9fa48("385") ? {} : (stryCov_9fa48("385"), {
      global: stryMutAct_9fa48("386") ? false : (stryCov_9fa48("386"), true),
      name: stryMutAct_9fa48("387") ? "" : (stryCov_9fa48("387"), 'stripePaymentIntentId-index'),
      project: stryMutAct_9fa48("388") ? false : (stryCov_9fa48("388"), true)
    })
  }),
  paymentDetails: stryMutAct_9fa48("389") ? {} : (stryCov_9fa48("389"), {
    type: Object,
    schema: stryMutAct_9fa48("390") ? {} : (stryCov_9fa48("390"), {
      stripeCustomerId: stryMutAct_9fa48("391") ? {} : (stryCov_9fa48("391"), {
        type: String
      }),
      stripeConnectAccountId: stryMutAct_9fa48("392") ? {} : (stryCov_9fa48("392"), {
        type: String
      }),
      transactionFee: stryMutAct_9fa48("393") ? {} : (stryCov_9fa48("393"), {
        type: Number,
        default: 0
      }),
      platformFeeCoveredBy: stryMutAct_9fa48("394") ? {} : (stryCov_9fa48("394"), {
        type: String,
        enum: stryMutAct_9fa48("395") ? [] : (stryCov_9fa48("395"), [stryMutAct_9fa48("396") ? "" : (stryCov_9fa48("396"), 'parent'), stryMutAct_9fa48("397") ? "" : (stryCov_9fa48("397"), 'organization')])
      }),
      paymentStatus: stryMutAct_9fa48("398") ? {} : (stryCov_9fa48("398"), {
        type: String,
        enum: stryMutAct_9fa48("399") ? [] : (stryCov_9fa48("399"), [stryMutAct_9fa48("400") ? "" : (stryCov_9fa48("400"), 'pending'), stryMutAct_9fa48("401") ? "" : (stryCov_9fa48("401"), 'approved'), stryMutAct_9fa48("402") ? "" : (stryCov_9fa48("402"), 'cancelled'), stryMutAct_9fa48("403") ? "" : (stryCov_9fa48("403"), 'payment-initiated')]),
        required: stryMutAct_9fa48("404") ? false : (stryCov_9fa48("404"), true)
      }),
      paymentType: stryMutAct_9fa48("405") ? {} : (stryCov_9fa48("405"), {
        type: String,
        enum: stryMutAct_9fa48("406") ? [] : (stryCov_9fa48("406"), [stryMutAct_9fa48("407") ? "" : (stryCov_9fa48("407"), 'stripe'), stryMutAct_9fa48("408") ? "" : (stryCov_9fa48("408"), 'cash'), stryMutAct_9fa48("409") ? "" : (stryCov_9fa48("409"), 'cheque'), stryMutAct_9fa48("410") ? "" : (stryCov_9fa48("410"), 'venmo'), stryMutAct_9fa48("411") ? "" : (stryCov_9fa48("411"), 'free')])
      }),
      membershipDiscount: stryMutAct_9fa48("412") ? {} : (stryCov_9fa48("412"), {
        type: Number,
        default: 0
      })
    }),
    default: stryMutAct_9fa48("413") ? {} : (stryCov_9fa48("413"), {
      stripeCustomerId: stryMutAct_9fa48("414") ? "Stryker was here!" : (stryCov_9fa48("414"), ''),
      stripeConnectAccountId: stryMutAct_9fa48("415") ? "Stryker was here!" : (stryCov_9fa48("415"), ''),
      transactionFee: 0
    })
  }),
  purchasedProducts: stryMutAct_9fa48("416") ? {} : (stryCov_9fa48("416"), {
    type: Array,
    schema: stryMutAct_9fa48("417") ? [] : (stryCov_9fa48("417"), [stryMutAct_9fa48("418") ? {} : (stryCov_9fa48("418"), {
      type: Object,
      schema: stryMutAct_9fa48("419") ? {} : (stryCov_9fa48("419"), {
        id: stryMutAct_9fa48("420") ? {} : (stryCov_9fa48("420"), {
          type: String,
          required: stryMutAct_9fa48("421") ? false : (stryCov_9fa48("421"), true)
        }),
        itemName: stryMutAct_9fa48("422") ? {} : (stryCov_9fa48("422"), {
          type: String,
          required: stryMutAct_9fa48("423") ? false : (stryCov_9fa48("423"), true)
        }),
        itemCost: stryMutAct_9fa48("424") ? {} : (stryCov_9fa48("424"), {
          type: Number,
          required: stryMutAct_9fa48("425") ? false : (stryCov_9fa48("425"), true)
        }),
        quantity: stryMutAct_9fa48("426") ? {} : (stryCov_9fa48("426"), {
          type: Number,
          required: stryMutAct_9fa48("427") ? false : (stryCov_9fa48("427"), true)
        })
      })
    })])
  }),
  quantityCount: stryMutAct_9fa48("428") ? {} : (stryCov_9fa48("428"), {
    type: Number,
    default: 1
  }),
  isGuestSignup: stryMutAct_9fa48("429") ? {} : (stryCov_9fa48("429"), {
    type: Boolean,
    default: stryMutAct_9fa48("430") ? true : (stryCov_9fa48("430"), false)
  }),
  createdBy: stryMutAct_9fa48("431") ? {} : (stryCov_9fa48("431"), {
    type: String,
    required: stryMutAct_9fa48("432") ? false : (stryCov_9fa48("432"), true)
  }),
  updatedBy: stryMutAct_9fa48("433") ? {} : (stryCov_9fa48("433"), {
    type: String
  })
}), stryMutAct_9fa48("434") ? {} : (stryCov_9fa48("434"), {
  timestamps: stryMutAct_9fa48("435") ? {} : (stryCov_9fa48("435"), {
    createdAt: stryMutAct_9fa48("436") ? "" : (stryCov_9fa48("436"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("437") ? "" : (stryCov_9fa48("437"), 'updatedAt')
  })
}));
module.exports = dynamoose.model(stryMutAct_9fa48("438") ? "" : (stryCov_9fa48("438"), 'EventSignups'), eventSignupSchema);