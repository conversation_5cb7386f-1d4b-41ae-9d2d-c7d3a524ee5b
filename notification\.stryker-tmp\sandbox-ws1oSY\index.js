#!/usr/bin/env node
// @ts-nocheck
'use strict';

const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: env + '.env' });
const app = require('./server/server');
const SqsService = require('./server/sqs/sqsService');
const PushNotificationService = require('./server/pushNotification/pushNotificationService');
const UpdateFeedsService = require('./server/updateFeeds/updateChildFeedsService');
const AWS = require('aws-sdk');
const ConversationService = require('./server/conversation/ConversationService');

AWS.config.update({
    region: process.env.AWS_DB_REGION,
    endpoint: process.env.DB_HOST,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

const isSqsTrigger = event => event.Records !== undefined && Array.isArray(event.Records);
const isCloudWatchTrigger = event => event.source === 'aws.events';

const handleSqs = async (event, context) => {
    await SqsService.pullEventFromQueue(event, context);
};

const handleCloudWatch = async (event, context) => {
    await PushNotificationService.pushNotfication(event, context);
    await UpdateFeedsService.updateChildFeeds();
    await ConversationService.updateConversationGroups();
};

if (process.env.SERVERLESS === 'false') {
    module.exports = app.listen(process.env.PORT, () => {
        CONSOLE_LOGGER.info('Server is started at : %s', process.env.PORT);
    });
} else {
    module.exports.handler = async (event, context) => {
        if (isCloudWatchTrigger(event)) {
            CONSOLE_LOGGER.info('Handling CloudWatch event');
            return handleCloudWatch(event, context);
        } else if (isSqsTrigger(event)) {
            CONSOLE_LOGGER.info('Inserting SQS event to the queue');
            return handleSqs(event, context);
        } else {
            const serverless = require('serverless-http');
            CONSOLE_LOGGER.info('Serverless project is started');
            return serverless(app)(event, context);
        }
    };
}
