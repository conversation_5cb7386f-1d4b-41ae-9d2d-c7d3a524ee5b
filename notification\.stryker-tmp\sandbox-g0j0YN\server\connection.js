function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const AWS = require('aws-sdk');
const {
  DynamoDBClient,
  ListTablesCommand
} = require('@aws-sdk/client-dynamodb');
const dynamoose = require('dynamoose');
class DynamoDBConnection {
  static async connectToDB() {
    if (stryMutAct_9fa48("0")) {
      {}
    } else {
      stryCov_9fa48("0");
      const dynamoDBConfig = stryMutAct_9fa48("1") ? {} : (stryCov_9fa48("1"), {
        region: process.env.AWS_DB_REGION
      });
      if (stryMutAct_9fa48("4") ? process.env.LOCAL !== 'true' : stryMutAct_9fa48("3") ? false : stryMutAct_9fa48("2") ? true : (stryCov_9fa48("2", "3", "4"), process.env.LOCAL === (stryMutAct_9fa48("5") ? "" : (stryCov_9fa48("5"), 'true')))) {
        if (stryMutAct_9fa48("6")) {
          {}
        } else {
          stryCov_9fa48("6");
          dynamoDBConfig.endpoint = process.env.DB_HOST;
        }
      }
      const dynamodb = new AWS.DynamoDB(dynamoDBConfig);
      const ddb = new dynamoose.aws.ddb.DynamoDB(stryMutAct_9fa48("7") ? {} : (stryCov_9fa48("7"), {
        'credentials': stryMutAct_9fa48("8") ? {} : (stryCov_9fa48("8"), {
          'accessKeyId': process.env.ACCESS_KEY_ID,
          'secretAccessKey': process.env.SECRET_ACCESS_KEY
        }),
        'region': process.env.AWS_DB_REGION
      }));
      dynamoose.aws.ddb.set(ddb);
      return new Promise(async resolve => {
        if (stryMutAct_9fa48("9")) {
          {}
        } else {
          stryCov_9fa48("9");
          try {
            if (stryMutAct_9fa48("10")) {
              {}
            } else {
              stryCov_9fa48("10");
              if (stryMutAct_9fa48("13") ? process.env.LOCAL === 'true' : stryMutAct_9fa48("12") ? false : stryMutAct_9fa48("11") ? true : (stryCov_9fa48("11", "12", "13"), process.env.LOCAL !== (stryMutAct_9fa48("14") ? "" : (stryCov_9fa48("14"), 'true')))) {
                if (stryMutAct_9fa48("15")) {
                  {}
                } else {
                  stryCov_9fa48("15");
                  const client = new DynamoDBClient(dynamoDBConfig);
                  const command = new ListTablesCommand({});
                  const response = await client.send(command);
                  const tableNames = response.TableNames;
                  CONSOLE_LOGGER.info(stryMutAct_9fa48("16") ? "" : (stryCov_9fa48("16"), 'Connected to DynamoDB'));
                  resolve(tableNames);
                }
              } else {
                if (stryMutAct_9fa48("17")) {
                  {}
                } else {
                  stryCov_9fa48("17");
                  const data = await dynamodb.listTables().promise();
                  CONSOLE_LOGGER.info(stryMutAct_9fa48("18") ? "" : (stryCov_9fa48("18"), 'Connected to DynamoDB'));
                  resolve(data.TableNames);
                }
              }
            }
          } catch (error) {
            if (stryMutAct_9fa48("19")) {
              {}
            } else {
              stryCov_9fa48("19");
              CONSOLE_LOGGER.info(stryMutAct_9fa48("20") ? "" : (stryCov_9fa48("20"), 'Error connecting to DynamoDB:'), JSON.stringify(error));
              CONSOLE_LOGGER.error(stryMutAct_9fa48("21") ? "" : (stryCov_9fa48("21"), 'DynamoDB connection unsuccessful, retry after 0.5 seconds.'));
              setTimeout(DynamoDBConnection.connectToDB, 500);
            }
          }
        }
      });
    }
  }
}
module.exports = DynamoDBConnection;