// @ts-nocheck
const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const groupsSchema = new dynamoose.Schema({
    groupId: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    groupType: {
        type: String,
        required: true,
        enum: ['event', 'organization', 'organization_admin'],
        index: {
            name: 'groupType-index',
            global: true,
            project: true
        }
    },
    organizationId: {
        type: String,
        index: {
            name: 'organizationId-index',
            global: true,
            project: true
        }
    },
    organizationMetaData: {
        type: Object,
        schema: {
            name: {
                type: String
            },
            logo: {
                type: String
            }
        }
    },
    eventId: {
        type: String,
        index: {
            name: 'eventId-index',
            global: true,
            project: true
        }
    },
    eventMetaData: {
        type: Object,
        schema: {
            title: {
                type: String
            },
            startDateTime: {
                type: Date
            },
            endDateTime: {
                type: Date
            },
            image: {
                type: String
            }
        }
    },
    status: {
        type: String,
        enum: ['active', 'deleted', 'expired'],
        default: 'active'
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('Groups', groupsSchema);
