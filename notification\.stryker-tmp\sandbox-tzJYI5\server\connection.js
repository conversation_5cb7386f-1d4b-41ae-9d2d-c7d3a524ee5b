// @ts-nocheck
const AWS = require('aws-sdk');
const {
    DynamoDBClient,
    ListTablesCommand
} = require('@aws-sdk/client-dynamodb');
const dynamoose = require('dynamoose');

class DynamoDBConnection {
    static async connectToDB () {
        const dynamoDBConfig = {
            region: process.env.AWS_DB_REGION
        };
        if (process.env.LOCAL === 'true') {
            dynamoDBConfig.endpoint = process.env.DB_HOST;
        }
        const dynamodb = new AWS.DynamoDB(dynamoDBConfig);
        const ddb = new dynamoose.aws.ddb.DynamoDB({
            'credentials': {
                'accessKeyId': process.env.ACCESS_KEY_ID,
                'secretAccessKey': process.env.SECRET_ACCESS_KEY
            },
            'region': process.env.AWS_DB_REGION
        });
        dynamoose.aws.ddb.set(ddb);
        return new Promise(async (resolve) => {
            try {
                if (process.env.LOCAL !== 'true') {
                    const client = new DynamoDBClient(dynamoDBConfig);

                    const command = new ListTablesCommand({});
                    const response = await client.send(command);
                    const tableNames = response.TableNames;
                    CONSOLE_LOGGER.info('Connected to DynamoDB');
                    resolve(tableNames);
                } else {
                    const data = await dynamodb.listTables().promise();
                    CONSOLE_LOGGER.info('Connected to DynamoDB');
                    resolve(data.TableNames);
                }
            } catch (error) {
                CONSOLE_LOGGER.info('Error connecting to DynamoDB:', JSON.stringify(error));
                CONSOLE_LOGGER.error(
                    'DynamoDB connection unsuccessful, retry after 0.5 seconds.'
                );
                setTimeout(DynamoDBConnection.connectToDB, 500);
            }
        });
    }
}

module.exports = DynamoDBConnection;
