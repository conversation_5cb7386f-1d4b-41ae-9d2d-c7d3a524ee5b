// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
module.exports = stryMutAct_9fa48("1634") ? {} : (stryCov_9fa48("1634"), {
  // For AES, this is always 16
  IV_LENGTH: 16,
  LOG_LEVEL: stryMutAct_9fa48("1635") ? "" : (stryCov_9fa48("1635"), 'debug'),
  POST_EXPIRATION_DAYS: stryMutAct_9fa48("1636") ? "" : (stryCov_9fa48("1636"), 'PostExpirationDays'),
  PROFILE_PICTURE: stryMutAct_9fa48("1637") ? {} : (stryCov_9fa48("1637"), {
    MIN_SIZE: 5120,
    MAX_SIZE: 5242880,
    ALLOWED_TYPE: stryMutAct_9fa48("1638") ? [] : (stryCov_9fa48("1638"), [stryMutAct_9fa48("1639") ? "" : (stryCov_9fa48("1639"), 'image/jpg'), stryMutAct_9fa48("1640") ? "" : (stryCov_9fa48("1640"), 'image/jpeg'), stryMutAct_9fa48("1641") ? "" : (stryCov_9fa48("1641"), 'image/png')])
  }),
  USER_DOCUMENT_FILE: stryMutAct_9fa48("1642") ? {} : (stryCov_9fa48("1642"), {
    MIN_SIZE: 10240,
    MAX_SIZE: 5242880,
    ALLOWED_TYPE: stryMutAct_9fa48("1643") ? [] : (stryCov_9fa48("1643"), [stryMutAct_9fa48("1644") ? "" : (stryCov_9fa48("1644"), 'image/jpg'), stryMutAct_9fa48("1645") ? "" : (stryCov_9fa48("1645"), 'image/jpeg'), stryMutAct_9fa48("1646") ? "" : (stryCov_9fa48("1646"), 'image/png'), stryMutAct_9fa48("1647") ? "" : (stryCov_9fa48("1647"), 'application/pdf')])
  }),
  REGEX: stryMutAct_9fa48("1648") ? {} : (stryCov_9fa48("1648"), {
    EMAIL: stryMutAct_9fa48("1661") ? /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[^a-z]{2,6}$/ : stryMutAct_9fa48("1660") ? /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]$/ : stryMutAct_9fa48("1659") ? /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([^a-z]{1,6}\.)?[a-z]{2,6}$/ : stryMutAct_9fa48("1658") ? /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]\.)?[a-z]{2,6}$/ : stryMutAct_9fa48("1657") ? /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)[a-z]{2,6}$/ : stryMutAct_9fa48("1656") ? /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[^A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/ : stryMutAct_9fa48("1655") ? /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]\.([a-z]{1,6}\.)?[a-z]{2,6}$/ : stryMutAct_9fa48("1654") ? /^[A-Za-z0-9](\.?[^A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/ : stryMutAct_9fa48("1653") ? /^[A-Za-z0-9](\.[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/ : stryMutAct_9fa48("1652") ? /^[A-Za-z0-9](\.?[A-Za-z0-9_-])@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/ : stryMutAct_9fa48("1651") ? /^[^A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/ : stryMutAct_9fa48("1650") ? /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}/ : stryMutAct_9fa48("1649") ? /[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/ : (stryCov_9fa48("1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661"), /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/),
    FIRSTNAME: stryMutAct_9fa48("1665") ? /^[^a-zA-Z0-9,'~._^ -]*$/ : stryMutAct_9fa48("1664") ? /^[a-zA-Z0-9,'~._^ -]$/ : stryMutAct_9fa48("1663") ? /^[a-zA-Z0-9,'~._^ -]*/ : stryMutAct_9fa48("1662") ? /[a-zA-Z0-9,'~._^ -]*$/ : (stryCov_9fa48("1662", "1663", "1664", "1665"), /^[a-zA-Z0-9,'~._^ -]*$/),
    SURNAME: stryMutAct_9fa48("1669") ? /^[^a-zA-Z0-9,'~._^ -]*$/ : stryMutAct_9fa48("1668") ? /^[a-zA-Z0-9,'~._^ -]$/ : stryMutAct_9fa48("1667") ? /^[a-zA-Z0-9,'~._^ -]*/ : stryMutAct_9fa48("1666") ? /[a-zA-Z0-9,'~._^ -]*$/ : (stryCov_9fa48("1666", "1667", "1668", "1669"), /^[a-zA-Z0-9,'~._^ -]*$/),
    ALPHA_ONLY: stryMutAct_9fa48("1673") ? /^[^a-zA-Z']*$/ : stryMutAct_9fa48("1672") ? /^[a-zA-Z']$/ : stryMutAct_9fa48("1671") ? /^[a-zA-Z']*/ : stryMutAct_9fa48("1670") ? /[a-zA-Z']*$/ : (stryCov_9fa48("1670", "1671", "1672", "1673"), /^[a-zA-Z']*$/),
    ALPHA_SPECIAL_CHAR: stryMutAct_9fa48("1677") ? /^[^ A-Za-z0-9_@./#&+-]*$/ : stryMutAct_9fa48("1676") ? /^[ A-Za-z0-9_@./#&+-]$/ : stryMutAct_9fa48("1675") ? /^[ A-Za-z0-9_@./#&+-]*/ : stryMutAct_9fa48("1674") ? /[ A-Za-z0-9_@./#&+-]*$/ : (stryCov_9fa48("1674", "1675", "1676", "1677"), /^[ A-Za-z0-9_@./#&+-]*$/),
    ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: stryMutAct_9fa48("1681") ? /^[^ A-Za-z_@./#&+-]*$/ : stryMutAct_9fa48("1680") ? /^[ A-Za-z_@./#&+-]$/ : stryMutAct_9fa48("1679") ? /^[ A-Za-z_@./#&+-]*/ : stryMutAct_9fa48("1678") ? /[ A-Za-z_@./#&+-]*$/ : (stryCov_9fa48("1678", "1679", "1680", "1681"), /^[ A-Za-z_@./#&+-]*$/),
    FULL_ACCESS: stryMutAct_9fa48("1685") ? /^[<> ?//\\]+$/ : stryMutAct_9fa48("1684") ? /^[^<> ?//\\]$/ : stryMutAct_9fa48("1683") ? /^[^<> ?//\\]+/ : stryMutAct_9fa48("1682") ? /[^<> ?//\\]+$/ : (stryCov_9fa48("1682", "1683", "1684", "1685"), /^[^<> ?//\\]+$/),
    ALPHA_NUMARIC: stryMutAct_9fa48("1690") ? /^[\W@ ]+$/ : stryMutAct_9fa48("1689") ? /^[^\w@ ]+$/ : stryMutAct_9fa48("1688") ? /^[\w@ ]$/ : stryMutAct_9fa48("1687") ? /^[\w@ ]+/ : stryMutAct_9fa48("1686") ? /[\w@ ]+$/ : (stryCov_9fa48("1686", "1687", "1688", "1689", "1690"), /^[\w@ ]+$/),
    templateName: stryMutAct_9fa48("1694") ? /^[^ A-Za-z0-9_@./#&+-]*$/ : stryMutAct_9fa48("1693") ? /^[ A-Za-z0-9_@./#&+-]$/ : stryMutAct_9fa48("1692") ? /^[ A-Za-z0-9_@./#&+-]*/ : stryMutAct_9fa48("1691") ? /[ A-Za-z0-9_@./#&+-]*$/ : (stryCov_9fa48("1691", "1692", "1693", "1694"), /^[ A-Za-z0-9_@./#&+-]*$/),
    SUBJECT: stryMutAct_9fa48("1698") ? /^[^ A-Za-z0-9_@./#&+-]*$/ : stryMutAct_9fa48("1697") ? /^[ A-Za-z0-9_@./#&+-]$/ : stryMutAct_9fa48("1696") ? /^[ A-Za-z0-9_@./#&+-]*/ : stryMutAct_9fa48("1695") ? /[ A-Za-z0-9_@./#&+-]*$/ : (stryCov_9fa48("1695", "1696", "1697", "1698"), /^[ A-Za-z0-9_@./#&+-]*$/),
    URL: stryMutAct_9fa48("1706") ? /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([^-a-zA-Z0-9@:%+.~#?&//=]*)/ : stryMutAct_9fa48("1705") ? /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=])/ : stryMutAct_9fa48("1704") ? /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[^a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/ : stryMutAct_9fa48("1703") ? /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]\b([-a-zA-Z0-9@:%+.~#?&//=]*)/ : stryMutAct_9fa48("1702") ? /(http(s)?:\/\/www\.)?[^-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/ : stryMutAct_9fa48("1701") ? /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/ : stryMutAct_9fa48("1700") ? /(http(s):\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/ : stryMutAct_9fa48("1699") ? /(http(s)?:\/\/www\.)[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/ : (stryCov_9fa48("1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706"), /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/),
    MOBILE: stryMutAct_9fa48("1714") ? /^([+]\d{1,2})?\D{10}$/ : stryMutAct_9fa48("1713") ? /^([+]\d{1,2})?\d$/ : stryMutAct_9fa48("1712") ? /^([+]\D{1,2})?\d{10}$/ : stryMutAct_9fa48("1711") ? /^([+]\d)?\d{10}$/ : stryMutAct_9fa48("1710") ? /^([^+]\d{1,2})?\d{10}$/ : stryMutAct_9fa48("1709") ? /^([+]\d{1,2})\d{10}$/ : stryMutAct_9fa48("1708") ? /^([+]\d{1,2})?\d{10}/ : stryMutAct_9fa48("1707") ? /([+]\d{1,2})?\d{10}$/ : (stryCov_9fa48("1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714"), /^([+]\d{1,2})?\d{10}$/)
  }),
  OTPLENGTH: 6,
  STATUS: stryMutAct_9fa48("1715") ? {} : (stryCov_9fa48("1715"), {
    INACTIVE: stryMutAct_9fa48("1716") ? "" : (stryCov_9fa48("1716"), 'inactive'),
    ACTIVE: stryMutAct_9fa48("1717") ? "" : (stryCov_9fa48("1717"), 'active'),
    SUSPENDED: stryMutAct_9fa48("1718") ? "" : (stryCov_9fa48("1718"), 'suspended'),
    FREEZED: stryMutAct_9fa48("1719") ? "" : (stryCov_9fa48("1719"), 'freezed')
  }),
  ENVIRONMENT: stryMutAct_9fa48("1720") ? {} : (stryCov_9fa48("1720"), {
    TESTING: stryMutAct_9fa48("1721") ? "" : (stryCov_9fa48("1721"), 'testing'),
    LOCAL: stryMutAct_9fa48("1722") ? "" : (stryCov_9fa48("1722"), 'local'),
    DEV: stryMutAct_9fa48("1723") ? "" : (stryCov_9fa48("1723"), 'dev'),
    PRODUCTION: stryMutAct_9fa48("1724") ? "" : (stryCov_9fa48("1724"), 'production')
  }),
  EVENT_TYPES: stryMutAct_9fa48("1725") ? {} : (stryCov_9fa48("1725"), {
    EVENT: stryMutAct_9fa48("1726") ? "" : (stryCov_9fa48("1726"), 'event')
  }),
  TRIGGER: stryMutAct_9fa48("1727") ? {} : (stryCov_9fa48("1727"), {
    REQUESTED_BY: stryMutAct_9fa48("1728") ? "" : (stryCov_9fa48("1728"), 'requestedBy'),
    EVENT_SIGNUP: stryMutAct_9fa48("1729") ? "" : (stryCov_9fa48("1729"), 'eventSignup'),
    FUNDRAISER_SIGNUP: stryMutAct_9fa48("1730") ? "" : (stryCov_9fa48("1730"), 'fundraiserSignup'),
    EVENT_UPDATED: stryMutAct_9fa48("1731") ? "" : (stryCov_9fa48("1731"), 'eventSignupUpdate'),
    POST_CREATED: stryMutAct_9fa48("1732") ? "" : (stryCov_9fa48("1732"), 'postCreated'),
    BOOSTER_DONATION: stryMutAct_9fa48("1733") ? "" : (stryCov_9fa48("1733"), 'boosterDonation'),
    CONVERSATION: stryMutAct_9fa48("1734") ? "" : (stryCov_9fa48("1734"), 'conversation'),
    PERSONAL_CONVERSATION: stryMutAct_9fa48("1735") ? "" : (stryCov_9fa48("1735"), 'personalConversation')
  }),
  DEVELOPERS_EMAIL: stryMutAct_9fa48("1736") ? "" : (stryCov_9fa48("1736"), '<EMAIL>'),
  CONTACT_EMAIL: stryMutAct_9fa48("1737") ? "" : (stryCov_9fa48("1737"), '<EMAIL>'),
  SES_HOST: stryMutAct_9fa48("1738") ? "" : (stryCov_9fa48("1738"), 'email-smtp.eu-west-1.amazonaws.com'),
  ROLE: stryMutAct_9fa48("1739") ? {} : (stryCov_9fa48("1739"), {
    USER: 1,
    ADMIN: 4
  }),
  DEFAULT_PAGE_SIZE: 15,
  AWS_S3_PUBLIC_BUCKET: stryMutAct_9fa48("1740") ? "" : (stryCov_9fa48("1740"), 'vaalee-assets'),
  PAYMENT_STATUS: stryMutAct_9fa48("1741") ? {} : (stryCov_9fa48("1741"), {
    PENDING: stryMutAct_9fa48("1742") ? "" : (stryCov_9fa48("1742"), 'pending'),
    APPROVED: stryMutAct_9fa48("1743") ? "" : (stryCov_9fa48("1743"), 'approved'),
    CANCELLED: stryMutAct_9fa48("1744") ? "" : (stryCov_9fa48("1744"), 'cancelled'),
    PAYMENT_INITIATED: stryMutAct_9fa48("1745") ? "" : (stryCov_9fa48("1745"), 'payment-initiated')
  }),
  DEFAULT_NOTIFICATION_TITLE: stryMutAct_9fa48("1746") ? "" : (stryCov_9fa48("1746"), 'Alert'),
  ORG_ROLE: stryMutAct_9fa48("1747") ? {} : (stryCov_9fa48("1747"), {
    SUPER_ADMIN: stryMutAct_9fa48("1748") ? "" : (stryCov_9fa48("1748"), 'super admin'),
    ADMIN: stryMutAct_9fa48("1749") ? "" : (stryCov_9fa48("1749"), 'admin')
  }),
  NOTIFICATION_ROUTE: stryMutAct_9fa48("1750") ? {} : (stryCov_9fa48("1750"), {
    EVENT_DETAILS: stryMutAct_9fa48("1751") ? "" : (stryCov_9fa48("1751"), 'eventDetails'),
    FUNDRAISER_DETAILS: stryMutAct_9fa48("1752") ? "" : (stryCov_9fa48("1752"), 'fundraiserDetails')
  }),
  APP_NAME: stryMutAct_9fa48("1753") ? "" : (stryCov_9fa48("1753"), 'Vaalee'),
  CONTACT_US_SUBJECT: stryMutAct_9fa48("1754") ? "" : (stryCov_9fa48("1754"), 'New Contact Form Submission'),
  GROUP_CONVERSATION_TYPES: stryMutAct_9fa48("1755") ? {} : (stryCov_9fa48("1755"), {
    EVENT: stryMutAct_9fa48("1756") ? "" : (stryCov_9fa48("1756"), 'event'),
    ORGANIZATION: stryMutAct_9fa48("1757") ? "" : (stryCov_9fa48("1757"), 'organization'),
    ORGANIZATION_ADMIN: stryMutAct_9fa48("1758") ? "" : (stryCov_9fa48("1758"), 'organization_admin')
  }),
  GROUP_CONVERSATION_STATUS: stryMutAct_9fa48("1759") ? {} : (stryCov_9fa48("1759"), {
    ACTIVE: stryMutAct_9fa48("1760") ? "" : (stryCov_9fa48("1760"), 'active'),
    EXPIRED: stryMutAct_9fa48("1761") ? "" : (stryCov_9fa48("1761"), 'expired')
  }),
  GROUP_CONVERSATION_EXPIRATION_DAYS: 2,
  KEY_FOR_USER_EVENTS: stryMutAct_9fa48("1762") ? "" : (stryCov_9fa48("1762"), 'user-events'),
  KEY_FOR_USER_REGISTERED_EVENTS: stryMutAct_9fa48("1763") ? "" : (stryCov_9fa48("1763"), 'user-registered-events'),
  KEY_FOR_USER_CALENDAR_EVENTS: stryMutAct_9fa48("1764") ? "" : (stryCov_9fa48("1764"), 'user-calendar-events'),
  KEY_FOR_CHILD_EVENTS: stryMutAct_9fa48("1765") ? "" : (stryCov_9fa48("1765"), 'child-events'),
  KEY_FOR_CHILD_REGISTERED_EVENTS: stryMutAct_9fa48("1766") ? "" : (stryCov_9fa48("1766"), 'child-registered-events'),
  KEY_FOR_CHILD_CALENDAR_EVENTS: stryMutAct_9fa48("1767") ? "" : (stryCov_9fa48("1767"), 'child-calendar-events'),
  KEY_FOR_CHILD_DETAILS: stryMutAct_9fa48("1768") ? "" : (stryCov_9fa48("1768"), 'child-details'),
  KEY_FOR_EVENT_DETAILS: stryMutAct_9fa48("1769") ? "" : (stryCov_9fa48("1769"), 'event-details'),
  KEY_FOR_FUNDRAISER_DETAILS: stryMutAct_9fa48("1770") ? "" : (stryCov_9fa48("1770"), 'fundraiser-details'),
  KEY_FOR_POST_DETAILS: stryMutAct_9fa48("1771") ? "" : (stryCov_9fa48("1771"), 'post-details'),
  KEY_FOR_ORGANIZATION_DETAILS: stryMutAct_9fa48("1772") ? "" : (stryCov_9fa48("1772"), 'organization-details'),
  FEED_VERSION_PREFIX: stryMutAct_9fa48("1773") ? "" : (stryCov_9fa48("1773"), 'FeedVersionPrefix'),
  DB_BATCH_OPERATION_CHUNK_SIZE: stryMutAct_9fa48("1774") ? {} : (stryCov_9fa48("1774"), {
    BATCH_PUT: 25,
    BATCH_DELETE: 25,
    BATCH_GET: 100
  })
});