// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const CONSTANTS = require('../util/constants');
const Groups = require('../models/groups.model');
const MOMENT = require('moment');
class ConversationService {
  static async updateConversationGroups() {
    if (stryMutAct_9fa48("22")) {
      {}
    } else {
      stryCov_9fa48("22");
      try {
        if (stryMutAct_9fa48("23")) {
          {}
        } else {
          stryCov_9fa48("23");
          const groups = await Groups.query(stryMutAct_9fa48("24") ? "" : (stryCov_9fa48("24"), 'groupType')).eq(CONSTANTS.GROUP_CONVERSATION_TYPES.EVENT).using(stryMutAct_9fa48("25") ? "" : (stryCov_9fa48("25"), 'groupType-index')).where(stryMutAct_9fa48("26") ? "" : (stryCov_9fa48("26"), 'status')).eq(CONSTANTS.GROUP_CONVERSATION_STATUS.ACTIVE).exec();
          const expiredEventGroups = stryMutAct_9fa48("27") ? groups : (stryCov_9fa48("27"), groups.filter(stryMutAct_9fa48("28") ? () => undefined : (stryCov_9fa48("28"), group => MOMENT(stryMutAct_9fa48("29") ? group.eventMetaData.endDateTime : (stryCov_9fa48("29"), group.eventMetaData?.endDateTime)).add(CONSTANTS.GROUP_CONVERSATION_EXPIRATION_DAYS, stryMutAct_9fa48("30") ? "" : (stryCov_9fa48("30"), 'days')).isBefore(MOMENT()))));
          CONSOLE_LOGGER.info(stryMutAct_9fa48("31") ? "" : (stryCov_9fa48("31"), 'Expired event groups length'), expiredEventGroups.length);
          CONSOLE_LOGGER.info(stryMutAct_9fa48("32") ? "" : (stryCov_9fa48("32"), 'Expired event groups ids'), expiredEventGroups.map(stryMutAct_9fa48("33") ? () => undefined : (stryCov_9fa48("33"), group => group.groupId)));
          expiredEventGroups.forEach(group => {
            if (stryMutAct_9fa48("34")) {
              {}
            } else {
              stryCov_9fa48("34");
              group.status = CONSTANTS.GROUP_CONVERSATION_STATUS.EXPIRED;
            }
          });
          if (stryMutAct_9fa48("38") ? expiredEventGroups.length <= 0 : stryMutAct_9fa48("37") ? expiredEventGroups.length >= 0 : stryMutAct_9fa48("36") ? false : stryMutAct_9fa48("35") ? true : (stryCov_9fa48("35", "36", "37", "38"), expiredEventGroups.length > 0)) {
            if (stryMutAct_9fa48("39")) {
              {}
            } else {
              stryCov_9fa48("39");
              await this.batchPutInChunks(stryMutAct_9fa48("40") ? {} : (stryCov_9fa48("40"), {
                model: Groups,
                data: expiredEventGroups
              }));
            }
          }
        }
      } catch (error) {
        if (stryMutAct_9fa48("41")) {
          {}
        } else {
          stryCov_9fa48("41");
          CONSOLE_LOGGER.error(stryMutAct_9fa48("42") ? "" : (stryCov_9fa48("42"), 'Error updating conversation groups'), error);
        }
      }
    }
  }

  /**
   * @desc Generic function to perform batchPut in chunks
   * <AUTHOR>
   * @since 25/07/2025
   * @param {Object} options
   * @param {Object} options.model - Dynamoose model to use
   * @param {Array} options.data - Data to put
   * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT] - Max chunk size per request
   */
  static async batchPutInChunks({
    model,
    data,
    chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT
  }) {
    if (stryMutAct_9fa48("43")) {
      {}
    } else {
      stryCov_9fa48("43");
      if (stryMutAct_9fa48("46") ? false : stryMutAct_9fa48("45") ? true : stryMutAct_9fa48("44") ? Array.isArray(data) : (stryCov_9fa48("44", "45", "46"), !Array.isArray(data))) {
        if (stryMutAct_9fa48("47")) {
          {}
        } else {
          stryCov_9fa48("47");
          return;
        }
      }
      data = stryMutAct_9fa48("48") ? data : (stryCov_9fa48("48"), data.filter(Boolean));
      if (stryMutAct_9fa48("51") ? data.length !== 0 : stryMutAct_9fa48("50") ? false : stryMutAct_9fa48("49") ? true : (stryCov_9fa48("49", "50", "51"), data.length === 0)) {
        if (stryMutAct_9fa48("52")) {
          {}
        } else {
          stryCov_9fa48("52");
          return;
        }
      }
      for (let i = 0; stryMutAct_9fa48("55") ? i >= data.length : stryMutAct_9fa48("54") ? i <= data.length : stryMutAct_9fa48("53") ? false : (stryCov_9fa48("53", "54", "55"), i < data.length); stryMutAct_9fa48("56") ? i -= chunkSize : (stryCov_9fa48("56"), i += chunkSize)) {
        if (stryMutAct_9fa48("57")) {
          {}
        } else {
          stryCov_9fa48("57");
          const dataChunk = stryMutAct_9fa48("58") ? data : (stryCov_9fa48("58"), data.slice(i, stryMutAct_9fa48("59") ? i - chunkSize : (stryCov_9fa48("59"), i + chunkSize)));
          await model.batchPut(dataChunk);
        }
      }
    }
  }
}
module.exports = ConversationService;