// @ts-nocheck
const axios = require('axios');
const { google } = require('googleapis');
const stringify = require('json-stringify-safe');
const UploadService = require('./uploadService');

/**
 * This class represents all the notification services functions
 */
class Notification {
    /**
     * @desc This function is being used to get an access token
     * <AUTHOR>
     * @since 02/02/2024
     */
    static async getAccessToken () {
        return new Promise((resolve, reject) => {
            const jwtClient = new google.auth.JWT(
                process.env.NOTIFICATION_CLIENT_EMAIL,
                null,
                process.env.NOTIFICATION_PRIVATE_KEY.split(String.raw`\n`).join('\n'),
                ['https://www.googleapis.com/auth/firebase.messaging'],
                null
            );
            jwtClient.authorize((err, tokens) => {
                if (err) {
                    CONSOLE_LOGGER.error('Error getting access token', err);
                    reject(err);
                    return;
                }
                resolve(tokens.access_token);
            });
        });
    }

    /**
     * @desc This function is being used to send a notification
     * <AUTHOR>
     * @since 02/02/2024
     * @param {Object} notificationObject
     */
    static async sendNotification (notificationObject) {
        const accessToken = await this.getAccessToken();
        const child = notificationObject.data?.child && JSON.parse(notificationObject.data.child);
        if (child && child.photoURL) {
            child.photoURL = await UploadService.getSignedUrl(child.photoURL);
            notificationObject.data.child = JSON.stringify(child);
        }
        const message = {
            message: notificationObject
        };
        CONSOLE_LOGGER.info(`Notification message: in sendNotification ${stringify(message)}`);
        try {
            const response = await axios.post(`https://fcm.googleapis.com/v1/projects/${process.env.NOTIFICATION_PROJECT_ID}/messages:send`,
                message,
                {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });
            CONSOLE_LOGGER.info('Notification sent successfully: in sendNotification', stringify(response.data));
            return response.data;
        } catch (error) {
            CONSOLE_LOGGER.error('Error sending notification: in sendNotification',
                stringify(error), 'and error.message: ', error.message, 'and error.response', stringify(error.response));
            error.isError = true;
            return error;
        }
    }

    /**
     * @desc This function is being used to create a notification object
     * <AUTHOR>
     * @since 02/02/2024
     * @param {String} topic
     * @param {String} title
     * @param {String} body
     * @param {Object} data
     * @param {String} clickAction
     */
    static createNotificationObject ({ topic, title, body, data, clickAction, collapseKey }) {
        const toReturn = {
            topic,
            data,
            notification: {
                title,
                body
            },
            android: {
                priority: 'high',
                notification: {}
            },
            apns: {
                headers: {
                    'apns-priority': '10'
                },
                payload: {
                    aps: {
                        category: clickAction,
                        badge: 1
                    }
                }
            }
        };

        if (collapseKey) {
            toReturn.android.collapseKey = collapseKey;
        }

        return toReturn;
    }

}

module.exports = Notification;
