// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const NotificationModel = require('../../models/notification.model');
const ChildModel = require('../../models/child.model');
const OrganizationModel = require('../../models/organization.model');
const EventModel = require('../../models/event.model');
const FundraiserModel = require('../../models/fundraiser.model');
const EventSignupModel = require('../../models/eventSignup.model');
const FundraiserSignupModel = require('../../models/fundraiserSignup.model');
const ChildOrganizationMappingModel = require('../../models/childOrganizationMapping.model');
const NotificationValidator = require('./notificationValidator');
const UploadService = require('../../util/uploadService');
const GeneralError = require('../../util/GeneralError');
const Notification = require('../../util/notification');
const EmailService = require('../../util/sendEmail');
const GoogleSheetsService = require('../../util/googleSheets');
const CONSTANTS = require('../../util/constants');

/**
 * Class represents services for notification.
 */
class NotificationService {
  /**
   * @desc This function is being used to get notification list
   * <AUTHOR>
   * @since 31/01/2024
   * @param {Object} req Request
   * @param {Object} user User
   * @param {Object} locale Locale
   */
  static async getNotificationList(req, user) {
    if (stryMutAct_9fa48("1055")) {
      {}
    } else {
      stryCov_9fa48("1055");
      const {
        nextIndex,
        nextCreatedAt,
        pageSize
      } = req.query;
      const page = stryMutAct_9fa48("1058") ? Number(pageSize) && CONSTANTS.DEFAULT_PAGE_SIZE : stryMutAct_9fa48("1057") ? false : stryMutAct_9fa48("1056") ? true : (stryCov_9fa48("1056", "1057", "1058"), Number(pageSize) || CONSTANTS.DEFAULT_PAGE_SIZE);
      let query = stryMutAct_9fa48("1059") ? NotificationModel.query('userId').eq(user.id).using('userId-index').limit(page + 1) : (stryCov_9fa48("1059"), NotificationModel.query(stryMutAct_9fa48("1060") ? "" : (stryCov_9fa48("1060"), 'userId')).eq(user.id).using(stryMutAct_9fa48("1061") ? "" : (stryCov_9fa48("1061"), 'userId-index')).sort(stryMutAct_9fa48("1062") ? "" : (stryCov_9fa48("1062"), 'descending')).limit(stryMutAct_9fa48("1063") ? page - 1 : (stryCov_9fa48("1063"), page + 1)));
      if (stryMutAct_9fa48("1066") ? nextIndex || !isNaN(nextCreatedAt) : stryMutAct_9fa48("1065") ? false : stryMutAct_9fa48("1064") ? true : (stryCov_9fa48("1064", "1065", "1066"), nextIndex && (stryMutAct_9fa48("1067") ? isNaN(nextCreatedAt) : (stryCov_9fa48("1067"), !isNaN(nextCreatedAt))))) {
        if (stryMutAct_9fa48("1068")) {
          {}
        } else {
          stryCov_9fa48("1068");
          query = query.startAt(stryMutAct_9fa48("1069") ? {} : (stryCov_9fa48("1069"), {
            userId: user.id,
            id: nextIndex,
            createdAt: Number(nextCreatedAt)
          }));
        }
      }
      const data = await query.exec();
      let newNextIndex = null;
      const notifications = data;
      if (stryMutAct_9fa48("1073") ? data.length <= page : stryMutAct_9fa48("1072") ? data.length >= page : stryMutAct_9fa48("1071") ? false : stryMutAct_9fa48("1070") ? true : (stryCov_9fa48("1070", "1071", "1072", "1073"), data.length > page)) {
        if (stryMutAct_9fa48("1074")) {
          {}
        } else {
          stryCov_9fa48("1074");
          newNextIndex = stryMutAct_9fa48("1075") ? {} : (stryCov_9fa48("1075"), {
            id: data[stryMutAct_9fa48("1076") ? page + 1 : (stryCov_9fa48("1076"), page - 1)].id,
            createdAt: MOMENT(data[stryMutAct_9fa48("1077") ? page + 1 : (stryCov_9fa48("1077"), page - 1)].createdAt).valueOf()
          });
          notifications.pop();
        }
      }
      const notificationList = stryMutAct_9fa48("1078") ? ["Stryker was here"] : (stryCov_9fa48("1078"), []);
      for (const notification of notifications) {
        if (stryMutAct_9fa48("1079")) {
          {}
        } else {
          stryCov_9fa48("1079");
          notificationList.push(await this.formatNotificationResponse(notification));
        }
      }
      return stryMutAct_9fa48("1080") ? {} : (stryCov_9fa48("1080"), {
        nextIndex: newNextIndex ? newNextIndex.id : null,
        nextCreatedAt: newNextIndex ? newNextIndex.createdAt : null,
        notifications: notificationList
      });
    }
  }

  /**
   * @desc This function is being used to create notification
   * <AUTHOR>
   * @since 31/01/2024
   * @param {Object} req Request
   * @param {Object} user User
   * @param {Object} locale Locale
   */
  static async createNotification(req, user, locale) {
    if (stryMutAct_9fa48("1081")) {
      {}
    } else {
      stryCov_9fa48("1081");
      const Validator = new NotificationValidator(req.body, locale);
      Validator.validateCreateNotification();
      const {
        title,
        description,
        associatedChildId,
        notificationAction,
        payload
      } = req.body;
      if (stryMutAct_9fa48("1084") ? false : stryMutAct_9fa48("1083") ? true : stryMutAct_9fa48("1082") ? user.children.includes(associatedChildId) : (stryCov_9fa48("1082", "1083", "1084"), !user.children.includes(associatedChildId))) {
        if (stryMutAct_9fa48("1085")) {
          {}
        } else {
          stryCov_9fa48("1085");
          throw new GeneralError(locale(MESSAGES.CHILD_NOT_FOUND), 404);
        }
      }
      const notification = new NotificationModel(stryMutAct_9fa48("1086") ? {} : (stryCov_9fa48("1086"), {
        userId: user.id,
        title,
        description,
        associatedChildId,
        notificationAction,
        payload
      }));
      const savedNotification = await notification.save();
      return await this.formatNotificationResponse(savedNotification);
    }
  }

  /**
   * @desc This function is being used to update notification read status
   * <AUTHOR>
   * @since 31/01/2024
   * @param {Object} req Request
   * @param {Object} user User
   * @param {Object} locale Locale
   */
  static async updateNotification(req, user, locale) {
    if (stryMutAct_9fa48("1087")) {
      {}
    } else {
      stryCov_9fa48("1087");
      const Validator = new NotificationValidator(req.body, locale, req.query);
      Validator.validateUpdateNotification();
      const {
        notificationId
      } = req.query;
      const notification = await NotificationModel.get(notificationId);
      if (stryMutAct_9fa48("1090") ? false : stryMutAct_9fa48("1089") ? true : stryMutAct_9fa48("1088") ? notification : (stryCov_9fa48("1088", "1089", "1090"), !notification)) {
        if (stryMutAct_9fa48("1091")) {
          {}
        } else {
          stryCov_9fa48("1091");
          throw new GeneralError(locale(MESSAGES.NOTIFICATION_NOT_FOUND), 404);
        }
      }
      if (stryMutAct_9fa48("1094") ? notification.userId === user.id : stryMutAct_9fa48("1093") ? false : stryMutAct_9fa48("1092") ? true : (stryCov_9fa48("1092", "1093", "1094"), notification.userId !== user.id)) {
        if (stryMutAct_9fa48("1095")) {
          {}
        } else {
          stryCov_9fa48("1095");
          throw new GeneralError(locale(MESSAGES.NOTIFICATION_NOT_FOUND), 404);
        }
      }
      if (stryMutAct_9fa48("1097") ? false : stryMutAct_9fa48("1096") ? true : (stryCov_9fa48("1096", "1097"), notification.readStatus)) {
        if (stryMutAct_9fa48("1098")) {
          {}
        } else {
          stryCov_9fa48("1098");
          throw new GeneralError(locale(MESSAGES.NOTIFICATION_ALREADY_READ), 200);
        }
      }
      notification.readStatus = stryMutAct_9fa48("1099") ? false : (stryCov_9fa48("1099"), true);
      const updatedNotification = await notification.save();
      return await this.formatNotificationResponse(updatedNotification);
    }
  }

  /**
   * @desc This function is being used to send notification to user or group of users
   * <AUTHOR>
   * @param {Object} req Request
   * @param {Object} user User
   * @param {Object} locale Locale
   */
  static async sendNotification(req, user, locale) {
    if (stryMutAct_9fa48("1100")) {
      {}
    } else {
      stryCov_9fa48("1100");
      const Validator = new NotificationValidator(req.body, locale);
      Validator.validateSendNotification();
      const {
        title,
        description,
        children,
        allParticipants,
        allOrgChildren,
        eventId,
        organizationId,
        isFundraiser = stryMutAct_9fa48("1101") ? true : (stryCov_9fa48("1101"), false)
      } = req.body;
      const orgAssociation = await this.validateOrganizationAssociation(organizationId, user);
      if (stryMutAct_9fa48("1103") ? false : stryMutAct_9fa48("1102") ? true : (stryCov_9fa48("1102", "1103"), NotificationService.isOrgAdmin(orgAssociation))) {
        if (stryMutAct_9fa48("1104")) {
          {}
        } else {
          stryCov_9fa48("1104");
          throw new GeneralError(MESSAGES.ACCESS_DENIED, 403);
        }
      }
      const notificationEntries = stryMutAct_9fa48("1105") ? ["Stryker was here"] : (stryCov_9fa48("1105"), []);
      const notificationPromises = stryMutAct_9fa48("1106") ? ["Stryker was here"] : (stryCov_9fa48("1106"), []);
      if (stryMutAct_9fa48("1108") ? false : stryMutAct_9fa48("1107") ? true : (stryCov_9fa48("1107", "1108"), allOrgChildren)) {
        if (stryMutAct_9fa48("1109")) {
          {}
        } else {
          stryCov_9fa48("1109");
          const children = await ChildOrganizationMappingModel.query(stryMutAct_9fa48("1110") ? "" : (stryCov_9fa48("1110"), 'organizationId')).eq(organizationId).using(stryMutAct_9fa48("1111") ? "" : (stryCov_9fa48("1111"), 'organizationId-index')).exec();
          const childrenData = await this.getChildData(children.map(stryMutAct_9fa48("1112") ? () => undefined : (stryCov_9fa48("1112"), child => child.childId)));
          await this.getNotificationEntriesAndPromises(stryMutAct_9fa48("1113") ? {} : (stryCov_9fa48("1113"), {
            children: childrenData,
            notificationEntries,
            notificationPromises,
            description,
            title,
            eventId: null,
            isFundraiser,
            signups: stryMutAct_9fa48("1114") ? ["Stryker was here"] : (stryCov_9fa48("1114"), [])
          }));
        }
      } else if (stryMutAct_9fa48("1116") ? false : stryMutAct_9fa48("1115") ? true : (stryCov_9fa48("1115", "1116"), allParticipants)) {
        if (stryMutAct_9fa48("1117")) {
          {}
        } else {
          stryCov_9fa48("1117");
          let eventSignups = isFundraiser ? await FundraiserSignupModel.query(stryMutAct_9fa48("1118") ? "" : (stryCov_9fa48("1118"), 'eventId')).eq(eventId).using(stryMutAct_9fa48("1119") ? "" : (stryCov_9fa48("1119"), 'eventId-index')).exec() : await EventSignupModel.query(stryMutAct_9fa48("1120") ? "" : (stryCov_9fa48("1120"), 'eventId')).eq(eventId).using(stryMutAct_9fa48("1121") ? "" : (stryCov_9fa48("1121"), 'eventId-index')).exec();
          eventSignups = stryMutAct_9fa48("1122") ? eventSignups : (stryCov_9fa48("1122"), eventSignups.filter(stryMutAct_9fa48("1123") ? () => undefined : (stryCov_9fa48("1123"), signup => stryMutAct_9fa48("1126") ? signup.paymentDetails.paymentStatus === CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED : stryMutAct_9fa48("1125") ? false : stryMutAct_9fa48("1124") ? true : (stryCov_9fa48("1124", "1125", "1126"), signup.paymentDetails.paymentStatus !== CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED))));
          const childIds = eventSignups.map(stryMutAct_9fa48("1127") ? () => undefined : (stryCov_9fa48("1127"), signup => signup.childId));
          const uniqueChildIds = stryMutAct_9fa48("1128") ? [] : (stryCov_9fa48("1128"), [...new Set(childIds)]);
          const childrenData = await this.getChildData(uniqueChildIds);
          await this.getNotificationEntriesAndPromises(stryMutAct_9fa48("1129") ? {} : (stryCov_9fa48("1129"), {
            children: childrenData,
            notificationEntries,
            notificationPromises,
            description,
            title,
            eventId,
            isFundraiser,
            signups: eventSignups
          }));
        }
      } else {
        if (stryMutAct_9fa48("1130")) {
          {}
        } else {
          stryCov_9fa48("1130");
          let confirmedSignups = stryMutAct_9fa48("1131") ? ["Stryker was here"] : (stryCov_9fa48("1131"), []);
          if (stryMutAct_9fa48("1133") ? false : stryMutAct_9fa48("1132") ? true : (stryCov_9fa48("1132", "1133"), isFundraiser)) {
            if (stryMutAct_9fa48("1134")) {
              {}
            } else {
              stryCov_9fa48("1134");
              const signupIds = stryMutAct_9fa48("1135") ? children.map(child => child.fundraiserSignupId).map(id => ({
                id
              })) : (stryCov_9fa48("1135"), children.map(stryMutAct_9fa48("1136") ? () => undefined : (stryCov_9fa48("1136"), child => child.fundraiserSignupId)).filter(Boolean).map(stryMutAct_9fa48("1137") ? () => undefined : (stryCov_9fa48("1137"), id => stryMutAct_9fa48("1138") ? {} : (stryCov_9fa48("1138"), {
                id
              }))));
              const allSignups = stryMutAct_9fa48("1139") ? ["Stryker was here"] : (stryCov_9fa48("1139"), []);
              if (stryMutAct_9fa48("1143") ? signupIds.length <= 0 : stryMutAct_9fa48("1142") ? signupIds.length >= 0 : stryMutAct_9fa48("1141") ? false : stryMutAct_9fa48("1140") ? true : (stryCov_9fa48("1140", "1141", "1142", "1143"), signupIds.length > 0)) {
                if (stryMutAct_9fa48("1144")) {
                  {}
                } else {
                  stryCov_9fa48("1144");
                  allSignups.push(...(await this.getFundraiserSignupData(signupIds)));
                }
              }
              confirmedSignups = stryMutAct_9fa48("1145") ? allSignups : (stryCov_9fa48("1145"), allSignups.filter(stryMutAct_9fa48("1146") ? () => undefined : (stryCov_9fa48("1146"), signup => stryMutAct_9fa48("1149") ? signup.paymentDetails.paymentStatus === CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED : stryMutAct_9fa48("1148") ? false : stryMutAct_9fa48("1147") ? true : (stryCov_9fa48("1147", "1148", "1149"), signup.paymentDetails.paymentStatus !== CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED))));
            }
          }
          const childIds = stryMutAct_9fa48("1150") ? [] : (stryCov_9fa48("1150"), [...new Set(children.map(stryMutAct_9fa48("1151") ? () => undefined : (stryCov_9fa48("1151"), child => child.childId)))]);
          const childData = await this.getChildData(childIds);
          await this.getNotificationEntriesAndPromises(stryMutAct_9fa48("1152") ? {} : (stryCov_9fa48("1152"), {
            children: childData,
            notificationEntries,
            notificationPromises,
            description,
            title,
            eventId,
            isFundraiser,
            signups: confirmedSignups
          }));
        }
      }
      await this.batchPutInChunks(stryMutAct_9fa48("1153") ? {} : (stryCov_9fa48("1153"), {
        model: NotificationModel,
        data: notificationEntries
      }));
      await Promise.all(notificationPromises);
    }
  }

  /**
   * @desc Generic function to perform batchPut in chunks
   * <AUTHOR>
   * @since 25/07/2025
   * @param {Object} options
   * @param {Object} options.model - Dynamoose model to use
   * @param {Array} options.data - Data to put
   * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT] - Max chunk size per request
   */
  static async batchPutInChunks({
    model,
    data,
    chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT
  }) {
    if (stryMutAct_9fa48("1154")) {
      {}
    } else {
      stryCov_9fa48("1154");
      if (stryMutAct_9fa48("1157") ? false : stryMutAct_9fa48("1156") ? true : stryMutAct_9fa48("1155") ? Array.isArray(data) : (stryCov_9fa48("1155", "1156", "1157"), !Array.isArray(data))) {
        if (stryMutAct_9fa48("1158")) {
          {}
        } else {
          stryCov_9fa48("1158");
          return;
        }
      }
      data = stryMutAct_9fa48("1159") ? data : (stryCov_9fa48("1159"), data.filter(Boolean));
      if (stryMutAct_9fa48("1162") ? data.length !== 0 : stryMutAct_9fa48("1161") ? false : stryMutAct_9fa48("1160") ? true : (stryCov_9fa48("1160", "1161", "1162"), data.length === 0)) {
        if (stryMutAct_9fa48("1163")) {
          {}
        } else {
          stryCov_9fa48("1163");
          return;
        }
      }
      for (let i = 0; stryMutAct_9fa48("1166") ? i >= data.length : stryMutAct_9fa48("1165") ? i <= data.length : stryMutAct_9fa48("1164") ? false : (stryCov_9fa48("1164", "1165", "1166"), i < data.length); stryMutAct_9fa48("1167") ? i -= chunkSize : (stryCov_9fa48("1167"), i += chunkSize)) {
        if (stryMutAct_9fa48("1168")) {
          {}
        } else {
          stryCov_9fa48("1168");
          const dataChunk = stryMutAct_9fa48("1169") ? data : (stryCov_9fa48("1169"), data.slice(i, stryMutAct_9fa48("1170") ? i - chunkSize : (stryCov_9fa48("1170"), i + chunkSize)));
          await model.batchPut(dataChunk);
        }
      }
    }
  }

  /**
   * @desc This function checks if the user is an organization admin
   * <AUTHOR>
   * @since 18/07/2025
   * @param {Object} orgAssociation Organization association object
   * @return {Boolean} True if user is not an admin or super admin, false otherwise
   */
  static isOrgAdmin(orgAssociation) {
    if (stryMutAct_9fa48("1171")) {
      {}
    } else {
      stryCov_9fa48("1171");
      return stryMutAct_9fa48("1174") ? orgAssociation.role !== CONSTANTS.ORG_ROLE.SUPER_ADMIN || orgAssociation.role !== CONSTANTS.ORG_ROLE.ADMIN : stryMutAct_9fa48("1173") ? false : stryMutAct_9fa48("1172") ? true : (stryCov_9fa48("1172", "1173", "1174"), (stryMutAct_9fa48("1176") ? orgAssociation.role === CONSTANTS.ORG_ROLE.SUPER_ADMIN : stryMutAct_9fa48("1175") ? true : (stryCov_9fa48("1175", "1176"), orgAssociation.role !== CONSTANTS.ORG_ROLE.SUPER_ADMIN)) && (stryMutAct_9fa48("1178") ? orgAssociation.role === CONSTANTS.ORG_ROLE.ADMIN : stryMutAct_9fa48("1177") ? true : (stryCov_9fa48("1177", "1178"), orgAssociation.role !== CONSTANTS.ORG_ROLE.ADMIN)));
    }
  }

  /**
   * @desc Generic function to perform batchGet in chunks
   * <AUTHOR>
   * @since 21/07/2025
   * @param {Object} options
   * @param {Array} options.ids - List of IDs to fetch
   * @param {Object} options.model - Dynamoose model to use
   * @param {Array} [options.attributes] - Attributes to fetch (optional)
   * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET] - Max chunk size per request
   * @returns {Promise<Array>} Combined result of all batchGets
   */
  static async batchGetInChunks({
    ids,
    model,
    attributes,
    chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET
  }) {
    if (stryMutAct_9fa48("1179")) {
      {}
    } else {
      stryCov_9fa48("1179");
      if (stryMutAct_9fa48("1182") ? !Array.isArray(ids) && ids.length === 0 : stryMutAct_9fa48("1181") ? false : stryMutAct_9fa48("1180") ? true : (stryCov_9fa48("1180", "1181", "1182"), (stryMutAct_9fa48("1183") ? Array.isArray(ids) : (stryCov_9fa48("1183"), !Array.isArray(ids))) || (stryMutAct_9fa48("1185") ? ids.length !== 0 : stryMutAct_9fa48("1184") ? false : (stryCov_9fa48("1184", "1185"), ids.length === 0)))) {
        if (stryMutAct_9fa48("1186")) {
          {}
        } else {
          stryCov_9fa48("1186");
          return stryMutAct_9fa48("1187") ? ["Stryker was here"] : (stryCov_9fa48("1187"), []);
        }
      }
      const result = stryMutAct_9fa48("1188") ? ["Stryker was here"] : (stryCov_9fa48("1188"), []);
      for (let i = 0; stryMutAct_9fa48("1191") ? i >= ids.length : stryMutAct_9fa48("1190") ? i <= ids.length : stryMutAct_9fa48("1189") ? false : (stryCov_9fa48("1189", "1190", "1191"), i < ids.length); stryMutAct_9fa48("1192") ? i -= chunkSize : (stryCov_9fa48("1192"), i += chunkSize)) {
        if (stryMutAct_9fa48("1193")) {
          {}
        } else {
          stryCov_9fa48("1193");
          const chunk = stryMutAct_9fa48("1194") ? ids : (stryCov_9fa48("1194"), ids.slice(i, stryMutAct_9fa48("1195") ? i - chunkSize : (stryCov_9fa48("1195"), i + chunkSize)));
          const dataChunk = await model.batchGet(chunk, attributes ? stryMutAct_9fa48("1196") ? {} : (stryCov_9fa48("1196"), {
            attributes
          }) : undefined);
          result.push(...dataChunk);
        }
      }
      return result;
    }
  }

  /**
   * @desc Get fundraiser signup data in chunks
   */
  static async getFundraiserSignupData(signupIds) {
    if (stryMutAct_9fa48("1197")) {
      {}
    } else {
      stryCov_9fa48("1197");
      return this.batchGetInChunks(stryMutAct_9fa48("1198") ? {} : (stryCov_9fa48("1198"), {
        ids: signupIds,
        model: FundraiserSignupModel,
        attributes: stryMutAct_9fa48("1199") ? [] : (stryCov_9fa48("1199"), [stryMutAct_9fa48("1200") ? "" : (stryCov_9fa48("1200"), 'id'), stryMutAct_9fa48("1201") ? "" : (stryCov_9fa48("1201"), 'childId'), stryMutAct_9fa48("1202") ? "" : (stryCov_9fa48("1202"), 'paymentDetails')])
      }));
    }
  }

  /**
   * @desc Get child data in chunks
   */
  static async getChildData(childIds) {
    if (stryMutAct_9fa48("1203")) {
      {}
    } else {
      stryCov_9fa48("1203");
      return this.batchGetInChunks(stryMutAct_9fa48("1204") ? {} : (stryCov_9fa48("1204"), {
        ids: childIds,
        model: ChildModel
        // No attributes passed, fetches all
      }));
    }
  }

  /**
   * @desc This function is being used to validate organization association
   * <AUTHOR>
   * @since 21/02/2024
   * @param {String} organizationId organization id
   */
  static async validateOrganizationAssociation(organizationId, user) {
    if (stryMutAct_9fa48("1205")) {
      {}
    } else {
      stryCov_9fa48("1205");
      const orgAssociation = user.associatedOrganizations.find(stryMutAct_9fa48("1206") ? () => undefined : (stryCov_9fa48("1206"), org => stryMutAct_9fa48("1209") ? org.organizationId !== organizationId : stryMutAct_9fa48("1208") ? false : stryMutAct_9fa48("1207") ? true : (stryCov_9fa48("1207", "1208", "1209"), org.organizationId === organizationId)));
      if (stryMutAct_9fa48("1212") ? false : stryMutAct_9fa48("1211") ? true : stryMutAct_9fa48("1210") ? orgAssociation : (stryCov_9fa48("1210", "1211", "1212"), !orgAssociation)) {
        if (stryMutAct_9fa48("1213")) {
          {}
        } else {
          stryCov_9fa48("1213");
          throw new GeneralError(MESSAGES.ORGANIZATION_NOT_FOUND, 404);
        }
      }
      return orgAssociation;
    }
  }

  /**
   * @desc This function prepares notification entries and promises for a list of children,
   * handling both single and multiple notifications
   * <AUTHOR>
   * @since 01/02/2024
   * @param {Object} options
   * @param {Array} options.children
   * @param {Array} options.notificationEntries
   * @param {Array} options.notificationPromises
   * @param {String} options.title
   * @param {String} options.description
   * @param {String|null} options.eventId
   * @param {Boolean} options.isFundraiser
   * @param {Array} [options.signups=[]]
   */
  static async getNotificationEntriesAndPromises({
    children,
    notificationEntries,
    notificationPromises,
    description,
    title,
    eventId,
    isFundraiser,
    signups = stryMutAct_9fa48("1214") ? ["Stryker was here"] : (stryCov_9fa48("1214"), [])
  }) {
    if (stryMutAct_9fa48("1215")) {
      {}
    } else {
      stryCov_9fa48("1215");
      title = stryMutAct_9fa48("1218") ? title && CONSTANTS.DEFAULT_NOTIFICATION_TITLE : stryMutAct_9fa48("1217") ? false : stryMutAct_9fa48("1216") ? true : (stryCov_9fa48("1216", "1217", "1218"), title || CONSTANTS.DEFAULT_NOTIFICATION_TITLE);
      const eventData = eventId ? await (isFundraiser ? FundraiserModel.get(eventId, stryMutAct_9fa48("1219") ? {} : (stryCov_9fa48("1219"), {
        attributes: stryMutAct_9fa48("1220") ? [] : (stryCov_9fa48("1220"), [stryMutAct_9fa48("1221") ? "" : (stryCov_9fa48("1221"), 'id'), stryMutAct_9fa48("1222") ? "" : (stryCov_9fa48("1222"), 'startDate'), stryMutAct_9fa48("1223") ? "" : (stryCov_9fa48("1223"), 'endDate')])
      })) : EventModel.get(eventId, stryMutAct_9fa48("1224") ? {} : (stryCov_9fa48("1224"), {
        attributes: stryMutAct_9fa48("1225") ? [] : (stryCov_9fa48("1225"), [stryMutAct_9fa48("1226") ? "" : (stryCov_9fa48("1226"), 'id'), stryMutAct_9fa48("1227") ? "" : (stryCov_9fa48("1227"), 'details')])
      }))) : null;
      const getPayload = (child, signup = null) => {
        if (stryMutAct_9fa48("1228")) {
          {}
        } else {
          stryCov_9fa48("1228");
          const childData = stryMutAct_9fa48("1229") ? {} : (stryCov_9fa48("1229"), {
            id: child.id,
            firstName: child.firstName,
            lastName: child.lastName,
            associatedColor: child.associatedColor,
            photoURL: child.photoURL
          });
          const details = eventData ? stryMutAct_9fa48("1230") ? {} : (stryCov_9fa48("1230"), {
            associatedChild: stryMutAct_9fa48("1231") ? {} : (stryCov_9fa48("1231"), {
              id: child.id
            }),
            id: eventData.id,
            score: MOMENT(isFundraiser ? eventData.startDate : eventData.details.startDateTime).valueOf(),
            startDateTime: MOMENT(isFundraiser ? eventData.startDate : eventData.details.startDateTime).valueOf(),
            endDateTime: MOMENT(isFundraiser ? eventData.endDate : eventData.details.endDateTime).valueOf(),
            isFundraiser,
            ...(stryMutAct_9fa48("1234") ? isFundraiser || {
              fundraiserSignupId: signup?.id || null
            } : stryMutAct_9fa48("1233") ? false : stryMutAct_9fa48("1232") ? true : (stryCov_9fa48("1232", "1233", "1234"), isFundraiser && (stryMutAct_9fa48("1235") ? {} : (stryCov_9fa48("1235"), {
              fundraiserSignupId: stryMutAct_9fa48("1238") ? signup?.id && null : stryMutAct_9fa48("1237") ? false : stryMutAct_9fa48("1236") ? true : (stryCov_9fa48("1236", "1237", "1238"), (stryMutAct_9fa48("1239") ? signup.id : (stryCov_9fa48("1239"), signup?.id)) || null)
            }))))
          }) : null;
          return stryMutAct_9fa48("1240") ? {} : (stryCov_9fa48("1240"), {
            details: details ? JSON.stringify(details) : undefined,
            child: JSON.stringify(childData),
            route: isFundraiser ? CONSTANTS.NOTIFICATION_ROUTE.FUNDRAISER_DETAILS : CONSTANTS.NOTIFICATION_ROUTE.EVENT_DETAILS
          });
        }
      };
      const addNotificationsForChild = (child, payload) => {
        if (stryMutAct_9fa48("1241")) {
          {}
        } else {
          stryCov_9fa48("1241");
          for (const guardian of child.guardians) {
            if (stryMutAct_9fa48("1242")) {
              {}
            } else {
              stryCov_9fa48("1242");
              notificationEntries.push(stryMutAct_9fa48("1243") ? {} : (stryCov_9fa48("1243"), {
                userId: guardian,
                associatedChildId: child.id,
                title,
                description,
                payload
              }));
            }
          }
          notificationPromises.push(this.createNotificationForChild(child.id, title, description, payload));
        }
      };
      const childrenMap = new Map(children.map(stryMutAct_9fa48("1244") ? () => undefined : (stryCov_9fa48("1244"), child => stryMutAct_9fa48("1245") ? [] : (stryCov_9fa48("1245"), [child.id, child]))));
      if (stryMutAct_9fa48("1247") ? false : stryMutAct_9fa48("1246") ? true : (stryCov_9fa48("1246", "1247"), isFundraiser)) {
        if (stryMutAct_9fa48("1248")) {
          {}
        } else {
          stryCov_9fa48("1248");
          for (const signup of signups) {
            if (stryMutAct_9fa48("1249")) {
              {}
            } else {
              stryCov_9fa48("1249");
              const child = childrenMap.get(signup.childId);
              if (stryMutAct_9fa48("1251") ? false : stryMutAct_9fa48("1250") ? true : (stryCov_9fa48("1250", "1251"), child)) {
                if (stryMutAct_9fa48("1252")) {
                  {}
                } else {
                  stryCov_9fa48("1252");
                  const payload = getPayload(child, signup);
                  addNotificationsForChild(child, payload);
                }
              }
            }
          }
        }
      } else {
        if (stryMutAct_9fa48("1253")) {
          {}
        } else {
          stryCov_9fa48("1253");
          for (const child of children) {
            if (stryMutAct_9fa48("1254")) {
              {}
            } else {
              stryCov_9fa48("1254");
              const signup = signups.find(stryMutAct_9fa48("1255") ? () => undefined : (stryCov_9fa48("1255"), signup => stryMutAct_9fa48("1258") ? signup.childId !== child.id : stryMutAct_9fa48("1257") ? false : stryMutAct_9fa48("1256") ? true : (stryCov_9fa48("1256", "1257", "1258"), signup.childId === child.id)));
              const payload = getPayload(child, stryMutAct_9fa48("1261") ? signup && null : stryMutAct_9fa48("1260") ? false : stryMutAct_9fa48("1259") ? true : (stryCov_9fa48("1259", "1260", "1261"), signup || null));
              addNotificationsForChild(child, payload);
            }
          }
        }
      }
    }
  }

  /**
   * @desc This function is being used to create notification for child
   * @param {String} childId Child Id
   * @param {String} title Title
   * @param {String} description Description
   */
  static async createNotificationForChild(childId, title, description, data) {
    if (stryMutAct_9fa48("1262")) {
      {}
    } else {
      stryCov_9fa48("1262");
      const notificationObject = Notification.createNotificationObject(stryMutAct_9fa48("1263") ? {} : (stryCov_9fa48("1263"), {
        title,
        data,
        topic: stryMutAct_9fa48("1264") ? `` : (stryCov_9fa48("1264"), `childId-${childId}`),
        body: description
      }));
      await Notification.sendNotification(notificationObject);
    }
  }

  /**
   * @desc This function is being used to format notification response object
   * @param {Object} notification Notification
   * <AUTHOR>
   * @since 01/02/2024
   */
  static async formatNotificationResponse(notification) {
    if (stryMutAct_9fa48("1265")) {
      {}
    } else {
      stryCov_9fa48("1265");
      const child = await this.getChild(notification.associatedChildId);
      if (stryMutAct_9fa48("1268") ? notification.payload || notification.payload.child : stryMutAct_9fa48("1267") ? false : stryMutAct_9fa48("1266") ? true : (stryCov_9fa48("1266", "1267", "1268"), notification.payload && notification.payload.child)) {
        if (stryMutAct_9fa48("1269")) {
          {}
        } else {
          stryCov_9fa48("1269");
          notification.payload.child = JSON.stringify(child);
        }
      }
      return stryMutAct_9fa48("1270") ? {} : (stryCov_9fa48("1270"), {
        id: notification.id,
        title: notification.title,
        description: notification.description,
        userId: notification.userId,
        associatedChild: stryMutAct_9fa48("1273") ? notification.payload || child : stryMutAct_9fa48("1272") ? false : stryMutAct_9fa48("1271") ? true : (stryCov_9fa48("1271", "1272", "1273"), notification.payload && child),
        notificationAction: notification.notificationAction,
        payload: notification.payload,
        readStatus: notification.readStatus,
        createdAt: MOMENT(notification.createdAt).valueOf(),
        updatedAt: MOMENT(notification.updatedAt).valueOf()
      });
    }
  }

  /**
   * @desc This function is being used to get child details by child id
   * @param {String} childId Child Id
   * @returns {Object} Child details object
   * <AUTHOR>
   * @since 01/02/2024
   */
  static async getChild(childId) {
    if (stryMutAct_9fa48("1274")) {
      {}
    } else {
      stryCov_9fa48("1274");
      const child = await ChildModel.get(childId, stryMutAct_9fa48("1275") ? {} : (stryCov_9fa48("1275"), {
        attributes: stryMutAct_9fa48("1276") ? [] : (stryCov_9fa48("1276"), [stryMutAct_9fa48("1277") ? "" : (stryCov_9fa48("1277"), 'id'), stryMutAct_9fa48("1278") ? "" : (stryCov_9fa48("1278"), 'firstName'), stryMutAct_9fa48("1279") ? "" : (stryCov_9fa48("1279"), 'lastName'), stryMutAct_9fa48("1280") ? "" : (stryCov_9fa48("1280"), 'associatedColor'), stryMutAct_9fa48("1281") ? "" : (stryCov_9fa48("1281"), 'photoURL'), stryMutAct_9fa48("1282") ? "" : (stryCov_9fa48("1282"), 'school'), stryMutAct_9fa48("1283") ? "" : (stryCov_9fa48("1283"), 'homeRoom')])
      }));
      child.photoURL = (stryMutAct_9fa48("1284") ? child.photoURL : (stryCov_9fa48("1284"), child?.photoURL)) ? await UploadService.getSignedUrl(child.photoURL) : null;
      child.school = await this.getOrganizationName(child.school);
      child.homeRoom = child.homeRoom ? await this.getOrganizationName(child.homeRoom) : null;
      return child;
    }
  }

  /**
   * @desc This function is being used to get organization name by organization id
   * @param {String} organizationId Organization Id
   * @returns {String} Organization name string
   * <AUTHOR>
   * @since 01/02/2024
   */
  static async getOrganizationName(organizationId) {
    if (stryMutAct_9fa48("1285")) {
      {}
    } else {
      stryCov_9fa48("1285");
      const organization = await OrganizationModel.get(organizationId, stryMutAct_9fa48("1286") ? {} : (stryCov_9fa48("1286"), {
        attributes: stryMutAct_9fa48("1287") ? [] : (stryCov_9fa48("1287"), [stryMutAct_9fa48("1288") ? "" : (stryCov_9fa48("1288"), 'name')])
      }));
      return organization.name;
    }
  }

  /**
   * @desc This function is being used to notify
   * @param {Object} req Request
   * @param {Object} res Response
   */
  static async notify(req) {
    if (stryMutAct_9fa48("1289")) {
      {}
    } else {
      stryCov_9fa48("1289");
      await Notification.sendNotification(req.body);
    }
  }

  /**
   * @desc This function is being used to send email to admin and write data to google sheets
   * @param {Object} req Request
   * @param {Object} res Response
   */
  static async contactUs(req) {
    if (stryMutAct_9fa48("1290")) {
      {}
    } else {
      stryCov_9fa48("1290");
      try {
        if (stryMutAct_9fa48("1291")) {
          {}
        } else {
          stryCov_9fa48("1291");
          const {
            firstName,
            lastName,
            email,
            phone,
            message
          } = req.body;
          const template = stryMutAct_9fa48("1292") ? "" : (stryCov_9fa48("1292"), 'emailTemplates/contactUs.html');
          const templateVariables = stryMutAct_9fa48("1293") ? {} : (stryCov_9fa48("1293"), {
            firstName: firstName,
            lastName: lastName,
            email: email,
            phone: phone,
            message: message,
            appname: CONSTANTS.APP_NAME
          });
          await EmailService.prepareAndSendEmail(stryMutAct_9fa48("1294") ? [] : (stryCov_9fa48("1294"), [CONSTANTS.CONTACT_EMAIL]), CONSTANTS.CONTACT_US_SUBJECT, template, templateVariables);
          const sheetsService = new GoogleSheetsService(stryMutAct_9fa48("1295") ? "" : (stryCov_9fa48("1295"), './server/util/credentials.json'), stryMutAct_9fa48("1296") ? "" : (stryCov_9fa48("1296"), '1ZmeeKwjsWnM7jJFuCbet4Y6872eDn9EHyZniVl1-iPQ'));
          const currentTime = MOMENT().format(stryMutAct_9fa48("1297") ? "" : (stryCov_9fa48("1297"), 'YYYY-MM-DD HH:mm:ss'));
          await sheetsService.appendRow(stryMutAct_9fa48("1298") ? "" : (stryCov_9fa48("1298"), 'Sheet1'), stryMutAct_9fa48("1299") ? [] : (stryCov_9fa48("1299"), [firstName, lastName, email, phone, message, currentTime]));
          return stryMutAct_9fa48("1300") ? {} : (stryCov_9fa48("1300"), {
            message: stryMutAct_9fa48("1301") ? "" : (stryCov_9fa48("1301"), 'Email sent successfully!')
          });
        }
      } catch (error) {
        if (stryMutAct_9fa48("1302")) {
          {}
        } else {
          stryCov_9fa48("1302");
          CONSOLE_LOGGER.error(stryMutAct_9fa48("1303") ? "" : (stryCov_9fa48("1303"), 'Error:'), error);
          throw new GeneralError(MESSAGES.INTERNAL_SERVER_ERROR, 500);
        }
      }
    }
  }
}
module.exports = NotificationService;