// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const axios = require('axios');
const {
  google
} = require('googleapis');
const stringify = require('json-stringify-safe');
const UploadService = require('./uploadService');

/**
 * This class represents all the notification services functions
 */
class Notification {
  /**
   * @desc This function is being used to get an access token
   * <AUTHOR>
   * @since 02/02/2024
   */
  static async getAccessToken() {
    if (stryMutAct_9fa48("1868")) {
      {}
    } else {
      stryCov_9fa48("1868");
      return new Promise((resolve, reject) => {
        if (stryMutAct_9fa48("1869")) {
          {}
        } else {
          stryCov_9fa48("1869");
          const jwtClient = new google.auth.JWT(process.env.NOTIFICATION_CLIENT_EMAIL, null, process.env.NOTIFICATION_PRIVATE_KEY.split(stryMutAct_9fa48("1870") ? String.raw`` : (stryCov_9fa48("1870"), String.raw`\n`)).join(stryMutAct_9fa48("1871") ? "" : (stryCov_9fa48("1871"), '\n')), stryMutAct_9fa48("1872") ? [] : (stryCov_9fa48("1872"), [stryMutAct_9fa48("1873") ? "" : (stryCov_9fa48("1873"), 'https://www.googleapis.com/auth/firebase.messaging')]), null);
          jwtClient.authorize((err, tokens) => {
            if (stryMutAct_9fa48("1874")) {
              {}
            } else {
              stryCov_9fa48("1874");
              if (stryMutAct_9fa48("1876") ? false : stryMutAct_9fa48("1875") ? true : (stryCov_9fa48("1875", "1876"), err)) {
                if (stryMutAct_9fa48("1877")) {
                  {}
                } else {
                  stryCov_9fa48("1877");
                  CONSOLE_LOGGER.error(stryMutAct_9fa48("1878") ? "" : (stryCov_9fa48("1878"), 'Error getting access token'), err);
                  reject(err);
                  return;
                }
              }
              resolve(tokens.access_token);
            }
          });
        }
      });
    }
  }

  /**
   * @desc This function is being used to send a notification
   * <AUTHOR>
   * @since 02/02/2024
   * @param {Object} notificationObject
   */
  static async sendNotification(notificationObject) {
    if (stryMutAct_9fa48("1879")) {
      {}
    } else {
      stryCov_9fa48("1879");
      const accessToken = await this.getAccessToken();
      const child = stryMutAct_9fa48("1882") ? notificationObject.data?.child || JSON.parse(notificationObject.data.child) : stryMutAct_9fa48("1881") ? false : stryMutAct_9fa48("1880") ? true : (stryCov_9fa48("1880", "1881", "1882"), (stryMutAct_9fa48("1883") ? notificationObject.data.child : (stryCov_9fa48("1883"), notificationObject.data?.child)) && JSON.parse(notificationObject.data.child));
      if (stryMutAct_9fa48("1886") ? child || child.photoURL : stryMutAct_9fa48("1885") ? false : stryMutAct_9fa48("1884") ? true : (stryCov_9fa48("1884", "1885", "1886"), child && child.photoURL)) {
        if (stryMutAct_9fa48("1887")) {
          {}
        } else {
          stryCov_9fa48("1887");
          child.photoURL = await UploadService.getSignedUrl(child.photoURL);
          notificationObject.data.child = JSON.stringify(child);
        }
      }
      const message = stryMutAct_9fa48("1888") ? {} : (stryCov_9fa48("1888"), {
        message: notificationObject
      });
      CONSOLE_LOGGER.info(stryMutAct_9fa48("1889") ? `` : (stryCov_9fa48("1889"), `Notification message: in sendNotification ${stringify(message)}`));
      try {
        if (stryMutAct_9fa48("1890")) {
          {}
        } else {
          stryCov_9fa48("1890");
          const response = await axios.post(stryMutAct_9fa48("1891") ? `` : (stryCov_9fa48("1891"), `https://fcm.googleapis.com/v1/projects/${process.env.NOTIFICATION_PROJECT_ID}/messages:send`), message, stryMutAct_9fa48("1892") ? {} : (stryCov_9fa48("1892"), {
            headers: stryMutAct_9fa48("1893") ? {} : (stryCov_9fa48("1893"), {
              'Authorization': stryMutAct_9fa48("1894") ? `` : (stryCov_9fa48("1894"), `Bearer ${accessToken}`),
              'Content-Type': stryMutAct_9fa48("1895") ? "" : (stryCov_9fa48("1895"), 'application/json')
            })
          }));
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1896") ? "" : (stryCov_9fa48("1896"), 'Notification sent successfully: in sendNotification'), stringify(response.data));
          return response.data;
        }
      } catch (error) {
        if (stryMutAct_9fa48("1897")) {
          {}
        } else {
          stryCov_9fa48("1897");
          CONSOLE_LOGGER.error(stryMutAct_9fa48("1898") ? "" : (stryCov_9fa48("1898"), 'Error sending notification: in sendNotification'), stringify(error), stryMutAct_9fa48("1899") ? "" : (stryCov_9fa48("1899"), 'and error.message: '), error.message, stryMutAct_9fa48("1900") ? "" : (stryCov_9fa48("1900"), 'and error.response'), stringify(error.response));
          error.isError = stryMutAct_9fa48("1901") ? false : (stryCov_9fa48("1901"), true);
          return error;
        }
      }
    }
  }

  /**
   * @desc This function is being used to create a notification object
   * <AUTHOR>
   * @since 02/02/2024
   * @param {String} topic
   * @param {String} title
   * @param {String} body
   * @param {Object} data
   * @param {String} clickAction
   */
  static createNotificationObject({
    topic,
    title,
    body,
    data,
    clickAction,
    collapseKey
  }) {
    if (stryMutAct_9fa48("1902")) {
      {}
    } else {
      stryCov_9fa48("1902");
      const toReturn = stryMutAct_9fa48("1903") ? {} : (stryCov_9fa48("1903"), {
        topic,
        data,
        notification: stryMutAct_9fa48("1904") ? {} : (stryCov_9fa48("1904"), {
          title,
          body
        }),
        android: stryMutAct_9fa48("1905") ? {} : (stryCov_9fa48("1905"), {
          priority: stryMutAct_9fa48("1906") ? "" : (stryCov_9fa48("1906"), 'high'),
          notification: {}
        }),
        apns: stryMutAct_9fa48("1907") ? {} : (stryCov_9fa48("1907"), {
          headers: stryMutAct_9fa48("1908") ? {} : (stryCov_9fa48("1908"), {
            'apns-priority': stryMutAct_9fa48("1909") ? "" : (stryCov_9fa48("1909"), '10')
          }),
          payload: stryMutAct_9fa48("1910") ? {} : (stryCov_9fa48("1910"), {
            aps: stryMutAct_9fa48("1911") ? {} : (stryCov_9fa48("1911"), {
              category: clickAction,
              badge: 1
            })
          })
        })
      });
      if (stryMutAct_9fa48("1913") ? false : stryMutAct_9fa48("1912") ? true : (stryCov_9fa48("1912", "1913"), collapseKey)) {
        if (stryMutAct_9fa48("1914")) {
          {}
        } else {
          stryCov_9fa48("1914");
          toReturn.android.collapseKey = collapseKey;
        }
      }
      return toReturn;
    }
  }
}
module.exports = Notification;