/* eslint-disable max-len */
module.exports = {
    // For AES, this is always 16
    IV_LENGTH: 16,
    LOG_LEVEL: 'debug',
    PAYMENT_STATUS: {
        APPROVED: 'approved',
        PAYMENT_INITIATED: 'payment-initiated',
        SUCCESS: 'success'
    },
    EVENT_STATUS: {
        PUBLISHED: 'published'
    },
    POST_STATUS: {
        PUBLISHED: 'published'
    },
    FUNDRAISER_STATUS: {
        PUBLISHED: 'published'
    },
    EVENT_TYPE: {
        CALENDAR: 'calendar',
        EVENT: 'event'
    },
    CONNECTION_STATUS: {
        REQUESTED_BY: 'requestedBy',
        CONNECTED: 'connected',
        REQUESTED_TO: 'requestedTo'
    },
    CONNECTION_TITLE: {
        CONNECTION_REQUEST: 'Connection Request',
        CONNECTION_ACCEPTED: 'Connection Accepted'
    },
    CONNECTION_MESSAGE: {
        CONNECTION_REQUEST: 'Someone has requested for the connection',
        CONNECTION_ACCEPTED: 'Your connection request has been accepted'
    },
    INDICES: {
        'events': 'eventSchema',
        'children': 'childSchema',
        'fundraisers': 'fundraiserSchema',
        'posts': 'postSchema'
    },
    MEMBERSHIP_TYPES: {
        CHILD: 'child',
        FAMILY: 'family'
    },
    FUNDRAISER_TYPES: {
        SPIRITWEAR: 'spiritwearFundraiser',
        ANNUAL: 'annualFundraiser',
        MEMBERSHIP: 'membershipFundraiser',
        BOOSTER: 'booster',
        OTHER: 'other'
    },
    TRIGGER: {
        BOOSTER_DONATION: 'boosterDonation'
    },
    GROUP_TYPES: {
        EVENT: 'event',
        ORGANIZATION: 'organization',
        ORGANIZATION_ADMIN: 'organization_admin'
    },
    GROUP_STATUS: {
        ACTIVE: 'active',
        INACTIVE: 'inactive',
        EXPIRED: 'expired'
    },
    ORG_MEMBER_STATUS: {
        ACTIVE: 'active',
        INACTIVE: 'inactive',
        DELETED: 'deleted'
    },
    GROUP_MEMBER_STATUS: {
        ACTIVE: 'active',
        DISABLED: 'disabled',
        REMOVED: 'removed'
    },
    OPENSEARCH_COLLECTION: {
        ORGANIZATIONS: 'organizations'
    },
    ORGANIZATION_CATEGORY: {
        PTO: 'PTO',
        SCHOOL: 'School',
        HOMEROOM: 'Homeroom',
        CLUB: 'Club',
        SUPER_ORGANIZATION: 'Super Organization',
        BUSINESS: 'Business'
    },
    DEVELOPERS_EMAIL: '<EMAIL>',
    POST_EXPIRATION_DAYS: 'PostExpirationDays',
    FEED_VERSION_PREFIX: 'FeedVersionPrefix',
    GROUP_CONVERSATION_EXPIRATION_DAYS: 2,
    AWS_S3_PUBLIC_BUCKET: 'vaalee-assets',
    COMPRESSION_QUALITY: 60,
    TOP_RECENT_GROUP_MESSAGES_LIMIT: 11,
    ALLOWED_REACTIONS: ['1F44D', '1F44E', '2665'],
    PLUS_INF: '+inf',
    MINUS_INF: '-inf',
    KEY_FOR_USER_EVENTS: 'user-events',
    KEY_FOR_USER_REGISTERED_EVENTS: 'user-registered-events',
    KEY_FOR_USER_CALENDAR_EVENTS: 'user-calendar-events',
    KEY_FOR_CHILD_EVENTS: 'child-events',
    KEY_FOR_CHILD_REGISTERED_EVENTS: 'child-registered-events',
    KEY_FOR_CHILD_CALENDAR_EVENTS: 'child-calendar-events',
    KEY_FOR_CHILD_DETAILS: 'child-details',
    KEY_FOR_EVENT_DETAILS: 'event-details',
    KEY_FOR_FUNDRAISER_DETAILS: 'fundraiser-details',
    KEY_FOR_POST_DETAILS: 'post-details',
    KEY_FOR_ORGANIZATION_DETAILS: 'organization-details',
    KEY_FOR_USER_CONVERSATIONS: 'conversation-list',
    KEY_FOR_CONVERSATION_DETAILS: 'conversation-details',
    KEY_FOR_CONVERSATION_MEMBERS: 'conversation-members',
    KEY_FOR_CONVERSATION_MESSAGES: 'conversation-messages',
    KEY_FOR_USER_DETAILS: 'user-details',
    CHILD_ATTRIBUTES: ['id', 'firstName', 'lastName', 'associatedColor', 'photoURL', 'guardians'],
    VERSION_PREFIXES: {
        VERSION_PREFIX_V1: 'v1',
        VERSION_PREFIX_V2: 'v2'
    },
    ALLOWED_MESSAGE_SIZE: 125 * 1024, // 125kb
    DB_BATCH_OPERATION_CHUNK_SIZE: {
        BATCH_PUT: 25,
        BATCH_DELETE: 25,
        BATCH_GET: 100
    }
};
