/* eslint-disable max-len */
module.exports = {
    // For AES, this is always 16
    IV_LENGTH: 16,
    LOG_LEVEL: 'debug',
    EVENT_IMAGE: {
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png']
    },
    USER_DOCUMENT_FILE: {
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf']
    },
    REGEX: {
        EMAIL: /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/,
        NAME: /^[a-zA-Z0-9,'~._^ -]{1,100}$/,
        ALPHA_ONLY: /^[a-zA-Z']*$/,
        ALPHA_SPECIAL_CHAR: /^[ A-Za-z0-9_@./#&+-]*$/,
        ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: /^[ A-Za-z_@./#&+-]*$/,
        FULL_ACCESS: /^[^<> ?//\\]+$/,
        ALPHA_NUMARIC: /^[\w@ ]+$/,
        templateName: /^[ A-Za-z0-9_@./#&+-]*$/,
        SUBJECT: /^[ A-Za-z0-9_@./#&+-]*$/,
        URL: /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/,
        MOBILE: /^([+]\d{1,2})?\d{10}$/,
        PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    },
    OTPLENGTH: 6,
    OTP_EXPIRY_DURATION: 30,
    VERIFIED: {
        PENDING: 0,
        ACTIVE: 1
    },
    EMAIL_TEMPLATE: {
        REGISTER: 'verificationOtpMail.html',
        FORGOTPASSWORD: 'forgotPassword.html'
    },
    OTP_TYPE: ['register', 'forgotPassword'],
    PAYMENT_TYPES: {
        FREE: 'free',
        STRIPE: 'stripe',
        CASH: 'cash',
        CHECK: 'cheque',
        VENMO: 'venmo',
        PAYMENT_INITIATED: 'payment-initiated',
        OFFLINE: 'offline',
        ONLINE: 'online'
    },
    PAYMENT_STATUS: {
        PENDING: 'pending',
        APPROVED: 'approved'
    },
    STATUS: {
        INACTIVE: 'inactive',
        ACTIVE: 'active',
        SUSPENDED: 'suspended',
        FREEZED: 'freezed',
        PUBLISHED: 'published'
    },
    FUNDRAISER_TYPE: ['spiritwearFundraiser', 'annualFundraiser', 'membershipFundraiser', 'other', 'booster'],
    FUNDRAISER_TYPES: {
        SPIRITWEAR: 'spiritwearFundraiser',
        ANNUAL: 'annualFundraiser',
        MEMBERSHIP: 'membershipFundraiser',
        OTHER: 'other',
        ORG_MANAGED_BOOSTER: 'booster'
    },
    QUANTITY_TYPE: ['People', 'Items'],
    ENVIRONMENT: {
        TESTING: 'testing',
        LOCAL: 'local',
        DEV: 'dev',
        PRODUCTION: 'production'
    },
    DEVELOPERS_EMAIL: '<EMAIL>',
    SES_HOST: 'email-smtp.us-east-2.amazonaws.com',
    CLIENT_INFO: {
        EMAIL: '<EMAIL>',
        HELP_EMAIL: '<EMAIL>'
    },
    APP_NAME: 'Vaalee',
    ROLE: {
        USER: 1,
        ORG_ADMIN: 3,
        ADMIN: 4
    },
    ACCESS_LEVEL: {
        APP: 'app',
        ORG_APP: 'org_app',
        ROOT: 'root'
    },
    ORG_ROLE: {
        SUPER_ADMIN: 'super admin'
    },
    OPEN_SEARCH: {
        COLLECTION: {
            CHILD: 'children',
            FUNDRAISER: 'fundraisers'
        },
        SEARCHABLE_FIELDS: {
            'fundraisers': ['title', 'description'],
            'children': ['firstName', 'lastName']
        },
        INDICES: {
            'fundraisers': 'fundraiserSchema',
            'children': 'childSchema'
        }
    },
    MEMBERSHIP_TYPES: {
        CHILD: 'child',
        FAMILY: 'family'
    },
    ALLOWED_EVENT_STATUS: ['published', 'draft', 'cancelled'],
    ALLOWED_SIGNUP_STATUS: ['pending', 'approved', 'cancelled'],
    AWS_S3_PUBLIC_BUCKET: 'vaalee-assets',
    COMPRESSION_QUALITY: 60,
    DEFAULT_MIN_PLATFORM_FEE_AMOUNT: 1,
    DEFAULT_BOOSTER_MESSAGE_FROM_CHILD: 'Help me raise fund for my school. Thank You!',
    BOOSTER_DONATION_STATUS: {
        SUCCESS: 'success',
        UNKNOWN: 'unknown',
        FAILED: 'failed'
    },
    DB_BATCH_OPERATION_CHUNK_SIZE: {
        BATCH_PUT: 25,
        BATCH_DELETE: 25,
        BATCH_GET: 100
    }
};
