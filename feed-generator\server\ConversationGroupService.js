
const CONSTANTS = require('./constants');
const MOMENT = require('moment');
const GroupMembers = require('./models/groupMembers.model');
const Groups = require('./models/groups.model');
const OrganizationMember = require('./models/organizationMember.model');
const ConversationService = require('./ConversationService');
const User = require('./models/user.model');
const Message = require('./models/message.model');
const EventSignups = require('./models/eventSignup.model');
const Child = require('./models/child.model');
const UploadService = require('./uploadService');
const { v4: uuidv4 } = require('uuid');
const CONSOLE_LOGGER = require('./logger');

class ConversationGroupService {
    /**
     * Add user to organization group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} child - The child object
     * @returns {Promise<boolean>} - Returns true if the user is added to the organization group
    */
    static async addUserToOrganizationGroup (child) {
        const guardians = child.guardians;
        for (const userId of guardians) {
            for (const organizationId of child.associatedOrganizations) {
                const orgGroup = await this.getOrganizationGroup(organizationId);
                const isUserInGroup = await this.isUserInGroup(userId, orgGroup);
                await this.addOrUpdateUserToGroup({ id: userId }, orgGroup, isUserInGroup, child);
            }
        }
        return true;
    }

    /**
     * Check if user is in group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {string} userId - The user id
     * @param {Object} orgGroup - The organization group object
     * @returns {Promise<Object>} - Returns the group members if the user is in the group
    */
    static async isUserInGroup (userId, orgGroup) {
        if (orgGroup) {
            const groupMembers = GroupMembers.query('userId').eq(userId).using('userId-index')
                .where('groupId').eq(orgGroup.groupId).exec();
            return groupMembers;
        }
        return null;
    }

    /**
     * Get organization group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {string} organizationId - The organization id
     * @returns {Promise<Object>} - Returns the organization group if it exists
    */
    static async getOrganizationGroup (organizationId) {
        const orgGroup = await Groups.query('organizationId').eq(organizationId).using('organizationId-index')
            .where('groupType').eq(CONSTANTS.GROUP_TYPES.ORGANIZATION)
            .where('status').eq('active').exec();
        if (orgGroup.length > 0) {
            return orgGroup[0];
        }
        return null;
    }

    /**
     * Get groups associated with organization
     * <AUTHOR>
     * @since 18/09/2024
     * @param {string} organizationId - The organization id
     * @returns {Promise<Array>} - Returns the groups associated with the organization
    */
    static async getGroupsAssociatedWithOrganization (organizationId) {
        return await Groups.query('organizationId').eq(organizationId).using('organizationId-index').where('status').eq('active').exec();
    }

    /**
     * Add or update user to group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {string} userId - The user id
     * @param {Object} orgGroup - The organization group object
     * @param {Object} isUserInGroup - The group members if the user is in the group
     * @param {Object} child - The child object
     * @param {boolean} isAdmin - The is admin flag
     * @returns {Promise<Object>} - Returns the group member if the user is in the group
    */
    static async addOrUpdateUserToGroup (user, orgGroup, isUserInGroup, child, isAdmin = false) {
        if (isUserInGroup?.length > 0) {
            const groupMember = isUserInGroup[0];
            if (child) {
                groupMember.childrenIds = Array.from(new Set([...groupMember.childrenIds, child.id]));
            }
            groupMember.status = 'active';
            groupMember.isAdmin = groupMember.isAdmin || isAdmin;
            const updatedGroupMember = await groupMember.save();
            await this.handleGroupMemberStatus(user, orgGroup, groupMember);
            return updatedGroupMember;
        } else if (orgGroup) {
            const groupMember = await GroupMembers.create({
                userId: user.id,
                isAdmin,
                groupId: orgGroup.groupId,
                childrenIds: child ? [child.id] : [],
                status: 'active'
            });
            await this.handleGroupMemberStatus(user, orgGroup, groupMember);
            return groupMember;
        } else {
            return null;
        }
    }

    static async handleGroupMemberStatus (user, orgGroup, groupMember) {
        const groupMembers = await GroupMembers.query('groupId').eq(orgGroup.groupId).using('groupId-index').exec();
        const groupMemberIds = [];
        const groupMembersUserDatailsPromise = groupMembers.map(async member => {
            const fetchedUser = await User.query('id').eq(member.userId).attributes(['id', 'firstName', 'lastName']).exec();
            if (!fetchedUser || fetchedUser.length === 0) {
                return null;
            }
            groupMemberIds.push(member.userId);
            member.firstName = fetchedUser[0].firstName;
            member.lastName = fetchedUser[0].lastName;
            return member;
        }).filter(Boolean);
        const groupMembersUserDatails = await Promise.all(groupMembersUserDatailsPromise);
        const activeSocketConnections = await ConversationService.getActiveSocketConnections(groupMemberIds);
        if (!user.firstName && !user.lastName) {
            const fetchedUser = await User.query('id').eq(user.id).attributes(['id', 'firstName', 'lastName']).exec();
            if (fetchedUser.length > 0) {
                user = fetchedUser[0];
            } else {
                CONSOLE_LOGGER.error('User not found for id:', user.id);
            }
        }
        const messageData = {
            message: 'New user added to group!',
            actionType: 'ADD_USER_TO_GROUP',
            action: 'sendMessage',
            data: {
                id: groupMember.id,
                userId: user.id,
                groupId: groupMember.groupId,
                firstName: user?.firstName,
                lastName: user?.lastName,
                isAdmin: groupMember.isAdmin,
                status: groupMember.status,
                childrenIds: groupMember.childrenIds
            }
        };
        await ConversationService.sendMessagesToConnections(activeSocketConnections, messageData);

        const messagesData = await this.fetchMessages(groupMember.groupId);
        const isOrgGroup =
            [CONSTANTS.GROUP_TYPES.ORGANIZATION, CONSTANTS.GROUP_TYPES.ORGANIZATION_ADMIN].includes(orgGroup.groupType);
        const groupMemberUserDetails = { ...groupMember, firstName: user?.firstName, lastName: user?.lastName };
        const groupData = await this.createGroupDetails(
            groupMemberUserDetails, orgGroup, isOrgGroup,
            groupMembersUserDatails, messagesData
        );
        const newGroup = {
            message: 'You have been added to a new group',
            actionType: 'NEW_GROUP_ADDED',
            action: 'sendMessage',
            data: {
                id: user.id,
                group: groupData,
                firstName: user?.firstName,
                lastName: user?.lastName,
                isAdmin: groupMember.isAdmin,
                status: groupMember.status
            }
        };
        const userActiveSocketConnection = await ConversationService.getActiveSocketConnections([user.id]);
        await ConversationService.sendMessagesToConnections(userActiveSocketConnection, newGroup);
    }

    /**
     * Remove user from group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} isUserInGroup - The group members if the user is in the group
     * @param {Object} child - The child object
     * @returns {Promise<Object>} - Returns the group member if the user is in the group
    */
    static async removeUserFromGroup (isUserInGroup, child) {
        if (isUserInGroup?.length > 0) {
            const groupMember = isUserInGroup[0];
            if (child) {
                groupMember.childrenIds = groupMember.childrenIds.filter(childId => childId !== child.id);
            }
            if (groupMember.childrenIds.length === 0) {
                groupMember.status = 'removed';
                groupMember.isAdmin = false;
                await groupMember.save();
                const groupMemberIds = await ConversationService.getGroupMemberIds(groupMember.groupId);
                const activeSocketConnections = await ConversationService.getActiveSocketConnections(groupMemberIds);
                const messageData = {
                    message: 'User removed from group',
                    actionType: 'REMOVE_USER_FROM_GROUP',
                    action: 'sendMessage',
                    data: {
                        id: groupMember.userId,
                        groupId: groupMember.groupId
                    }
                };
                await ConversationService.sendMessagesToConnections(activeSocketConnections, messageData);
            } else {
                groupMember.isAdmin = false;
                await groupMember.save();
            }
        }
        return true;
    }

    /**
     * Update child guardians groups
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} oldChild - The old child object
     * @param {Object} newChild - The new child object
     * @returns {Promise<boolean>} - Returns true if the child guardians groups are updated
    */
    static async updateChildGuardiansGroups (oldChild, newChild) {
        const oldChildOrganizations = new Set(oldChild.associatedOrganizations);
        const newChildOrganizations = new Set(newChild.associatedOrganizations);

        const addedOrganizations = [...newChildOrganizations].filter(org => !oldChildOrganizations.has(org));
        const removedOrganizations = [...oldChildOrganizations].filter(org => !newChildOrganizations.has(org));

        const oldGuardians = new Set(oldChild.guardians);
        const newGuardians = new Set(newChild.guardians);

        const addedGuardians = [...newGuardians].filter(guardian => !oldGuardians.has(guardian));
        const removedGuardians = [...oldGuardians].filter(guardian => !newGuardians.has(guardian));

        await Promise.all([
            addedOrganizations.length > 0 && this.addGuardiansToOrganizationGroups(newChild.guardians, addedOrganizations, newChild),
            removedOrganizations.length > 0
            && this.removeGuardiansFromOrganizationGroups(newChild.guardians, removedOrganizations, newChild),
            addedGuardians.length > 0 && this.addGuardiansToOrganizationGroups(addedGuardians, [...newChildOrganizations], newChild),
            addedGuardians.length > 0 && this.addGuardiansToEventGroups(addedGuardians, newChild),
            removedGuardians.length > 0
            && this.removeGuardiansFromOrganizationGroups(removedGuardians, [...newChildOrganizations], newChild),
            removedGuardians.length > 0 && this.removeGuardiansFromEventGroups(removedGuardians, newChild)
        ]);

        return true;
    }

    /**
     * Add guardians to groups
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} guardians - The guardians array
     * @param {Array} groups - The groups array
     * @param {Object} child - The child object
     * @param {string} groupType - The group type
     * @returns {Promise<boolean>} - Returns true if the guardians are added to the groups
    */
    static async addGuardiansToGroups (guardians, groups, child) {
        const guardianIds = new Set(guardians);

        const updateItems = [];
        const createItems = [];
        const allGroupsAndMembersUserIds = [];
        const allGroupsAndMembersMap = new Map();
        let allGroupsMembersUserIds = [];

        for (const group of groups) {
            if (!group) {
                continue;
            }
            const groupMembers = await GroupMembers.query('groupId').eq(group.groupId).using('groupId-index').exec();
            allGroupsAndMembersMap.set(group.groupId, groupMembers);
            const groupMemberIds = groupMembers.map(member => member.userId);
            allGroupsAndMembersUserIds.push({ groupId: group.groupId, groupMemberIds });
            allGroupsMembersUserIds.push(...groupMemberIds);
            const existingMembers = new Map(groupMembers.map(member => [member.userId, member]));

            for (const guardianId of guardianIds) {
                if (existingMembers.has(guardianId)) {
                    const existingMember = existingMembers.get(guardianId);
                    existingMember.childrenIds = Array.from(new Set([...existingMember.childrenIds, child.id]));
                    existingMember.status = 'active';
                    updateItems.push(existingMember);
                } else {
                    createItems.push(
                        new GroupMembers({
                            id: uuidv4(),
                            userId: guardianId,
                            isAdmin: false,
                            groupId: group.groupId,
                            childrenIds: [child.id],
                            status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE
                        })
                    );
                }
            }
        }

        allGroupsMembersUserIds = [...new Set(allGroupsMembersUserIds)];
        const allGroupMembersUserDetails = (await Promise.all(allGroupsMembersUserIds.map(async userId =>
            await User.query('id').eq(userId).attributes(['id', 'firstName', 'lastName']).exec()
        ))).flat();

        const groupMembersToProcess = [...createItems, ...updateItems];
        await this.batchPutInChunks({
            model: GroupMembers,
            data: groupMembersToProcess
        });

        const addUserToGroupPromise = groupMembersToProcess.map(async newGroupMember => {
            const activeSocketConnections = await ConversationService.getActiveSocketConnections(
                allGroupsAndMembersUserIds
                    .find(groupAndMembersUserIds => groupAndMembersUserIds.groupId === newGroupMember.groupId)
                    .groupMemberIds);
            const user = await this.getUserDetails(allGroupMembersUserDetails, newGroupMember.userId);
            const messageData = {
                message: 'New user added to group!',
                actionType: 'ADD_USER_TO_GROUP',
                action: 'sendMessage',
                data: {
                    id: newGroupMember.id,
                    userId: newGroupMember.userId,
                    groupId: newGroupMember.groupId,
                    firstName: user?.firstName,
                    lastName: user?.lastName,
                    isAdmin: newGroupMember.isAdmin,
                    status: newGroupMember.status,
                    childrenIds: newGroupMember.childrenIds
                }
            };
            await ConversationService.sendMessagesToConnections(activeSocketConnections, messageData);

            const messagesData = await this.fetchMessages(newGroupMember.groupId);
            const groupDetails = groups.find(group => group.groupId === newGroupMember.groupId);
            const isOrgGroup =
                [CONSTANTS.GROUP_TYPES.ORGANIZATION, CONSTANTS.GROUP_TYPES.ORGANIZATION_ADMIN].includes(groupDetails.groupType);
            if (isOrgGroup && groupDetails.organizationMetaData?.logo) {
                groupDetails.organizationMetaData.logo = await UploadService.getSignedUrl(groupDetails.organizationMetaData.logo);
            }
            if (!isOrgGroup && groupDetails.eventMetaData?.image) {
                groupDetails.eventMetaData.image = await UploadService.getSignedUrl(groupDetails.eventMetaData.image);
            }
            const currentGroupMembers = allGroupsAndMembersMap.get(newGroupMember.groupId)?.map(member => {
                const userDetails = allGroupMembersUserDetails.find(user => user.id === member.userId);
                if (!userDetails) {
                    CONSOLE_LOGGER.error(`User not found for userId: ${member.userId}`);
                    return null;
                }
                member.firstName = userDetails.firstName;
                member.lastName = userDetails.lastName;
                return member;
            }).filter(Boolean) ?? [];

            const groupMemberUserDetails = { ...newGroupMember, firstName: user?.firstName, lastName: user?.lastName };
            const groupData =
                await this.createGroupDetails(groupMemberUserDetails, groupDetails, isOrgGroup, currentGroupMembers, messagesData);
            const newGroup = {
                message: 'You have been added to a group!',
                actionType: 'NEW_GROUP_ADDED',
                action: 'sendMessage',
                data: {
                    id: newGroupMember.userId,
                    group: groupData,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    isAdmin: newGroupMember.isAdmin,
                    status: newGroupMember.status
                }
            };
            const userActiveSocketConnection = await ConversationService.getActiveSocketConnections([newGroupMember.userId]);
            await ConversationService.sendMessagesToConnections(userActiveSocketConnection, newGroup);
        });
        await Promise.all(addUserToGroupPromise);
        return true;
    }

    /**
     * @desc Generic function to perform batchPut in chunks
     * <AUTHOR>
     * @since 25/07/2025
     * @param {Object} options
     * @param {Object} options.model - Dynamoose model to use
     * @param {Array} options.data - Data to put
     * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT] - Max chunk size per request
     */
    static async batchPutInChunks ({ model, data, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT }) {
        if (!Array.isArray(data)) {
            return;
        }
        data = data.filter(Boolean);
        if (data.length === 0) {
            return;
        }

        for (let i = 0; i < data.length; i += chunkSize) {
            const dataChunk = data.slice(i, i + chunkSize);
            await model.batchPut(dataChunk);
        }
    }

    static async getUserDetails (allGroupMembersUserDetails, userId) {
        let user = allGroupMembersUserDetails.find(user => user.id === userId);
        if (!user) {
            const fetchedUser = await User.query('id').eq(userId).attributes(['id', 'firstName', 'lastName']).exec();
            if (fetchedUser.length > 0) {
                user = fetchedUser[0];
            } else {
                CONSOLE_LOGGER.error('User not found for id:', userId);
            }
        }
        return user;
    }
    /**
     * Add guardians to organization groups
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} guardians - The guardians array
     * @param {Array} organizations - The organizations array
     * @param {Object} child - The child object
     * @returns {Promise<boolean>} - Returns true if the guardians are added to the organization groups
    */
    static async addGuardiansToOrganizationGroups (guardians, organizations, child) {
        const orgGroups = (await Promise.all(organizations.map(orgId => this.getOrganizationGroup(orgId)))).filter(Boolean);
        return this.addGuardiansToGroups(guardians, orgGroups, child);
    }

    /**
     * Add guardians to event groups
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} guardians - The guardians array
     * @param {Object} child - The child object
     * @returns {Promise<boolean>} - Returns true if the guardians are added to the event groups
    */
    static async addGuardiansToEventGroups (guardians, child) {
        const signedUpEvents = await EventSignups.query('childId').eq(child.id).exec();
        const eventGroups = (await Promise.all(signedUpEvents.map(event => this.getEventGroup(event.eventId)))).filter(Boolean);
        return this.addGuardiansToGroups(guardians, eventGroups, child);
    }

    /**
     * Remove guardians from groups
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} guardians - The guardians array
     * @param {Array} groups - The groups array
     * @param {Object} child - The child object
     * @returns {Promise<boolean>} - Returns true if the guardians are removed from the groups
    */
    static async removeGuardiansFromGroups (guardians, groups, child) {
        const guardianIds = new Set(guardians);

        groups = groups.filter(Boolean);

        const groupMembersToUpdate = [];
        const groupMembersToRemove = [];
        const allGroupMemberIds = [];

        const handleGroupMembers = async (groupMember) => {
            if (groupMember) {
                if (child) {
                    groupMember.childrenIds = groupMember.childrenIds.filter(childId => childId !== child.id);
                }
                if (groupMember.childrenIds.length === 0) {
                    groupMember.status = 'removed';
                    groupMember.isAdmin = false;
                    groupMembersToRemove.push(groupMember);
                } else {
                    groupMember.isAdmin = false;
                    groupMembersToUpdate.push(groupMember);
                }
            }
        };

        for (const group of groups) {
            const groupMembers = await GroupMembers.query('groupId').eq(group.groupId).using('groupId-index').exec();
            const groupMemberIds = groupMembers.map(member => member.userId);
            allGroupMemberIds.push({ groupId: group.groupId, groupMemberIds });
            for (const guardian of guardianIds) {
                const groupMemberMap = new Map(groupMembers.map(member => [member.userId, member]));
                const groupMember = groupMemberMap.get(guardian);
                await handleGroupMembers(groupMember);
            }
        }

        await this.batchPutInChunks({
            model: GroupMembers,
            data: [...groupMembersToUpdate, ...groupMembersToRemove]
        });

        const removeUserToGroupEventPromise = groupMembersToRemove.map(async groupMemberToDelete => {
            const userGroupMemberIds = allGroupMemberIds
                .find(groupMemberDetails => groupMemberDetails.groupId === groupMemberToDelete.groupId).groupMemberIds;
            const activeSocketConnections = await ConversationService.getActiveSocketConnections(userGroupMemberIds);
            const messageData = {
                message: 'User removed from group!',
                actionType: 'REMOVE_USER_FROM_GROUP',
                action: 'sendMessage',
                data: {
                    id: groupMemberToDelete.userId,
                    groupId: groupMemberToDelete.groupId
                }
            };
            await ConversationService.sendMessagesToConnections(activeSocketConnections, messageData);
        });
        await Promise.all(removeUserToGroupEventPromise);
    }
    /**
     * Remove guardians from organization groups
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} guardians - The guardians array
     * @param {Array} organizations - The organizations array
     * @param {Object} child - The child object
     * @returns {Promise<boolean>} - Returns true if the guardians are removed from the organization groups
    */
    static async removeGuardiansFromOrganizationGroups (guardians, organizations, child) {
        const orgGroups = await Promise.all(organizations.map(orgId => this.getOrganizationGroup(orgId)));
        return this.removeGuardiansFromGroups(guardians, orgGroups, child);
    }

    /**
     * Remove guardians from event groups
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} guardians - The guardians array
     * @param {Object} child - The child object
     * @returns {Promise<boolean>} - Returns true if the guardians are removed from the event groups
    */
    static async removeGuardiansFromEventGroups (guardians, child) {
        const signedUpEvents = await EventSignups.query('childId').eq(child.id).exec();
        const eventGroups = await Promise.all(signedUpEvents.map(event => this.getEventGroup(event.eventId)));
        return this.removeGuardiansFromGroups(guardians, eventGroups, child);
    }

    /**
     * Create event group with admins
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} event - The event object
     * @returns {Promise<Object>} - Returns the event group if it is created
    */
    static async createEventGroupWithAdmins (event) {
        if (event.eventType !== CONSTANTS.EVENT_TYPE.EVENT || event.status !== CONSTANTS.EVENT_STATUS.PUBLISHED) {
            return null;
        }
        const group = await Groups.create({
            groupType: CONSTANTS.GROUP_TYPES.EVENT,
            organizationId: event.organizationId,
            eventId: event.id,
            eventMetaData: {
                title: event.title,
                image: event.photoURL,
                startDateTime: MOMENT.utc(event.details.startDateTime).toDate(),
                endDateTime: MOMENT.utc(event.details.endDateTime).toDate()
            }
        });

        const admins = await this.getAdminsForOrganization(event.organizationId);

        await Promise.all(admins.map(async admin => {
            const isUserInGroup = await this.isUserInGroup(admin.id, group);
            await this.addOrUpdateUserToGroup(admin, group, isUserInGroup, null, true);
        }));

        return group;
    }

    /**
     * Get admins for organization
     * <AUTHOR>
     * @since 18/09/2024
     * @param {string} organizationId - The organization id
     * @returns {Promise<Array>} - Returns the admins for the organization
    */
    static async getAdminsForOrganization (organizationId) {
        const orgMembers = await OrganizationMember.get(organizationId);
        if (orgMembers) {
            return orgMembers.users
                .filter(user => user.status === CONSTANTS.ORG_MEMBER_STATUS.ACTIVE)
                .filter((user, index, self) =>
                    index === self.findIndex((t) => (
                        t.id === user.id
                    ))
                );
        }
        return [];
    }

    /**
     * Check event group meta data
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} oldEvent - The old event object
     * @param {Object} newEvent - The new event object
     * @returns {Promise<boolean>} - Returns true if the event group meta data is checked
    */
    static async checkEventGroupMetaData (oldEvent, newEvent) {
        if (newEvent.eventType !== CONSTANTS.EVENT_TYPE.EVENT ||
            newEvent.status !== CONSTANTS.EVENT_STATUS.PUBLISHED ||
            newEvent.isDeleted === 1 ||
            (oldEvent.title === newEvent.title &&
                oldEvent.details.startDateTime === newEvent.details.startDateTime &&
                oldEvent.details.endDateTime === newEvent.details.endDateTime &&
                oldEvent.photoURL === newEvent.photoURL)) {
            return false;
        }
        return true;
    }


    /**
     * Check event group expired
     * <AUTHOR>
     * @since 05/11/2024
     * @param {Object} event - The event object
     * @returns {Promise<boolean>} - Returns true if the event group is expired
    */
    static async checkEventGroupExpired (event) {
        return MOMENT(event.details.endDateTime)
            .add(CONSTANTS.GROUP_CONVERSATION_EXPIRATION_DAYS, 'days')
            .isBefore(MOMENT());
    }

    /**
     * Get event group without status check
     * <AUTHOR>
     * @since 05/11/2024
     * @param {string} eventId - The event id
     * @returns {Promise<Object>} - Returns the event group if it is found
    */
    static async getEventGroupWithoutStatusCheck (eventId) {
        const eventGroup = await Groups.query('eventId').eq(eventId).using('eventId-index').exec();
        if (eventGroup?.length > 0) {
            return eventGroup[0];
        }
        return null;
    }

    /**
     * Update event group meta data
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} oldEvent - The old event object
     * @param {Object} newEvent - The new event object
     * @returns {Promise<Object>} - Returns the event group if it is updated
    */
    static async updateEventGroupMetaData (oldEvent, newEvent) {
        const eventGroup = await this.getEventGroupWithoutStatusCheck(newEvent.id);
        if (oldEvent.status !== CONSTANTS.EVENT_STATUS.PUBLISHED && newEvent.status === CONSTANTS.EVENT_STATUS.PUBLISHED && !eventGroup) {
            return await this.createEventGroupWithAdmins(newEvent);
        }
        if (!eventGroup) {
            return null;
        }
        const isMetaDataUpdated = await this.checkEventGroupMetaData(oldEvent, newEvent);
        const isEventGroupExpired = await this.checkEventGroupExpired(newEvent);
        if (isMetaDataUpdated && eventGroup && !isEventGroupExpired) {
            eventGroup.eventMetaData = {
                title: newEvent.title,
                image: newEvent.photoURL,
                startDateTime: MOMENT.utc(newEvent.details.startDateTime).toDate(),
                endDateTime: MOMENT.utc(newEvent.details.endDateTime).toDate()
            };
            eventGroup.status = eventGroup.status === CONSTANTS.GROUP_STATUS.EXPIRED
                ? CONSTANTS.GROUP_STATUS.ACTIVE
                : eventGroup.status;
            return await eventGroup.save();
        }
        return null;
    }

    /**
     * Create organization group with admins
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} organization - The organization object
     * @returns {Promise<Object>} - Returns the organization group if it is created
    */
    static async createOrganizationGroupWithAdmins (organization) {
        if (organization.category === CONSTANTS.ORGANIZATION_CATEGORY.SUPER_ORGANIZATION) {
            return null;
        }
        const group = await Groups.create({
            groupType: CONSTANTS.GROUP_TYPES.ORGANIZATION,
            organizationId: organization.id,
            organizationMetaData: {
                name: organization.name,
                logo: organization.logo
            }
        });
        const adminGroup = await Groups.create({
            groupType: CONSTANTS.GROUP_TYPES.ORGANIZATION_ADMIN,
            organizationId: organization.id,
            organizationMetaData: {
                name: organization.name,
                logo: organization.logo
            }
        });
        const admins = await this.getAdminsForOrganization(organization.id);
        await Promise.all(admins.map(async admin => {
            await this.addOrUpdateUserToGroup(admin, group, null, null, true);
            await this.addOrUpdateUserToGroup(admin, adminGroup, null, null, true);
        }));
        return group;
    }

    /**
     * Update organization group meta data
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} oldOrganization - The old organization object
     * @param {Object} newOrganization - The new organization object
     * @returns {Promise<Object>} - Returns the organization group if it is updated
    */
    static async updateOrganizationGroupMetaData (oldOrganization, newOrganization) {
        if (oldOrganization.name !== newOrganization.name || oldOrganization.logo !== newOrganization.logo) {
            const orgGroup = await Groups.query('organizationId').eq(newOrganization.id).using('organizationId-index')
                .where('groupType').in([CONSTANTS.GROUP_TYPES.ORGANIZATION, CONSTANTS.GROUP_TYPES.ORGANIZATION_ADMIN]).exec();
            if (orgGroup?.length > 0) {
                for (const group of orgGroup) {
                    group.organizationMetaData = {
                        name: newOrganization.name,
                        logo: newOrganization.logo
                    };
                    await group.save();
                }
                return true;
            }
        }
        return false;
    }

    /**
     * Update organization group members
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} oldOrganizationMember - The old organization member object
     * @param {Object} newOrganizationMember - The new organization member object
     * @returns {Promise<Object>} - Returns the organization group members if it is updated
    */
    static async updateOrganizationGroupMembers (oldOrganizationMember, newOrganizationMember) {
        const oldOrgMembers = oldOrganizationMember.users.filter(user => user.status === CONSTANTS.ORG_MEMBER_STATUS.ACTIVE);
        const newOrgMembers = newOrganizationMember.users.filter(user => user.status === CONSTANTS.ORG_MEMBER_STATUS.ACTIVE);

        const oldOrgMembersIds = new Set(oldOrgMembers.map(member => member.id));
        const newOrgMembersIds = new Set(newOrgMembers.map(member => member.id));

        const addedMembers = newOrgMembers.filter(member => !oldOrgMembersIds.has(member.id));
        const removedMembers = oldOrgMembers.filter(member => !newOrgMembersIds.has(member.id));

        let orgGroups = [];
        if (addedMembers.length > 0 || removedMembers.length > 0) {
            orgGroups = await this.getGroupsAssociatedWithOrganization(newOrganizationMember.organizationId);
        }

        await Promise.all([
            addedMembers.length > 0 && this.addMembersToOrganizationGroups(addedMembers, orgGroups),
            removedMembers.length > 0 && this.removeMembersFromOrganizationGroups(removedMembers, orgGroups)
        ]);
        return true;
    }

    /**
     * Add members to organization groups
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} members - The members array
     * @param {string} organizationId - The organization id
     * @param {Object} organization - The organization object
     * @returns {Promise<boolean>} - Returns true if the members are added to the organization groups
    */
    static async addMembersToOrganizationGroups (members, orgGroups) {
        await Promise.all(orgGroups.map(async orgGroup => {
            let groupMembers = await GroupMembers.query('groupId').eq(orgGroup.groupId).using('groupId-index').exec();
            const groupMemberIds = [];
            const groupMembersMap = new Map();
            const groupMembersUserDetailsPromise = groupMembers.map(async member => {
                const userDetails = await User.query('id').eq(member.userId).attributes(['id', 'firstName', 'lastName']).exec();
                if (!userDetails || userDetails.length === 0) {
                    return null;
                }
                groupMemberIds.push(member.userId);
                member.firstName = userDetails[0].firstName;
                member.lastName = userDetails[0].lastName;
                groupMembersMap.set(member.userId, member);
                return member;
            }).filter(Boolean);
            groupMembers = await Promise.all(groupMembersUserDetailsPromise);

            const updateItems = [];
            const createItems = [];

            for (const member of members) {
                if (groupMembersMap.has(member.id)) {
                    const existingMember = groupMembersMap.get(member.id);
                    existingMember.status = CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE;
                    existingMember.isAdmin = true;
                    updateItems.push(existingMember);
                } else {
                    createItems.push(
                        new GroupMembers({
                            id: uuidv4(),
                            userId: member.id,
                            isAdmin: true,
                            groupId: orgGroup.groupId,
                            childrenIds: [],
                            status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE
                        })
                    );
                }
            }
            const userIds = [... new Set(createItems.map(item => item.userId))];
            const users = (await Promise.all(
                userIds.map(async userId => await User.query('id').eq(userId).attributes(['id', 'firstName', 'lastName']).exec())
            )).flat();
            const activeSocketConnections = await ConversationService.getActiveSocketConnections(groupMemberIds);
            const groupMembersToProcess = [...createItems, ...updateItems];

            const addGroupMemberEventPromise = groupMembersToProcess.map(async item => {
                const currentUser = users.find(user => user.id === item.userId);
                const messageData = {
                    message: 'New user added to group!',
                    actionType: 'ADD_USER_TO_GROUP',
                    action: 'sendMessage',
                    data: {
                        id: item.id,
                        userId: item.userId,
                        groupId: item.groupId,
                        firstName: currentUser.firstName,
                        lastName: currentUser.lastName,
                        isAdmin: item.isAdmin,
                        status: item.status,
                        childrenIds: item.childrenIds
                    }
                };
                await ConversationService.sendMessagesToConnections(activeSocketConnections, messageData);
                const messagesData = await this.fetchMessages(orgGroup.groupId);

                if (orgGroup.organizationMetaData?.logo) {
                    orgGroup.organizationMetaData.logo = await UploadService.getSignedUrl(orgGroup.organizationMetaData.logo);
                }
                const groupMemberUserDetails = { ...item, firstName: currentUser?.firstName, lastName: currentUser?.lastName };
                const groupDetails = await this.createGroupDetails(groupMemberUserDetails, orgGroup, true, groupMembers, messagesData);
                const newGroup = {
                    message: 'You have been added to a group!',
                    actionType: 'NEW_GROUP_ADDED',
                    action: 'sendMessage',
                    data: {
                        id: item.userId,
                        group: groupDetails,
                        firstName: currentUser.firstName,
                        lastName: currentUser.lastName,
                        isAdmin: item.isAdmin,
                        status: item.status
                    }
                };
                const userActiveSocketConnection = await ConversationService.getActiveSocketConnections([item.userId]);
                await ConversationService.sendMessagesToConnections(userActiveSocketConnection, newGroup);
            });
            await Promise.all(addGroupMemberEventPromise);
            return await Promise.all([
                await this.batchPutInChunks({
                    model: GroupMembers,
                    data: groupMembersToProcess
                })
            ]);
        }));
    }

    static async fetchMessages (groupId, limit = CONSTANTS.TOP_RECENT_GROUP_MESSAGES_LIMIT) {
        const result = await Message.query('groupId').eq(groupId)
            .using('groupId-createdAt-index')
            .sort('descending')
            .limit(limit)
            .exec();

        const messagesPromise = result.map(async message => {
            if (message.mediaName) {
                message.mediaUrl = await UploadService.getSignedUrl(message.mediaName);
                delete message.mediaName;
            }
            if (message.mediaThumbnailName) {
                message.mediaThumbnailUrl = await UploadService.getSignedUrl(message.mediaThumbnailName);
                delete message.mediaThumbnailName;
            }
            if (message.replyMessage?.mediaName) {
                message.replyMessage.mediaUrl = await UploadService.getSignedUrl(message.replyMessage.mediaName);
                delete message.replyMessage.mediaName;
            }
            return message;
        });
        const messages = await Promise.all(messagesPromise);
        const reactions = await ConversationService.getReactionsForMessage({ messageIds: messages.map(message => message.id) });

        return {
            messages,
            reactions,
            lastEvaluatedKey: result.lastKey
        };
    }

    static async createGroupDetails (membership, groupDetails, isOrganizationGroup, groupMembers, messagesData) {
        let groupMembersUserDatails = groupMembers;
        const isUserExistsInGroupMembers = groupMembers.some(member => member.userId === membership.userId);
        if (!isUserExistsInGroupMembers) {
            groupMembersUserDatails = [...groupMembers, membership];
        }
        const groupImage = isOrganizationGroup
            ? groupDetails.organizationMetaData?.logo
            : groupDetails.eventMetaData?.image;
        const groupImageSignedUrl = groupImage ? await UploadService.getSignedUrl(groupImage) : null;
        const formatedGroupDetails = {
            groupId: membership.groupId,
            groupType: groupDetails.groupType,
            groupName: isOrganizationGroup
                ? groupDetails.organizationMetaData?.name
                : groupDetails.eventMetaData?.title,
            groupImage: groupImageSignedUrl,
            isAdmin: membership.isAdmin,
            childrenIds: membership.childrenIds,
            muteConversation: membership.muteConversation,
            organizationId: groupDetails.organizationId,
            eventId: groupDetails.eventId,
            eventStartDate: groupDetails.eventMetaData?.startDateTime,
            eventEndDate: groupDetails.eventMetaData?.endDateTime,
            lastReadMessage: membership.lastReadMessage
        };
        return {
            groupDetails: formatedGroupDetails,
            groupMembers: groupMembersUserDatails,
            groupMessages: messagesData.messages,
            reactions: messagesData.reactions,
            lastEvaluatedKey: messagesData.lastEvaluatedKey
        };
    }

    /**
     * Remove members from organization groups
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} members - The members array
     * @param {Object} orgGroup - The organization group object
     * @returns {Promise<boolean>} - Returns true if the members are removed from the organization groups
    */
    static async removeMembersFromOrganizationGroups (members, orgGroups) {
        return await Promise.all(orgGroups.map(async orgGroup => {
            return await Promise.all(members.map(async member => {
                const isUserInGroup = await this.isUserInGroup(member.id, orgGroup);
                await this.removeUserFromGroup(isUserInGroup, null);
            }));
        }));
    }

    /**
     * Add users to event group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} users - The users array
     * @param {Object} eventGroup - The event group object
     * @returns {Promise<boolean>} - Returns true if the users are added to the event group
    */
    static async addUsersToEventGroup (userIds, eventGroup, child) {
        return await Promise.all(userIds.map(async userId => {
            const isUserInGroup = await this.isUserInGroup(userId, eventGroup);
            await this.addOrUpdateUserToGroup({ id: userId }, eventGroup, isUserInGroup, child);
        }));
    }

    /**
     * Remove users from event group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Array} users - The users array
     * @param {Object} eventGroup - The event group object
     * @param {string} childId - The child id
     * @returns {Promise<boolean>} - Returns true if the users are removed from the event group
    */
    static async removeUsersFromEventGroup (users, eventGroup, childId) {
        return await Promise.all(users.map(async userId => {
            const isUserInGroup = await this.isUserInGroup(userId, eventGroup);
            await this.removeUserFromGroup(isUserInGroup, { id: childId });
        }));
    }

    /**
     * Get event group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {string} eventId - The event id
     * @returns {Promise<Object>} - Returns the event group if it is found
    */
    static async getEventGroup (eventId) {
        const eventGroup = await Groups.query('eventId').eq(eventId).using('eventId-index').where('status').eq('active').exec();
        if (eventGroup?.length > 0) {
            return eventGroup[0];
        }
        return null;
    }

    /**
     * Get guardians for child
     * <AUTHOR>
     * @since 18/09/2024
     * @param {string} childId - The child id
     * @returns {Promise<Array>} - Returns the guardians for the child
    */
    static async getGuardiansForChild (childId) {
        const child = await Child.get(childId);
        return child.guardians;
    }

    /**
     * Add guardians to event group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} eventSignUp - The event sign up object
     * @returns {Promise<boolean>} - Returns true if the guardians are added to the event group
    */
    static async addGuardiansToEventGroup (eventSignUp) {
        if (eventSignUp.paymentDetails.paymentStatus !== CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED) {
            await this.getEventGroupAndAddGuardians(eventSignUp);
        }
        return true;
    }

    /**
     * Add guardians to event group after event signup is updated
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} eventSignUp - The event sign up object
     * @returns {Promise<boolean>} - Returns true if the guardians are added to the event group
    */
    static async addGuardiansToEventGroupAfterUpdate (oldEventSignup, newEventSignup) {
        if (oldEventSignup.paymentDetails.paymentStatus === CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED &&
            newEventSignup.paymentDetails.paymentStatus !== CONSTANTS.PAYMENT_STATUS.PAYMENT_INITIATED) {
            await this.getEventGroupAndAddGuardians(newEventSignup);
        }
        return true;
    }

    /**
     * Get event group and add guardians
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} eventSignUp - The event sign up object
     * @returns {Promise<boolean>} - Returns true if the guardians are added to the event group
    */
    static async getEventGroupAndAddGuardians (eventSignUp) {
        const eventGroup = await this.getEventGroup(eventSignUp.eventId);
        if (eventGroup) {
            const guardians = await this.getGuardiansForChild(eventSignUp.childId);
            await this.addUsersToEventGroup(guardians, eventGroup, { id: eventSignUp.childId });
        }
    }

    /**
     * Remove guardians from event group
     * <AUTHOR>
     * @since 18/09/2024
     * @param {Object} eventSignUp - The event sign up object
     * @returns {Promise<boolean>} - Returns true if the guardians are removed from the event group
    */
    static async removeGuardiansFromEventGroup (eventSignUp) {
        const eventGroup = await this.getEventGroup(eventSignUp.eventId);
        if (eventGroup) {
            const guardians = await this.getGuardiansForChild(eventSignUp.childId);
            await this.removeUsersFromEventGroup(guardians, eventGroup, eventSignUp.childId);
        }
        return true;
    }
}

module.exports = ConversationGroupService;
