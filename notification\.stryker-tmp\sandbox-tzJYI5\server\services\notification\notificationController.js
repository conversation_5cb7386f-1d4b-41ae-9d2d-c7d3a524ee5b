// @ts-nocheck
const notificationService = require('./notificationService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for notification routes.
 */
class NotificationController {
    /**
     * @desc This function is being used to get notification list
     * <AUTHOR>
     * @since 31/01/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async getNotificationList (req, res) {
        try {
            const data = await notificationService.getNotificationList(req, res.locals.user);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in getNotificationList', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to create notification
     * <AUTHOR>
     * @since 31/01/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async createNotification (req, res) {
        try {
            const data = await notificationService.createNotification(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in createNotification', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to update notification read status
     * <AUTHOR>
     * @since 31/01/2024
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async updateNotification (req, res) {
        try {
            const data = await notificationService.updateNotification(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in updateNotification', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to send notification to user or group of users
     * <AUTHOR>
     * @since 20/02/2024
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async sendNotification (req, res) {
        try {
            const data = await notificationService.sendNotification(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in sendNotification', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to send notification to user or group of users
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async notify (req, res) {
        try {
            const data = await notificationService.notify(req);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in notify', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to handle contact us form submissions data
     * @param {Object} req Request
     * @param {function} res Response
    */
    static async contactUs (req, res) {
        try {
            const data = await notificationService.contactUs(req);
            Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        } catch (error) {
            CONSOLE_LOGGER.error('Error in contactUs', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = NotificationController;
