// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const {
  google
} = require('googleapis');
class GoogleSheetsService {
  constructor(keyFilePath, spreadsheetId) {
    if (stryMutAct_9fa48("1776")) {
      {}
    } else {
      stryCov_9fa48("1776");
      this.keyFilePath = keyFilePath;
      this.spreadsheetId = spreadsheetId;
      this.auth = null;
      this.sheets = null;
    }
  }
  async initialize() {
    if (stryMutAct_9fa48("1777")) {
      {}
    } else {
      stryCov_9fa48("1777");
      const credentials = JSON.parse(process.env[stryMutAct_9fa48("1778") ? "" : (stryCov_9fa48("1778"), 'CREDS')]);
      this.auth = new google.auth.GoogleAuth(stryMutAct_9fa48("1779") ? {} : (stryCov_9fa48("1779"), {
        credentials,
        scopes: stryMutAct_9fa48("1780") ? [] : (stryCov_9fa48("1780"), [stryMutAct_9fa48("1781") ? "" : (stryCov_9fa48("1781"), "https://www.googleapis.com/auth/spreadsheets")])
      }));
      const client = await this.auth.getClient();
      this.sheets = google.sheets(stryMutAct_9fa48("1782") ? {} : (stryCov_9fa48("1782"), {
        version: stryMutAct_9fa48("1783") ? "" : (stryCov_9fa48("1783"), 'v4'),
        auth: client
      }));
    }
  }
  async getSheet(sheetName) {
    if (stryMutAct_9fa48("1784")) {
      {}
    } else {
      stryCov_9fa48("1784");
      if (stryMutAct_9fa48("1787") ? false : stryMutAct_9fa48("1786") ? true : stryMutAct_9fa48("1785") ? this.sheets : (stryCov_9fa48("1785", "1786", "1787"), !this.sheets)) {
        if (stryMutAct_9fa48("1788")) {
          {}
        } else {
          stryCov_9fa48("1788");
          await this.initialize();
        }
      }
      return this.sheets.spreadsheets.values.get(stryMutAct_9fa48("1789") ? {} : (stryCov_9fa48("1789"), {
        auth: this.auth,
        spreadsheetId: this.spreadsheetId,
        range: sheetName
      }));
    }
  }
  async appendRow(sheetName, values) {
    if (stryMutAct_9fa48("1790")) {
      {}
    } else {
      stryCov_9fa48("1790");
      if (stryMutAct_9fa48("1793") ? false : stryMutAct_9fa48("1792") ? true : stryMutAct_9fa48("1791") ? this.sheets : (stryCov_9fa48("1791", "1792", "1793"), !this.sheets)) {
        if (stryMutAct_9fa48("1794")) {
          {}
        } else {
          stryCov_9fa48("1794");
          await this.initialize();
        }
      }
      return this.sheets.spreadsheets.values.append(stryMutAct_9fa48("1795") ? {} : (stryCov_9fa48("1795"), {
        auth: this.auth,
        spreadsheetId: this.spreadsheetId,
        range: sheetName,
        valueInputOption: stryMutAct_9fa48("1796") ? "" : (stryCov_9fa48("1796"), 'USER_ENTERED'),
        resource: stryMutAct_9fa48("1797") ? {} : (stryCov_9fa48("1797"), {
          values: stryMutAct_9fa48("1798") ? [] : (stryCov_9fa48("1798"), [values])
        })
      }));
    }
  }
}
module.exports = GoogleSheetsService;