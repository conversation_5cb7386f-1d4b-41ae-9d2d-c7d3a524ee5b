// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const Child = require('./child.model');
const userSchema = new dynamoose.Schema(stryMutAct_9fa48("766") ? {} : (stryCov_9fa48("766"), {
  id: stryMutAct_9fa48("767") ? {} : (stryCov_9fa48("767"), {
    hashKey: stryMutAct_9fa48("768") ? false : (stryCov_9fa48("768"), true),
    required: stryMutAct_9fa48("769") ? false : (stryCov_9fa48("769"), true),
    type: String
  }),
  email: stryMutAct_9fa48("770") ? {} : (stryCov_9fa48("770"), {
    type: String,
    required: stryMutAct_9fa48("771") ? false : (stryCov_9fa48("771"), true),
    rangeKey: stryMutAct_9fa48("772") ? false : (stryCov_9fa48("772"), true),
    index: stryMutAct_9fa48("773") ? {} : (stryCov_9fa48("773"), {
      name: stryMutAct_9fa48("774") ? "" : (stryCov_9fa48("774"), 'email-index'),
      global: stryMutAct_9fa48("775") ? false : (stryCov_9fa48("775"), true),
      project: stryMutAct_9fa48("776") ? false : (stryCov_9fa48("776"), true)
    })
  }),
  firstName: stryMutAct_9fa48("777") ? {} : (stryCov_9fa48("777"), {
    type: String
  }),
  lastName: stryMutAct_9fa48("778") ? {} : (stryCov_9fa48("778"), {
    type: String
  }),
  phoneNumber: stryMutAct_9fa48("779") ? {} : (stryCov_9fa48("779"), {
    type: String
  }),
  countryCode: stryMutAct_9fa48("780") ? {} : (stryCov_9fa48("780"), {
    type: String
  }),
  photoURL: stryMutAct_9fa48("781") ? {} : (stryCov_9fa48("781"), {
    type: String
  }),
  isVerified: stryMutAct_9fa48("782") ? {} : (stryCov_9fa48("782"), {
    type: Number,
    enum: stryMutAct_9fa48("783") ? [] : (stryCov_9fa48("783"), [0, 1]),
    default: 0
  }),
  status: stryMutAct_9fa48("784") ? {} : (stryCov_9fa48("784"), {
    type: String,
    enum: stryMutAct_9fa48("785") ? [] : (stryCov_9fa48("785"), [stryMutAct_9fa48("786") ? "" : (stryCov_9fa48("786"), 'active'), stryMutAct_9fa48("787") ? "" : (stryCov_9fa48("787"), 'inactive')]),
    default: stryMutAct_9fa48("788") ? "" : (stryCov_9fa48("788"), 'inactive')
  }),
  stripeCustomerId: stryMutAct_9fa48("789") ? {} : (stryCov_9fa48("789"), {
    type: String
  }),
  // 1 - deleted
  isDeleted: stryMutAct_9fa48("790") ? {} : (stryCov_9fa48("790"), {
    type: Number,
    enum: stryMutAct_9fa48("791") ? [] : (stryCov_9fa48("791"), [0, 1]),
    default: 0
  }),
  otp: stryMutAct_9fa48("792") ? {} : (stryCov_9fa48("792"), {
    type: Number
  }),
  otpExpiryDate: stryMutAct_9fa48("793") ? {} : (stryCov_9fa48("793"), {
    type: Date
  }),
  children: stryMutAct_9fa48("794") ? {} : (stryCov_9fa48("794"), {
    type: Array,
    schema: stryMutAct_9fa48("795") ? [] : (stryCov_9fa48("795"), [Child]),
    default: stryMutAct_9fa48("796") ? ["Stryker was here"] : (stryCov_9fa48("796"), [])
  }),
  associatedOrganizations: stryMutAct_9fa48("797") ? {} : (stryCov_9fa48("797"), {
    type: Array,
    schema: stryMutAct_9fa48("798") ? [] : (stryCov_9fa48("798"), [stryMutAct_9fa48("799") ? {} : (stryCov_9fa48("799"), {
      type: Object,
      schema: stryMutAct_9fa48("800") ? {} : (stryCov_9fa48("800"), {
        organizationId: stryMutAct_9fa48("801") ? {} : (stryCov_9fa48("801"), {
          type: String,
          required: stryMutAct_9fa48("802") ? false : (stryCov_9fa48("802"), true)
        }),
        role: stryMutAct_9fa48("803") ? {} : (stryCov_9fa48("803"), {
          type: String,
          required: stryMutAct_9fa48("804") ? false : (stryCov_9fa48("804"), true)
        })
      })
    })]),
    default: stryMutAct_9fa48("805") ? ["Stryker was here"] : (stryCov_9fa48("805"), [])
  }),
  partnerInvites: stryMutAct_9fa48("806") ? {} : (stryCov_9fa48("806"), {
    type: Array,
    schema: stryMutAct_9fa48("807") ? [] : (stryCov_9fa48("807"), [stryMutAct_9fa48("808") ? {} : (stryCov_9fa48("808"), {
      type: Object,
      schema: stryMutAct_9fa48("809") ? {} : (stryCov_9fa48("809"), {
        children: stryMutAct_9fa48("810") ? {} : (stryCov_9fa48("810"), {
          type: Array,
          schema: stryMutAct_9fa48("811") ? [] : (stryCov_9fa48("811"), [stryMutAct_9fa48("812") ? {} : (stryCov_9fa48("812"), {
            type: Object,
            schema: stryMutAct_9fa48("813") ? {} : (stryCov_9fa48("813"), {
              childId: stryMutAct_9fa48("814") ? {} : (stryCov_9fa48("814"), {
                type: Child,
                required: stryMutAct_9fa48("815") ? false : (stryCov_9fa48("815"), true)
              }),
              invitedAt: stryMutAct_9fa48("816") ? {} : (stryCov_9fa48("816"), {
                type: Date,
                default: Date.now
              })
            })
          })]),
          default: stryMutAct_9fa48("817") ? ["Stryker was here"] : (stryCov_9fa48("817"), [])
        }),
        inviterPartnerId: stryMutAct_9fa48("818") ? {} : (stryCov_9fa48("818"), {
          type: String,
          required: stryMutAct_9fa48("819") ? false : (stryCov_9fa48("819"), true)
        }),
        inviterPartnerEmail: stryMutAct_9fa48("820") ? {} : (stryCov_9fa48("820"), {
          type: String,
          required: stryMutAct_9fa48("821") ? false : (stryCov_9fa48("821"), true)
        })
      })
    })]),
    default: stryMutAct_9fa48("822") ? ["Stryker was here"] : (stryCov_9fa48("822"), [])
  }),
  sendInvites: stryMutAct_9fa48("823") ? {} : (stryCov_9fa48("823"), {
    type: Array,
    schema: stryMutAct_9fa48("824") ? [] : (stryCov_9fa48("824"), [stryMutAct_9fa48("825") ? {} : (stryCov_9fa48("825"), {
      type: Object,
      schema: stryMutAct_9fa48("826") ? {} : (stryCov_9fa48("826"), {
        invitedPartnerEmail: stryMutAct_9fa48("827") ? {} : (stryCov_9fa48("827"), {
          type: String,
          required: stryMutAct_9fa48("828") ? false : (stryCov_9fa48("828"), true)
        }),
        children: stryMutAct_9fa48("829") ? {} : (stryCov_9fa48("829"), {
          type: Array,
          schema: stryMutAct_9fa48("830") ? [] : (stryCov_9fa48("830"), [stryMutAct_9fa48("831") ? {} : (stryCov_9fa48("831"), {
            type: Object,
            schema: stryMutAct_9fa48("832") ? {} : (stryCov_9fa48("832"), {
              childId: stryMutAct_9fa48("833") ? {} : (stryCov_9fa48("833"), {
                type: Child,
                required: stryMutAct_9fa48("834") ? false : (stryCov_9fa48("834"), true)
              }),
              status: stryMutAct_9fa48("835") ? {} : (stryCov_9fa48("835"), {
                type: String,
                enum: stryMutAct_9fa48("836") ? [] : (stryCov_9fa48("836"), [stryMutAct_9fa48("837") ? "" : (stryCov_9fa48("837"), 'pending'), stryMutAct_9fa48("838") ? "" : (stryCov_9fa48("838"), 'accepted')]),
                default: stryMutAct_9fa48("839") ? "" : (stryCov_9fa48("839"), 'pending')
              })
            })
          })]),
          default: stryMutAct_9fa48("840") ? ["Stryker was here"] : (stryCov_9fa48("840"), [])
        })
      })
    })]),
    default: stryMutAct_9fa48("841") ? ["Stryker was here"] : (stryCov_9fa48("841"), [])
  }),
  membershipsPurchased: stryMutAct_9fa48("842") ? {} : (stryCov_9fa48("842"), {
    type: Array,
    schema: stryMutAct_9fa48("843") ? [] : (stryCov_9fa48("843"), [stryMutAct_9fa48("844") ? {} : (stryCov_9fa48("844"), {
      type: Object,
      schema: stryMutAct_9fa48("845") ? {} : (stryCov_9fa48("845"), {
        organizationId: stryMutAct_9fa48("846") ? {} : (stryCov_9fa48("846"), {
          type: String,
          required: stryMutAct_9fa48("847") ? false : (stryCov_9fa48("847"), true)
        }),
        fundraiserSignupId: stryMutAct_9fa48("848") ? {} : (stryCov_9fa48("848"), {
          type: String,
          required: stryMutAct_9fa48("849") ? false : (stryCov_9fa48("849"), true)
        }),
        membershipType: stryMutAct_9fa48("850") ? {} : (stryCov_9fa48("850"), {
          type: String,
          required: stryMutAct_9fa48("851") ? false : (stryCov_9fa48("851"), true),
          enum: stryMutAct_9fa48("852") ? [] : (stryCov_9fa48("852"), [stryMutAct_9fa48("853") ? "" : (stryCov_9fa48("853"), 'family')])
        }),
        startDate: stryMutAct_9fa48("854") ? {} : (stryCov_9fa48("854"), {
          type: Date,
          required: stryMutAct_9fa48("855") ? false : (stryCov_9fa48("855"), true)
        }),
        membershipId: stryMutAct_9fa48("856") ? {} : (stryCov_9fa48("856"), {
          type: String,
          required: stryMutAct_9fa48("857") ? false : (stryCov_9fa48("857"), true)
        }),
        endDate: stryMutAct_9fa48("858") ? {} : (stryCov_9fa48("858"), {
          type: Date,
          required: stryMutAct_9fa48("859") ? false : (stryCov_9fa48("859"), true)
        })
      })
    })]),
    default: stryMutAct_9fa48("860") ? ["Stryker was here"] : (stryCov_9fa48("860"), [])
  }),
  // 1 - user, 3 - org admin, 4 - admin
  role: stryMutAct_9fa48("861") ? {} : (stryCov_9fa48("861"), {
    type: Number,
    enum: stryMutAct_9fa48("862") ? [] : (stryCov_9fa48("862"), [1, 2, 3, 4]),
    default: 1,
    index: stryMutAct_9fa48("863") ? {} : (stryCov_9fa48("863"), {
      global: stryMutAct_9fa48("864") ? false : (stryCov_9fa48("864"), true),
      name: stryMutAct_9fa48("865") ? "" : (stryCov_9fa48("865"), 'role-index'),
      project: stryMutAct_9fa48("866") ? false : (stryCov_9fa48("866"), true)
    })
  }),
  accessLevel: stryMutAct_9fa48("867") ? {} : (stryCov_9fa48("867"), {
    type: String,
    enum: stryMutAct_9fa48("868") ? [] : (stryCov_9fa48("868"), [stryMutAct_9fa48("869") ? "" : (stryCov_9fa48("869"), 'app'), stryMutAct_9fa48("870") ? "" : (stryCov_9fa48("870"), 'org_app'), stryMutAct_9fa48("871") ? "" : (stryCov_9fa48("871"), 'root')]),
    default: stryMutAct_9fa48("872") ? "" : (stryCov_9fa48("872"), 'app'),
    index: stryMutAct_9fa48("873") ? {} : (stryCov_9fa48("873"), {
      global: stryMutAct_9fa48("874") ? false : (stryCov_9fa48("874"), true),
      name: stryMutAct_9fa48("875") ? "" : (stryCov_9fa48("875"), 'accessLevel-index'),
      project: stryMutAct_9fa48("876") ? false : (stryCov_9fa48("876"), true)
    })
  }),
  fcmToken: stryMutAct_9fa48("877") ? {} : (stryCov_9fa48("877"), {
    type: String
  }),
  token: stryMutAct_9fa48("878") ? {} : (stryCov_9fa48("878"), {
    type: String
  }),
  refreshToken: stryMutAct_9fa48("879") ? {} : (stryCov_9fa48("879"), {
    type: String
  }),
  provider: stryMutAct_9fa48("880") ? {} : (stryCov_9fa48("880"), {
    type: String,
    default: stryMutAct_9fa48("881") ? "" : (stryCov_9fa48("881"), 'email')
  }),
  isFeedsGenerated: stryMutAct_9fa48("882") ? {} : (stryCov_9fa48("882"), {
    type: Boolean
  }),
  hasReadChatGuidelines: stryMutAct_9fa48("883") ? {} : (stryCov_9fa48("883"), {
    type: Boolean,
    default: stryMutAct_9fa48("884") ? true : (stryCov_9fa48("884"), false)
  }),
  isGuestUser: stryMutAct_9fa48("885") ? {} : (stryCov_9fa48("885"), {
    type: Boolean
  }),
  createdBy: stryMutAct_9fa48("886") ? {} : (stryCov_9fa48("886"), {
    type: String
  }),
  updatedBy: stryMutAct_9fa48("887") ? {} : (stryCov_9fa48("887"), {
    type: String
  })
}), stryMutAct_9fa48("888") ? {} : (stryCov_9fa48("888"), {
  timestamps: stryMutAct_9fa48("889") ? {} : (stryCov_9fa48("889"), {
    createdAt: stryMutAct_9fa48("890") ? "" : (stryCov_9fa48("890"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("891") ? "" : (stryCov_9fa48("891"), 'updatedAt')
  })
}));
module.exports = dynamoose.model(stryMutAct_9fa48("892") ? "" : (stryCov_9fa48("892"), 'User'), userSchema);