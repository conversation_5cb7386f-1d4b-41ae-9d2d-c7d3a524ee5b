/* eslint-disable max-len */
// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const HTTPStatus = require('../util/http-status');
const crypto = require('crypto');
const secretKey = process.env.HMAC_SECRET_KEY;

/**
 * This class reprasents common utilities for application
 */
class Utils {
  static errorResponse() {
    if (stryMutAct_9fa48("1969")) {
      {}
    } else {
      stryCov_9fa48("1969");
      return JSON.parse(JSON.stringify(stryMutAct_9fa48("1970") ? {} : (stryCov_9fa48("1970"), {
        status: 0,
        data: {},
        message: stryMutAct_9fa48("1971") ? "Stryker was here!" : (stryCov_9fa48("1971"), '')
      })));
    }
  }
  static successResponse() {
    if (stryMutAct_9fa48("1972")) {
      {}
    } else {
      stryCov_9fa48("1972");
      return JSON.parse(JSON.stringify(stryMutAct_9fa48("1973") ? {} : (stryCov_9fa48("1973"), {
        status: 1,
        data: {},
        message: stryMutAct_9fa48("1974") ? "Stryker was here!" : (stryCov_9fa48("1974"), '')
      })));
    }
  }

  /**
  * This function is being used to add pagination for user table
  * @auther Growexx
  * @param {string} error Error Message
  * @param {Object} data Object to send in response
  * @param {Object} res Response Object
  * @param {string} successMessage success message
  * @param {Object} additionalData additional data outside of data object in response
  * @param {string} successMessageVars
  * @since 01/03/2021
  */
  static sendResponse(error, data, res, successMessage, successMessageVars) {
    if (stryMutAct_9fa48("1975")) {
      {}
    } else {
      stryCov_9fa48("1975");
      let responseObject;
      if (stryMutAct_9fa48("1977") ? false : stryMutAct_9fa48("1976") ? true : (stryCov_9fa48("1976", "1977"), error)) {
        if (stryMutAct_9fa48("1978")) {
          {}
        } else {
          stryCov_9fa48("1978");
          let status;
          responseObject = Utils.errorResponse();
          if (stryMutAct_9fa48("1981") ? typeof error !== 'object' : stryMutAct_9fa48("1980") ? false : stryMutAct_9fa48("1979") ? true : (stryCov_9fa48("1979", "1980", "1981"), typeof error === (stryMutAct_9fa48("1982") ? "" : (stryCov_9fa48("1982"), 'object')))) {
            if (stryMutAct_9fa48("1983")) {
              {}
            } else {
              stryCov_9fa48("1983");
              responseObject.message = error.message ? error.message : res.__(stryMutAct_9fa48("1984") ? "" : (stryCov_9fa48("1984"), 'ERROR_MSG'));
              status = error.statusCode ? error.statusCode : HTTPStatus.BAD_REQUEST;
            }
          } else {
            if (stryMutAct_9fa48("1985")) {
              {}
            } else {
              stryCov_9fa48("1985");
              responseObject.message = res.__(error);
              status = HTTPStatus.BAD_REQUEST;
            }
          }
          responseObject.data = error.data;
          res.status(status).send(responseObject);
        }
      } else {
        if (stryMutAct_9fa48("1986")) {
          {}
        } else {
          stryCov_9fa48("1986");
          responseObject = Utils.successResponse();
          responseObject.message = successMessageVars ? res.__.apply(stryMutAct_9fa48("1987") ? "Stryker was here!" : (stryCov_9fa48("1987"), ''), (stryMutAct_9fa48("1988") ? [] : (stryCov_9fa48("1988"), [successMessage])).concat(successMessageVars)) : successMessage;
          responseObject.data = data;
          res.status(HTTPStatus.OK).send(responseObject);
        }
      }
    }
  }
  static generateHmac(url) {
    if (stryMutAct_9fa48("1989")) {
      {}
    } else {
      stryCov_9fa48("1989");
      const currentDate = MOMENT().utc().format(stryMutAct_9fa48("1990") ? "" : (stryCov_9fa48("1990"), 'MMYYYYDD')); // 03202426
      const reqURL = url; // /feed/path
      const message = stryMutAct_9fa48("1991") ? currentDate - reqURL : (stryCov_9fa48("1991"), currentDate + reqURL);
      const calculatedHmac = crypto.createHmac(stryMutAct_9fa48("1992") ? "" : (stryCov_9fa48("1992"), 'sha256'), secretKey).update(message).digest(stryMutAct_9fa48("1993") ? "" : (stryCov_9fa48("1993"), 'hex'));
      return calculatedHmac;
    }
  }

  /**
  * This function is being used to overwrite request function for adding reqtoken
  * @auther Growexx
  * @param {import('supertest')} request
  * @since 28/03/2024
  */
  static addCommonReqTokenForHMac(request) {
    if (stryMutAct_9fa48("1994")) {
      {}
    } else {
      stryCov_9fa48("1994");
      const originalEnd = request.Test.prototype.end;
      request.Test.prototype.end = function (callback) {
        if (stryMutAct_9fa48("1995")) {
          {}
        } else {
          stryCov_9fa48("1995");
          const currentParsedUrl = new URL(this.url);
          this.set(stryMutAct_9fa48("1996") ? "" : (stryCov_9fa48("1996"), 'reqtoken'), Utils.generateHmac(stryMutAct_9fa48("1997") ? `` : (stryCov_9fa48("1997"), `${currentParsedUrl.pathname}`)));
          return originalEnd.call(this, callback);
        }
      };
    }
  }

  /**
   * @description Generates a prefix for a key
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} keyPrefix - The key prefix
   * @returns {String} The prefix
   */
  static generatePrefixForKey({
    versionPrefix,
    keyPrefix
  }) {
    if (stryMutAct_9fa48("1998")) {
      {}
    } else {
      stryCov_9fa48("1998");
      return stryMutAct_9fa48("1999") ? `` : (stryCov_9fa48("1999"), `${versionPrefix}:${keyPrefix}`);
    }
  }

  /**
   * @description Generates a key
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} keyPrefix - The key prefix
   * @param {String} id - The id
   * @returns {String} The key
   */
  static generateKey({
    versionPrefix,
    keyPrefix,
    id
  }) {
    if (stryMutAct_9fa48("2000")) {
      {}
    } else {
      stryCov_9fa48("2000");
      return stryMutAct_9fa48("2001") ? `` : (stryCov_9fa48("2001"), `${this.generatePrefixForKey(stryMutAct_9fa48("2002") ? {} : (stryCov_9fa48("2002"), {
        versionPrefix,
        keyPrefix
      }))}:${id}`);
    }
  }

  /**
   * @description Generates a key for a user
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} userId - The user id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getUserKey({
    versionPrefix,
    userId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2003") ? true : (stryCov_9fa48("2003"), false)
  }) {
    if (stryMutAct_9fa48("2004")) {
      {}
    } else {
      stryCov_9fa48("2004");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2005") ? {} : (stryCov_9fa48("2005"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_USER_EVENTS
      })) : this.generateKey(stryMutAct_9fa48("2006") ? {} : (stryCov_9fa48("2006"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_USER_EVENTS,
        id: userId
      }));
    }
  }

  /**
   * @description Generates a key for a registered user
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} userId - The user id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getRegisteredUserKey({
    versionPrefix,
    userId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2007") ? true : (stryCov_9fa48("2007"), false)
  }) {
    if (stryMutAct_9fa48("2008")) {
      {}
    } else {
      stryCov_9fa48("2008");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2009") ? {} : (stryCov_9fa48("2009"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_USER_REGISTERED_EVENTS
      })) : this.generateKey(stryMutAct_9fa48("2010") ? {} : (stryCov_9fa48("2010"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_USER_REGISTERED_EVENTS,
        id: userId
      }));
    }
  }

  /**
   * @description Generates a key for a calendar user
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} userId - The user id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getCalendarUserKey({
    versionPrefix,
    userId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2011") ? true : (stryCov_9fa48("2011"), false)
  }) {
    if (stryMutAct_9fa48("2012")) {
      {}
    } else {
      stryCov_9fa48("2012");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2013") ? {} : (stryCov_9fa48("2013"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_USER_CALENDAR_EVENTS
      })) : this.generateKey(stryMutAct_9fa48("2014") ? {} : (stryCov_9fa48("2014"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_USER_CALENDAR_EVENTS,
        id: userId
      }));
    }
  }

  /**
   * @description Generates a key for a child
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} childId - The child id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getChildKey({
    versionPrefix,
    childId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2015") ? true : (stryCov_9fa48("2015"), false)
  }) {
    if (stryMutAct_9fa48("2016")) {
      {}
    } else {
      stryCov_9fa48("2016");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2017") ? {} : (stryCov_9fa48("2017"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_CHILD_EVENTS
      })) : this.generateKey(stryMutAct_9fa48("2018") ? {} : (stryCov_9fa48("2018"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_CHILD_EVENTS,
        id: childId
      }));
    }
  }

  /**
   * @description Generates a key for a registered child
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} childId - The child id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getRegisteredChildKey({
    versionPrefix,
    childId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2019") ? true : (stryCov_9fa48("2019"), false)
  }) {
    if (stryMutAct_9fa48("2020")) {
      {}
    } else {
      stryCov_9fa48("2020");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2021") ? {} : (stryCov_9fa48("2021"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_CHILD_REGISTERED_EVENTS
      })) : this.generateKey(stryMutAct_9fa48("2022") ? {} : (stryCov_9fa48("2022"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_CHILD_REGISTERED_EVENTS,
        id: childId
      }));
    }
  }

  /**
   * @description Generates a key for a calendar child
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} childId - The child id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getCalendarChildKey({
    versionPrefix,
    childId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2023") ? true : (stryCov_9fa48("2023"), false)
  }) {
    if (stryMutAct_9fa48("2024")) {
      {}
    } else {
      stryCov_9fa48("2024");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2025") ? {} : (stryCov_9fa48("2025"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_CHILD_CALENDAR_EVENTS
      })) : this.generateKey(stryMutAct_9fa48("2026") ? {} : (stryCov_9fa48("2026"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_CHILD_CALENDAR_EVENTS,
        id: childId
      }));
    }
  }

  /**
   * @description Generates a key for a child details
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} childId - The child id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getChildDetailsKey({
    versionPrefix,
    childId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2027") ? true : (stryCov_9fa48("2027"), false)
  }) {
    if (stryMutAct_9fa48("2028")) {
      {}
    } else {
      stryCov_9fa48("2028");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2029") ? {} : (stryCov_9fa48("2029"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_CHILD_DETAILS
      })) : this.generateKey(stryMutAct_9fa48("2030") ? {} : (stryCov_9fa48("2030"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_CHILD_DETAILS,
        id: childId
      }));
    }
  }

  /**
   * @description Generates a key for a event details
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} eventId - The event id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getEventDetailsKey({
    versionPrefix,
    eventId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2031") ? true : (stryCov_9fa48("2031"), false)
  }) {
    if (stryMutAct_9fa48("2032")) {
      {}
    } else {
      stryCov_9fa48("2032");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2033") ? {} : (stryCov_9fa48("2033"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_EVENT_DETAILS
      })) : this.generateKey(stryMutAct_9fa48("2034") ? {} : (stryCov_9fa48("2034"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_EVENT_DETAILS,
        id: eventId
      }));
    }
  }

  /**
   * @description Generates a key for a fundraiser details
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} fundraiserId - The fundraiser id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getFundraiserDetailsKey({
    versionPrefix,
    fundraiserId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2035") ? true : (stryCov_9fa48("2035"), false)
  }) {
    if (stryMutAct_9fa48("2036")) {
      {}
    } else {
      stryCov_9fa48("2036");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2037") ? {} : (stryCov_9fa48("2037"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_FUNDRAISER_DETAILS
      })) : this.generateKey(stryMutAct_9fa48("2038") ? {} : (stryCov_9fa48("2038"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_FUNDRAISER_DETAILS,
        id: fundraiserId
      }));
    }
  }

  /**
   * @description Generates a key for a post details
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} postId - The post id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getPostDetailsKey({
    versionPrefix,
    postId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2039") ? true : (stryCov_9fa48("2039"), false)
  }) {
    if (stryMutAct_9fa48("2040")) {
      {}
    } else {
      stryCov_9fa48("2040");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2041") ? {} : (stryCov_9fa48("2041"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_POST_DETAILS
      })) : this.generateKey(stryMutAct_9fa48("2042") ? {} : (stryCov_9fa48("2042"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_POST_DETAILS,
        id: postId
      }));
    }
  }

  /**
   * @description Generates a key for a organization details
   * <AUTHOR>
   * @param {String} versionPrefix - The version prefix
   * @param {String} organizationId - The organization id
   * @param {Boolean} shouldGenerateKeyPrefix - Whether to generate the key
   * @returns {String} The key
   */
  static getOrganizationDetailsKey({
    versionPrefix,
    organizationId,
    shouldGenerateKeyPrefix = stryMutAct_9fa48("2043") ? true : (stryCov_9fa48("2043"), false)
  }) {
    if (stryMutAct_9fa48("2044")) {
      {}
    } else {
      stryCov_9fa48("2044");
      return shouldGenerateKeyPrefix ? this.generatePrefixForKey(stryMutAct_9fa48("2045") ? {} : (stryCov_9fa48("2045"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_ORGANIZATION_DETAILS
      })) : this.generateKey(stryMutAct_9fa48("2046") ? {} : (stryCov_9fa48("2046"), {
        versionPrefix,
        keyPrefix: CONSTANTS.KEY_FOR_ORGANIZATION_DETAILS,
        id: organizationId
      }));
    }
  }
}
module.exports = Utils;