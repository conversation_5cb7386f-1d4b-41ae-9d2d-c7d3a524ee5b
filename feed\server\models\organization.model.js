const dynamoose = require('dynamoose');
const { v4: uuidv4 } = require('uuid');

const organizationSchema = new dynamoose.Schema({
    id: {
        hashKey: true,
        type: String,
        default: () => uuidv4()
    },
    name: {
        type: String,
        required: true,
        index: {
            global: true,
            name: 'name-index',
            project: true
        }
    },
    zipCode: {
        type: String,
        required: true
    },
    applicableAgeRange: {
        type: String
    },
    organizationType: {
        type: String
    },
    parentOrganization: {
        type: String,
        index: {
            global: true,
            name: 'parentOrganization-index',
            project: true
        }
    },
    parentOrganizationName: {
        type: String
    },
    associatedOrganizations: {
        type: Array,
        schema: [String],
        default: []
    },
    archievedAssociatedOrganizations: {
        type: Array,
        schema: [String],
        default: []
    },
    logo: {
        type: String
    },
    paymentDetails: {
        type: Object,
        schema: {
            stripeConnectAccountId: {
                type: String
            },
            stripeOnboardingStatus: {
                type: String,
                enum: ['active', 'inactive', 'payoutPaused', 'rejectedFraud', 'rejectedOther'],
                default: 'inactive'
            },
            venmoPaymentURL: {
                type: String
            }
        },
        default: { stripeConnectAccountId: '', stripeOnboardingStatus: 'inactive', venmoPaymentURL: '' }
    },
    paymentInstructions: {
        type: Object,
        schema: {
            cashInstruction: {
                type: String
            },
            chequeInstruction: {
                type: String
            },
            venmoInstruction: {
                type: String
            }
        },
        default: { cashInstruction: '', chequeInstruction: '', venmoInstruction: '' }
    },
    allowedPaymentType: {
        type: Object,
        schema: {
            cash: {
                type: Boolean
            },
            cheque: {
                type: Boolean
            },
            venmo: {
                type: Boolean
            },
            stripe: {
                type: Boolean
            }
        },
        default: { cash: true, cheque: false, venmo: false, stripe: false }
    },
    platformFee: {
        type: Number
    },
    platformFeeCoveredBy: {
        type: String,
        enum: ['organization', 'parent', 'optional']
    },
    minPlatformFeeAmount: {
        type: Number
    },
    category: {
        type: String,
        enum: ['PTO', 'School', 'Homeroom', 'Club', 'Super Organization', 'Business'],
        required: true,
        index: {
            global: true,
            name: 'category-index',
            project: true
        }
    },
    address: {
        type: String,
        required: true
    },
    country: {
        type: String,
        required: true
    },
    state: {
        type: String,
        required: true
    },
    city: {
        type: String,
        required: true
    },
    isEnabled: {
        type: Number,
        // 0 = disabled
        enum: [0, 1],
        default: 1
    },
    isDeleted: {
        type: Number,
        // 1 = deleted
        enum: [0, 1],
        default: 0
    },
    createdBy: {
        type: String
    },
    updatedBy: {
        type: String
    }
}, {
    timestamps: {
        createdAt: 'createdAt',
        updatedAt: 'updatedAt'
    }
});

module.exports = dynamoose.model('Organization', organizationSchema);
