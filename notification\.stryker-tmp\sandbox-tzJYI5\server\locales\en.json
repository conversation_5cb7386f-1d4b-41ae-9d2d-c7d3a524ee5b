{"SUCCESS": "Success", "REGISTER_SUCCESS": "User registration successful", "ALREADY_REGISTER": "This user is already registered with us.", "INACTIVE_USER": "Please activate your user by verify email that has sent earlier", "INVALID_OTP": "The otp that has entered is incorrect", "USER_VERIFY_SUCCESS": "Email is verified successfully", "USER_NOT_FOUND": "Invalid user request", "INVALID_REQUEST": "Request is invalid", "RESEND_OTP_SUCCESS": "Email resend successful", "LOGIN_SUCCESS": "User successfully logged in", "LOGIN_FAILED": "Invalid user credentials.", "FIELD_REQUIRED": "%s is required", "FIELD_NOT_VALID": "%s is not valid.", "PORTAL_EXISTS": "Jira portal is already exists", "ERROR_MSG": "Something went wrong. please try again.", "ACCESS_DENIED": "You are not authorized to access this resource.", "DEACTIVATE_ACCOUNT_BY_ADMIN": "You account has been deactivate.", "PHOTO_DELETE_SUCCESS": "Your profile picture has been deleted successfully.", "INVALID_JIRA_CREDENTIALS": "The entered email and token are not correct. Please verify it.", "SELECT_EMPLOYEE": "Select the employee first", "INVALID_PORTAL_ID": "Jira portal id is invalid.", "PASSWORD_NOT_MATCH": "The passwords do not match", "CHANGE_PASSWORD_SUCCESS": "Password changed successfully", "FILE_NOT_FOUND": "File not found", "TEMPLATE_NAME_REQUIRED": "Template name is required", "TEMPLATE_SUBJECT_REQUIRED": "Template subject is required", "FORGOT_PASSWORD_LINK_SENT_SUCCESS": "An email has been sent. Please follow instructions on it.", "LINK_IS_VALID": "<PERSON> validated successfully.", "RESET_PASSWORD_SUCCESS": "Your password has been reset successfully.", "SIGNIN_SUCCESS": "User successfully logged in.", "NOTIFICATION_NOT_FOUND": "Notification not found.", "NOTIFICATION_ALREADY_READ": "Notification already read.", "Notification already read.": "Notification already read.", "CHILD_NOT_FOUND": "Child not found.", "ORGANIZATION_NOT_FOUND": "Organization not found.", "Child not found.": "Child not found.", "Notification not found.": "Notification not found."}