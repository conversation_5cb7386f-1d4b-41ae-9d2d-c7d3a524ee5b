/**
 * @name Server Configuration
 */function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const compression = require('compression');
const express = require('express');
const cookieParser = require('cookie-parser');
const app = express();
const bodyParser = require('body-parser');
const swaggerUi = require('swagger-ui-express');
const swaggerDoc = require('swagger-jsdoc');
const swaggerDef = require('./public/swagger');
const cors = require('cors');
const methodOverride = require('method-override');
const i18n = require('i18n');
const morgan = require('morgan');
const helmet = require('helmet');
const DynamoDBConnection = require('./connection');
const notificationRoutes = require('./routes/notificationRoute');
const HmacMiddleware = require('./middleware/hmac');
// Global Variables
global.CONSOLE_LOGGER = require('./util/logger');
global.CONSTANTS = require('./util/constants');
global.MESSAGES = require('./locales/en.json');
global.MOMENT = require('moment');
global._ = require('lodash');
const connectDB = stryMutAct_9fa48("979") ? () => undefined : (stryCov_9fa48("979"), (() => {
  const connectDB = async () => await DynamoDBConnection.connectToDB();
  return connectDB;
})());
if (stryMutAct_9fa48("982") ? process.env.NODE_ENV === 'testing' : stryMutAct_9fa48("981") ? false : stryMutAct_9fa48("980") ? true : (stryCov_9fa48("980", "981", "982"), process.env.NODE_ENV !== (stryMutAct_9fa48("983") ? "" : (stryCov_9fa48("983"), 'testing')))) {
  if (stryMutAct_9fa48("984")) {
    {}
  } else {
    stryCov_9fa48("984");
    connectDB();
  }
}
if (stryMutAct_9fa48("987") ? process.env.LOCAL !== 'true' : stryMutAct_9fa48("986") ? false : stryMutAct_9fa48("985") ? true : (stryCov_9fa48("985", "986", "987"), process.env.LOCAL === (stryMutAct_9fa48("988") ? "" : (stryCov_9fa48("988"), 'true')))) {
  if (stryMutAct_9fa48("989")) {
    {}
  } else {
    stryCov_9fa48("989");
    app.use(express.static(stryMutAct_9fa48("990") ? "" : (stryCov_9fa48("990"), '../jsdocs/jsdocs')));
    app.use(stryMutAct_9fa48("991") ? "" : (stryCov_9fa48("991"), '/auth/coverage'), express.static(stryMutAct_9fa48("992") ? `` : (stryCov_9fa48("992"), `${__dirname}/../coverage/lcov-report`)));
  }
}

// Configure i18n for multilingual
i18n.configure(stryMutAct_9fa48("993") ? {} : (stryCov_9fa48("993"), {
  locales: stryMutAct_9fa48("994") ? [] : (stryCov_9fa48("994"), [stryMutAct_9fa48("995") ? "" : (stryCov_9fa48("995"), 'en')]),
  directory: stryMutAct_9fa48("996") ? `` : (stryCov_9fa48("996"), `${__dirname}/locales`),
  extension: stryMutAct_9fa48("997") ? "" : (stryCov_9fa48("997"), '.json'),
  prefix: stryMutAct_9fa48("998") ? "Stryker was here!" : (stryCov_9fa48("998"), ''),
  logDebugFn(msg) {
    if (stryMutAct_9fa48("999")) {
      {}
    } else {
      stryCov_9fa48("999");
      if (stryMutAct_9fa48("1002") ? process.env.LOCAL !== 'true' : stryMutAct_9fa48("1001") ? false : stryMutAct_9fa48("1000") ? true : (stryCov_9fa48("1000", "1001", "1002"), process.env.LOCAL === (stryMutAct_9fa48("1003") ? "" : (stryCov_9fa48("1003"), 'true')))) {
        if (stryMutAct_9fa48("1004")) {
          {}
        } else {
          stryCov_9fa48("1004");
          CONSOLE_LOGGER.debug(stryMutAct_9fa48("1005") ? `` : (stryCov_9fa48("1005"), `i18n::${CONSTANTS.LOG_LEVEL}`), msg);
        }
      }
    }
  }
}));
app.use(compression());
app.use(helmet());
app.use(i18n.init);
app.use(cookieParser());
app.use(bodyParser.urlencoded(stryMutAct_9fa48("1006") ? {} : (stryCov_9fa48("1006"), {
  limit: stryMutAct_9fa48("1007") ? "" : (stryCov_9fa48("1007"), '50mb'),
  extended: stryMutAct_9fa48("1008") ? false : (stryCov_9fa48("1008"), true)
})));
app.use(bodyParser.json(stryMutAct_9fa48("1009") ? {} : (stryCov_9fa48("1009"), {
  limit: stryMutAct_9fa48("1010") ? "" : (stryCov_9fa48("1010"), '50mb'),
  extended: stryMutAct_9fa48("1011") ? false : (stryCov_9fa48("1011"), true)
})));
app.use(cors(stryMutAct_9fa48("1012") ? {} : (stryCov_9fa48("1012"), {
  origin: stryMutAct_9fa48("1013") ? false : (stryCov_9fa48("1013"), true),
  methods: stryMutAct_9fa48("1014") ? "" : (stryCov_9fa48("1014"), 'GET,HEAD,PUT,PATCH,POST,DELETE'),
  credentials: stryMutAct_9fa48("1015") ? false : (stryCov_9fa48("1015"), true),
  exposedHeaders: stryMutAct_9fa48("1016") ? [] : (stryCov_9fa48("1016"), [stryMutAct_9fa48("1017") ? "" : (stryCov_9fa48("1017"), 'x-auth-token')])
})));
app.use(morgan(stryMutAct_9fa48("1018") ? "" : (stryCov_9fa48("1018"), 'dev')));
app.use(methodOverride());

// Landing Page
app.get(stryMutAct_9fa48("1019") ? "" : (stryCov_9fa48("1019"), '/notification'), HmacMiddleware, (req, res) => {
  if (stryMutAct_9fa48("1020")) {
    {}
  } else {
    stryCov_9fa48("1020");
    res.send(stryMutAct_9fa48("1021") ? {} : (stryCov_9fa48("1021"), {
      status: stryMutAct_9fa48("1022") ? "" : (stryCov_9fa48("1022"), 'ok'),
      date: MOMENT(),
      message: stryMutAct_9fa48("1023") ? "" : (stryCov_9fa48("1023"), 'Hello from Notification service.')
    }));
  }
});
app.use(stryMutAct_9fa48("1024") ? "" : (stryCov_9fa48("1024"), '/notification'), notificationRoutes);
const spec = swaggerDoc(swaggerDef);
if (stryMutAct_9fa48("1027") ? process.env.NODE_ENV === 'production' : stryMutAct_9fa48("1026") ? false : stryMutAct_9fa48("1025") ? true : (stryCov_9fa48("1025", "1026", "1027"), process.env.NODE_ENV !== (stryMutAct_9fa48("1028") ? "" : (stryCov_9fa48("1028"), 'production')))) {
  if (stryMutAct_9fa48("1029")) {
    {}
  } else {
    stryCov_9fa48("1029");
    app.use(stryMutAct_9fa48("1030") ? "" : (stryCov_9fa48("1030"), '/notification/api-docs/'), swaggerUi.serve, swaggerUi.setup(spec));
  }
}
module.exports = app;