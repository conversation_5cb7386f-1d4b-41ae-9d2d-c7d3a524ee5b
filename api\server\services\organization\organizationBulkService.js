const OrganizationValidator = require('./organizationValidator');
const { v4: uuidv4 } = require('uuid');
const User = require('../../models/user.model');
const OrganizationMember = require('../../models/organizationMember.model');
const OrganizationRepository = require('../repositories/organizationRepository');
const UserRepository = require('../repositories/userRepository');
const OrganizationService = require('./organizationService');
const AwsOpenSearchService = require('../../util/opensearch');
const CONSTANTS = require('../../util/constants');

class OrganizationBulkService {

    static async addAssociatedOrganizations (req, loggedInUser, locale) {
        const { associatedOrgsDetails, email, adminFirstName, adminLastName, parentOrgId } = req.body;
        const parentOrgDetails = await OrganizationRepository.getOrganizationById(parentOrgId);

        if (!parentOrgDetails) {
            throw {
                message: MESSAGES.ORGANIZATION_DOES_NOT_EXIST, statusCode: 400
            };
        }

        let alreadyPresentAssociatedOrgs = await Promise.all(associatedOrgsDetails.map(async (orgDetails) =>
            await OrganizationRepository.orgExists(orgDetails.name, orgDetails.zipCode)));
        alreadyPresentAssociatedOrgs = alreadyPresentAssociatedOrgs.flat();

        const orgsToAssociate = associatedOrgsDetails.filter(orgDetails => !alreadyPresentAssociatedOrgs
            .some(alreadyPresentOrg => alreadyPresentOrg.name === orgDetails.name && alreadyPresentOrg.zipCode === orgDetails.zipCode));

        const newOrgIds = [];
        const createdAssociatedOrgs = orgsToAssociate.map((orgDetails) => {
            OrganizationService.parseRequest({ body: orgDetails });
            orgDetails.orgName = orgDetails.name;
            orgDetails.email = email.toLowerCase();
            orgDetails.adminFirstName = adminFirstName;
            orgDetails.adminLastName = adminLastName;
            const validator = new OrganizationValidator(orgDetails, locale);
            validator.validate();
            const createdOrg = this.createOrganization(orgDetails, loggedInUser);
            newOrgIds.push(createdOrg.id);
            return createdOrg;
        });
        if (newOrgIds.length > 0) {
            parentOrgDetails.associatedOrganizations.push(...newOrgIds);
            await OrganizationRepository.saveOrUpdateInBatch([...createdAssociatedOrgs, parentOrgDetails]);
            await this.associateOrgMembersAndRootUsers(newOrgIds);
            await this.OpenSearch(newOrgIds);
        }
        return createdAssociatedOrgs;
    }

    static async OpenSearch (orgIds) {
        const orgs = await OrganizationRepository.getInBatch(orgIds);
        await Promise.all(orgs.map(async (org) =>
            await AwsOpenSearchService.create(CONSTANTS.OPEN_SEARCH.COLLECTION.ORGANIZATION, org.id, org)));
    }

    static createOrganization (orgDetails, loggedInUser) {
        const {
            category,
            orgName,
            address,
            country,
            state,
            city,
            zipCode,
            email,
            parentOrganization,
            parentOrganizationName,
            paymentInstructions,
            allowedPaymentType,
            paymentDetails,
            platformFee,
            platformFeeCoveredBy
        } = orgDetails;
        const orgId = uuidv4();
        return {
            id: orgId,
            name: orgName,
            createdBy: loggedInUser.id,
            paymentInstructions,
            allowedPaymentType,
            paymentDetails,
            category,
            address,
            country,
            state,
            city,
            zipCode,
            email,
            platformFee,
            platformFeeCoveredBy,
            parentOrganization,
            parentOrganizationName
        };
    }

    /**
     * @desc Generic function to perform batchPut in chunks
     * <AUTHOR>
     * @since 25/07/2025
     * @param {Object} options
     * @param {Object} options.model - Dynamoose model to use
     * @param {Array} options.data - Data to put
     * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT] - Max chunk size per request
     */
    static async batchPutInChunks ({ model, data, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT }) {
        if (!Array.isArray(data)) {
            return;
        }

        data = data.filter(Boolean);
        if (data.length === 0) {
            return;
        }

        for (let i = 0; i < data.length; i += chunkSize) {
            const dataChunk = data.slice(i, i + chunkSize);
            await model.batchPut(dataChunk);
        }
    }

    static async associateOrgMembersAndRootUsers (orgIds) {
        const rootUsers = await UserRepository.getRootUsers();
        const rootUserAsOrgMember = [];
        const updatedRootUsers = [];
        for (const rootUser of rootUsers) {
            for (const orgId of orgIds) {
                const org = rootUser.associatedOrganizations.find(org => org.organizationId === orgId);
                if (!org) {
                    rootUser.associatedOrganizations.push({ organizationId: orgId, role: CONSTANTS.ROLE.ORG_SUPER_ADMIN });
                }
            }
            rootUserAsOrgMember.push({
                id: rootUser.id,
                email: rootUser.email,
                firstName: rootUser.firstName,
                lastName: rootUser.lastName,
                associatedOrgRole: CONSTANTS.ROLE.ORG_SUPER_ADMIN,
                status: CONSTANTS.STATUS.ACTIVE
            });
            updatedRootUsers.push(rootUser);
        }

        const orgMemberDetails = [];
        for (const orgId of orgIds) {
            orgMemberDetails.push({
                organizationId: orgId,
                users: rootUserAsOrgMember
            });
        }
        await this.batchPutInChunks({
            model: User,
            data: updatedRootUsers
        });
        await this.batchPutInChunks({
            model: OrganizationMember,
            data: orgMemberDetails
        });
    }
}

module.exports = OrganizationBulkService;
