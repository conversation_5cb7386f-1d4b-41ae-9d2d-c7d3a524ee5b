// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const groupsSchema = new dynamoose.Schema(stryMutAct_9fa48("578") ? {} : (stryCov_9fa48("578"), {
  groupId: stryMutAct_9fa48("579") ? {} : (stryCov_9fa48("579"), {
    hashKey: stryMutAct_9fa48("580") ? false : (stryCov_9fa48("580"), true),
    type: String,
    default: stryMutAct_9fa48("581") ? () => undefined : (stryCov_9fa48("581"), () => uuidv4())
  }),
  groupType: stryMutAct_9fa48("582") ? {} : (stryCov_9fa48("582"), {
    type: String,
    required: stryMutAct_9fa48("583") ? false : (stryCov_9fa48("583"), true),
    enum: stryMutAct_9fa48("584") ? [] : (stryCov_9fa48("584"), [stryMutAct_9fa48("585") ? "" : (stryCov_9fa48("585"), 'event'), stryMutAct_9fa48("586") ? "" : (stryCov_9fa48("586"), 'organization'), stryMutAct_9fa48("587") ? "" : (stryCov_9fa48("587"), 'organization_admin')]),
    index: stryMutAct_9fa48("588") ? {} : (stryCov_9fa48("588"), {
      name: stryMutAct_9fa48("589") ? "" : (stryCov_9fa48("589"), 'groupType-index'),
      global: stryMutAct_9fa48("590") ? false : (stryCov_9fa48("590"), true),
      project: stryMutAct_9fa48("591") ? false : (stryCov_9fa48("591"), true)
    })
  }),
  organizationId: stryMutAct_9fa48("592") ? {} : (stryCov_9fa48("592"), {
    type: String,
    index: stryMutAct_9fa48("593") ? {} : (stryCov_9fa48("593"), {
      name: stryMutAct_9fa48("594") ? "" : (stryCov_9fa48("594"), 'organizationId-index'),
      global: stryMutAct_9fa48("595") ? false : (stryCov_9fa48("595"), true),
      project: stryMutAct_9fa48("596") ? false : (stryCov_9fa48("596"), true)
    })
  }),
  organizationMetaData: stryMutAct_9fa48("597") ? {} : (stryCov_9fa48("597"), {
    type: Object,
    schema: stryMutAct_9fa48("598") ? {} : (stryCov_9fa48("598"), {
      name: stryMutAct_9fa48("599") ? {} : (stryCov_9fa48("599"), {
        type: String
      }),
      logo: stryMutAct_9fa48("600") ? {} : (stryCov_9fa48("600"), {
        type: String
      })
    })
  }),
  eventId: stryMutAct_9fa48("601") ? {} : (stryCov_9fa48("601"), {
    type: String,
    index: stryMutAct_9fa48("602") ? {} : (stryCov_9fa48("602"), {
      name: stryMutAct_9fa48("603") ? "" : (stryCov_9fa48("603"), 'eventId-index'),
      global: stryMutAct_9fa48("604") ? false : (stryCov_9fa48("604"), true),
      project: stryMutAct_9fa48("605") ? false : (stryCov_9fa48("605"), true)
    })
  }),
  eventMetaData: stryMutAct_9fa48("606") ? {} : (stryCov_9fa48("606"), {
    type: Object,
    schema: stryMutAct_9fa48("607") ? {} : (stryCov_9fa48("607"), {
      title: stryMutAct_9fa48("608") ? {} : (stryCov_9fa48("608"), {
        type: String
      }),
      startDateTime: stryMutAct_9fa48("609") ? {} : (stryCov_9fa48("609"), {
        type: Date
      }),
      endDateTime: stryMutAct_9fa48("610") ? {} : (stryCov_9fa48("610"), {
        type: Date
      }),
      image: stryMutAct_9fa48("611") ? {} : (stryCov_9fa48("611"), {
        type: String
      })
    })
  }),
  status: stryMutAct_9fa48("612") ? {} : (stryCov_9fa48("612"), {
    type: String,
    enum: stryMutAct_9fa48("613") ? [] : (stryCov_9fa48("613"), [stryMutAct_9fa48("614") ? "" : (stryCov_9fa48("614"), 'active'), stryMutAct_9fa48("615") ? "" : (stryCov_9fa48("615"), 'deleted'), stryMutAct_9fa48("616") ? "" : (stryCov_9fa48("616"), 'expired')]),
    default: stryMutAct_9fa48("617") ? "" : (stryCov_9fa48("617"), 'active')
  })
}), stryMutAct_9fa48("618") ? {} : (stryCov_9fa48("618"), {
  timestamps: stryMutAct_9fa48("619") ? {} : (stryCov_9fa48("619"), {
    createdAt: stryMutAct_9fa48("620") ? "" : (stryCov_9fa48("620"), 'createdAt'),
    updatedAt: stryMutAct_9fa48("621") ? "" : (stryCov_9fa48("621"), 'updatedAt')
  })
}));
module.exports = dynamoose.model(stryMutAct_9fa48("622") ? "" : (stryCov_9fa48("622"), 'Groups'), groupsSchema);