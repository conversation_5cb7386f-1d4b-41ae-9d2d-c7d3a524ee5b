{"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "_comment": "This config was generated using 'stryker init'. Please take a look at: https://stryker-mutator.io/docs/stryker-js/configuration/ for more information.", "packageManager": "npm", "reporters": ["html", "clear-text", "progress"], "testRunner": "mocha", "testRunner_comment": "Take a look at https://stryker-mutator.io/docs/stryker-js/mocha-runner for information about the mocha plugin.", "coverageAnalysis": "perTest", "mutate": ["server/**/*.js", "!server/**/*.test.js", "!server/**/*.spec.js", "!server/**/test/**/*.js", "!index.js"], "testRunnerNodeArgs": ["--max-old-space-size=4096"], "mochaOptions": {"spec": ["test/**/*.js"]}, "timeoutMS": 60000, "timeoutFactor": 2, "maxConcurrentTestRunners": 1, "disableTypeChecks": "{src,lib}/**/*.{js,ts,jsx,tsx,html,vue,svelte}", "ignorePatterns": ["coverage/**/*", "node_modules/**/*", ".stryker-tmp/**/*"]}