const Organization = require('../../models/organization.model');
const CONSTANTS = require('../../util/constants');

class OrganizationRepository {
    /**
     * @desc This function fetch array of organizations with given name and zip code.
     * <AUTHOR>
     * @since 27/08/2024
     * @param {string} orgName orgName
     * @param {string} zipCode zipCode
     */
    static async orgExists (orgName, zipCode) {
        return await Organization
            .query('name').eq(orgName)
            .using('name-index')
            .where('zipCode').eq(zipCode)
            .where('isDeleted').eq(0)
            .exec();
    }

    /**
     * @desc This function returns Organization with given organizationId
     * <AUTHOR>
     * @since 27/08/2024
     * @param {string} organizationId organizationId
     */
    static async getOrganizationById (organizationId) {
        return await Organization.get({ id: organizationId });
    }

    /**
     * @desc This function save or update Organizations in batch
     * <AUTHOR>
     * @since 27/08/2024
     * @param {object} organizations organizations
     */
    static async saveOrUpdateInBatch (organizations) {
        organizations = organizations.filter(Boolean);
        if (organizations.length === 0) {
            return;
        }

        const chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_PUT;
        for (let i = 0; i < organizations.length; i += chunkSize) {
            const organizationsChunk = organizations.slice(i, i + chunkSize);
            await Organization.batchPut(organizationsChunk);
        }
    }

    /**
     * @desc This function fetch Organizations in batch with given organizationIds
     * <AUTHOR>
     * @since 27/08/2024
     * @param {array} organizationIds organizationIds
     */
    static async getInBatch (organizationIds, attributes) {
        organizationIds = organizationIds.filter(Boolean);
        if (organizationIds.length === 0) {
            return [];
        }

        const chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET;
        const organizations = [];

        for (let i = 0; i < organizationIds.length; i += chunkSize) {
            const organizationIdsChunk = organizationIds.slice(i, i + chunkSize);
            const organizationsChunk = await Organization.batchGet(organizationIdsChunk, attributes ? { attributes } : undefined);
            organizations.push(...organizationsChunk);
        }

        return organizations;
    }
}

module.exports = OrganizationRepository;
