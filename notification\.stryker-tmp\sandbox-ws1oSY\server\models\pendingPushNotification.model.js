// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const dynamoose = require('dynamoose');
const {
  v4: uuidv4
} = require('uuid');
const PendingPushNotification = new dynamoose.Schema(stryMutAct_9fa48("747") ? {} : (stryCov_9fa48("747"), {
  id: stryMutAct_9fa48("748") ? {} : (stryCov_9fa48("748"), {
    hashKey: stryMutAct_9fa48("749") ? false : (stryCov_9fa48("749"), true),
    type: String,
    default: stryMutAct_9fa48("750") ? () => undefined : (stryCov_9fa48("750"), () => uuidv4())
  }),
  eventId: stryMutAct_9fa48("751") ? {} : (stryCov_9fa48("751"), {
    type: String,
    index: stryMutAct_9fa48("752") ? {} : (stryCov_9fa48("752"), {
      name: stryMutAct_9fa48("753") ? "" : (stryCov_9fa48("753"), 'eventId-index'),
      global: stryMutAct_9fa48("754") ? false : (stryCov_9fa48("754"), true)
    })
  }),
  pushNotificationTime: stryMutAct_9fa48("755") ? {} : (stryCov_9fa48("755"), {
    type: Date,
    required: stryMutAct_9fa48("756") ? false : (stryCov_9fa48("756"), true)
  }),
  associatedChildId: stryMutAct_9fa48("757") ? {} : (stryCov_9fa48("757"), {
    type: String,
    index: stryMutAct_9fa48("758") ? {} : (stryCov_9fa48("758"), {
      name: stryMutAct_9fa48("759") ? "" : (stryCov_9fa48("759"), 'associatedChildId-index'),
      global: stryMutAct_9fa48("760") ? false : (stryCov_9fa48("760"), true)
    })
  }),
  notificationAction: stryMutAct_9fa48("761") ? {} : (stryCov_9fa48("761"), {
    type: String
  }),
  payload: stryMutAct_9fa48("762") ? {} : (stryCov_9fa48("762"), {
    type: Object
  })
}), stryMutAct_9fa48("763") ? {} : (stryCov_9fa48("763"), {
  saveUnknown: stryMutAct_9fa48("764") ? false : (stryCov_9fa48("764"), true)
}));
module.exports = dynamoose.model(stryMutAct_9fa48("765") ? "" : (stryCov_9fa48("765"), 'PendingPushNotification'), PendingPushNotification);