/**
 * routes and schema for notification routes
*/
// @ts-nocheck


/**
 * @openapi
 * components:
 *  schemas:
 *      successNotificationsList:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *                  description: response object that contains notifications list, next index and next created date time
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                  notifications: [{
 *                    id: notification id,
 *                    title: notification title,
 *                    description: notification description,
 *                    userId: user id,
 *                    associatedChild: associated child details,
 *                    notificationAction: notification action,
 *                    payload: payload,
 *                    readStatus: is read or not,
 *                    createdAt: created date time,
 *                    updatedAt: updated date time
 *                  }],
 *                  nextIndex: next cursor pointer for pagination,
 *                  nextCreatedAt: next child id for pagination
 *              }
 *              message: Success
 *
 *      successCreateNotification:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: object
 *                  description: response object that contains created notification details
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              data: {
 *                  id: notification id,
 *                  title: notification title,
 *                  description: notification description,
 *                  userId: user id,
 *                  associatedChild: associated child details,
 *                  notificationAction: notification action,
 *                  payload: payload,
 *                  readStatus: is read or not,
 *                  createdAt: created date time,
 *                  updatedAt: updated date time
 *              }
 *              message: Success
 *
 *      sendNotificationRequest:
 *          type: object
 *          properties:
 *              title:
 *                  type: string
 *                  description: Title of notification (Optional)
 *              description:
 *                  type: string
 *                  required: true
 *                  description: Description of notification
 *              children:
 *                  type: array
 *                  description: List of children whom you want to send notification
 *              allParticipants:
 *                  type: boolean
 *                  description: If it is true than it will send notification to all registered children of that event
 *              allOrgChildren:
 *                  type: boolean
 *                  description: If it is true than it will send notification to all children associated to that organization
 *              eventId:
 *                  type: string
 *                  description: Event ID
 *              organizationId:
 *                  type: string
 *                  required: true
 *                  description: Organization ID
 *          example:
 *              title: notification title
 *              description: notification description
 *              children: []
 *              allParticipants: true
 *              allOrgChildren: false
 *              eventId: event-id
 *              organizationId: organization-id
 *
 *      successSendNotification:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 */

/**
 * @openapi
 * /notification/list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Notification]
 *      summary: get notification list
 *      parameters:
 *          - in: query
 *            name: nextIndex
 *            schema:
 *                type: string
 *            description: last notification id for pagination
 *          - in: query
 *            name: nextCreatedAt
 *            schema:
 *                type: string
 *            description: last notification created date time for pagination
 *          - in: query
 *            name: pageSize
 *            schema:
 *                type: string
 *            description: page size for the pagination
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successNotificationsList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /notification:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Notification]
 *      summary: create notification
 *      requestBody:
 *          content:
 *              application/json:
 *                  schema:
 *                      type: object
 *                      properties:
 *                          title:
 *                              type: string
 *                          description:
 *                              type: string
 *                          associatedChildId:
 *                              type: string
 *                          notificationAction:
 *                              type: string
 *                          payload:
 *                              type: object
 *                      example:
 *                          title: notification title
 *                          description: notification description
 *                          associatedChildId: associated child id
 *                          notificationAction: notification action
 *                          payload: { key: value }
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successCreateNotification'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /notification:
 *  patch:
 *      security:
 *        - bearerAuth: []
 *      tags: [Notification]
 *      summary: update notification read status
 *      parameters:
 *          - in: query
 *            name: notificationId
 *            schema:
 *                type: string
 *            description: notification id
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successCreateNotification'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /notification/send:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Notification]
 *      summary: send notification to a child or group of children
 *      requestBody:
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/sendNotificationRequest'
 *
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successSendNotification'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /notification/notify:
 *  post:
 *      tags: [Notification]
 *      summary: Send notification to a specific child
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      type: object
 *                      properties:
 *                          topic:
 *                              type: string
 *                              description: The child ID
 *                          data:
 *                              type: object
 *                              properties:
 *                                  child:
 *                                      type: string
 *                                      description: The child details as a stringified JSON object
 *                                  route:
 *                                      type: string
 *                                      description: The route for the notification
 *                                  activeTab:
 *                                      type: string
 *                                      description: The active tab when the notification is received
 *                          notification:
 *                              type: object
 *                              properties:
 *                                  title:
 *                                      type: string
 *                                      description: The title of the notification
 *                                  body:
 *                                      type: string
 *                                      description: The body content of the notification
 *                          android:
 *                              type: object
 *                              properties:
 *                                  notification:
 *                                      type: object
 *                                      description: Android specific notification options
 *                          apns:
 *                              type: object
 *                              properties:
 *                                  payload:
 *                                      type: object
 *                                      properties:
 *                                          aps:
 *                                              type: object
 *                                              description: APNS specific notification options
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successSendNotification'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */