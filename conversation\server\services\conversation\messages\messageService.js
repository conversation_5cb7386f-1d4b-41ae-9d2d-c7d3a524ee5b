/**
 * This file contains controller for Messages.
 * Created by Incubyte on 24/10/2024.
 * @name messageController
 */

const sendMessage = require('../../webSocketHandlers/handleSendGroupMessage');
const sendPersonalMessage = require('../../webSocketHandlers/handleSendPersonalMessage');
const UploadService = require('../../../util/uploadService');
const MessageValidator = require('./messageValidator');
const { v4: uuidv4 } = require('uuid');
const Message = require('../../../models/message.model');
const FlaggedMessage = require('../../../models/flaggedMessage.model');
const Groups = require('../../../models/groups.model');
const User = require('../../../models/user.model');
const GeneralError = require('../../../util/GeneralError');
const handleSendGroupMessage = require('../../webSocketHandlers/handleSendGroupMessage');
const GroupMembers = require('../../../models/groupMembers.model');
const SendMessageService = require('../../sendSocketMessageService');
const handleGetGroupMessage = require('../../webSocketHandlers/handleGetGroupMessage');
const CONSTANTS = require('../../../util/constants');

class MessageService {
    /**
     * @desc This function is being used to send message with media
     * <AUTHOR>
     * @since 24/10/2024
     * @param {Object} req Request
     */
    static async sendMessageWithMedia (req) {
        const validator = new MessageValidator(req);
        req.body.replyMessage = req.body.replyMessage ? JSON.parse(req.body.replyMessage) : undefined;
        validator.validateSendMessageWithMedia();
        const {
            messageId,
            organizationId,
            groupId,
            senderId,
            message,
            actionType,
            replyMessage,
            mediaFileName,
            mediaThumbnailFileName,
            mediaDisplayName: fileDisplayName,
            mediaType: fileType
        } = req.body;

        let mediaName = mediaFileName;
        let mediaType = fileType;
        let mediaDisplayName = fileDisplayName;
        let mediaThumbnailName = mediaThumbnailFileName;
        if (req.files && req.files.length > 0) {
            for (const file of req.files) {
                if (file.fieldname === 'media') {
                    mediaName = `${process.env.NODE_ENV}-message-media/${organizationId}/${groupId}/${messageId}`;
                    mediaDisplayName = file.originalname;
                    const isImage = file.mimetype.startsWith('image/');
                    await UploadService.uploadFile(file, mediaName, isImage);
                    mediaType = file.mimetype;
                } else if (file.fieldname === 'mediaThumbnail') {
                    mediaThumbnailName = `${process.env.NODE_ENV}-message-media-thumbnail/${organizationId}/${groupId}/${messageId}`;
                    await UploadService.uploadFile(file, mediaThumbnailName, true);
                } else {
                    // Do nothing
                }
            }
        }

        const eventBody = {
            messageId, actionType, groupId, senderId, message, mediaName,
            mediaType, replyMessage, mediaDisplayName, mediaThumbnailName
        };
        const res = await sendMessage({ body: eventBody });
        return res.data;
    }

    /**
     * @desc This function is being used to send personal message with media
     * <AUTHOR>
     * @since 20/12/2024
     * @param {Object} req Request
     */
    static async sendPersonalMessageWithMedia (req) {
        const validator = new MessageValidator(req);
        req.body.replyMessage = req.body.replyMessage ? JSON.parse(req.body.replyMessage) : undefined;

        validator.validateSendPersonalMessageWithMedia();
        const {
            messageId,
            senderId,
            actionType,
            message,
            replyMessage,
            conversationId,
            mediaFileName,
            mediaThumbnailFileName,
            receiverId,
            mediaDisplayName: fileDisplayName,
            mediaType: fileType
        } = req.body;

        const checkedConversationId = conversationId ?? uuidv4();

        let mediaName = mediaFileName;
        let mediaType = fileType;
        let mediaDisplayName = fileDisplayName;
        let mediaThumbnailName = mediaThumbnailFileName;
        if (req.files && req.files.length > 0) {
            for (const file of req.files) {
                if (file.fieldname === 'media') {
                    mediaName = `${process.env.NODE_ENV}-personal-message-media/${checkedConversationId}/${messageId}`;
                    mediaDisplayName = file.originalname;
                    const isImage = file.mimetype.startsWith('image/');
                    await UploadService.uploadFile(file, mediaName, isImage);
                    mediaType = file.mimetype;
                } else if (file.fieldname === 'mediaThumbnail') {
                    mediaThumbnailName = `${process.env.NODE_ENV}-personal-message-media-thumbnail/${checkedConversationId}/${messageId}`;
                    await UploadService.uploadFile(file, mediaThumbnailName, true);
                } else {
                    // Do nothing
                }
            }
        }

        const eventBody = {
            messageId, actionType, conversationId: checkedConversationId, senderId, message, mediaName,
            mediaType, replyMessage, mediaDisplayName, mediaThumbnailName, receiverId, isNewConversation: !conversationId
        };
        const res = await sendPersonalMessage({ body: eventBody });
        return res.data;
    }

    /**
     * @desc This function is being used to flag message
     * <AUTHOR>
     * @since 24/12/2024
     * @param {Object} req Request
     * @param {Object} user User
     */
    static async flagMessage (req, user) {
        const validator = new MessageValidator(req);
        validator.validateFlagMessage();
        const { messageId, reasons } = req.body;

        const message = await Message.get(messageId);
        if (!message) {
            throw new GeneralError('Message not found', 404);
        }

        const group = await Groups.get(message.groupId);
        if (!group) {
            throw new GeneralError('Group not found', 404);
        }
        await Message.update({ id: messageId }, { $ADD: { flagCount: 1 } });

        const orgId = group.organizationId;
        const flaggedMessage = new FlaggedMessage({
            messageId,
            conversationId: message.groupId,
            orgId,
            flaggedBy: user.id,
            reasons
        });
        await flaggedMessage.save();
    }

    /**
     * @desc This function is being used to get flag message list
     * <AUTHOR>
     * @since 26/12/2024
     * @param {Object} req Request
     */
    static async getFlagMessageList (req) {
        const validator = new MessageValidator(req);
        validator.validateGetFlagMessageList();
        const { orgId } = req.query;

        const flaggedMessages = await FlaggedMessage.query('orgId')
            .eq(orgId)
            .where('status')
            .eq(CONSTANTS.FLAG_MESSAGE_STATUS.PENDING)
            .using('orgId-status-index')
            .exec();
        const reportCountMap = flaggedMessages.reduce((acc, message) => {
            acc[message.messageId] = (acc[message.messageId] || 0) + 1;
            return acc;
        }, {});
        const flaggedMessagesSet = new Set(flaggedMessages.map(message => message.messageId));
        const messageIds = Array.from(flaggedMessagesSet);
        const messageChunks = await this.batchGetInChunks({
            ids: messageIds,
            model: Message
        });

        const { userMap, groupMap } = await this.getUserAndGroup(messageChunks);

        const messagesWithNames = messageChunks.map(async (message) => {
            if (message.mediaName) {
                message.mediaUrl = await UploadService.getSignedUrlForAttachments(message.mediaName);
                delete message.mediaName;
            }

            if (message.mediaThumbnailName) {
                message.mediaThumbnailUrl = await UploadService.getSignedUrlForAttachments(message.mediaThumbnailName);
                delete message.mediaThumbnailName;
            }
            if (message.replyMessage) {
                message.replyMessage.senderName = userMap[message.replyMessage.senderId];
                if (message.replyMessage?.mediaName) {
                    message.replyMessage.mediaUrl = await UploadService.getSignedUrlForAttachments(message.replyMessage.mediaName);
                    delete message.replyMessage.mediaName;
                }
            }

            const groupDetails = groupMap[message.groupId];
            return {
                messageId: message.id,
                message: message.message,
                senderId: message.senderId,
                senderName: userMap[message.senderId],
                groupName: groupDetails.groupName,
                groupType: groupDetails.groupType,
                groupId: message.groupId,
                reportCount: reportCountMap[message.id],
                isEdited: message.isEdited,
                isDeleted: message.isDeleted,
                createdAt: message.createdAt,
                mediaUrl: message.mediaUrl,
                mediaThumbnailUrl: message.mediaThumbnailUrl,
                mediaType: message.mediaType,
                mediaDisplayName: message.mediaDisplayName,
                replyMessage: message.replyMessage
            };
        });

        const messagesWithNamesAndMedia = await Promise.all(messagesWithNames);

        return messagesWithNamesAndMedia.sort((a, b) => b.reportCount - a.reportCount);
    }

    /**
     * @desc Generic function to perform batchGet in chunks
     * <AUTHOR>
     * @since 25/07/2025
     * @param {Object} options
     * @param {Array} options.ids - List of IDs to fetch
     * @param {Object} options.model - Dynamoose model to use
     * @param {Array} [options.attributes] - Attributes to fetch (optional)
     * @param {Number} [options.chunkSize=CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET] - Max chunk size per request
     * @returns {Promise<Array>} Combined result of all batchGets
    */
    static async batchGetInChunks ({ ids, model, attributes, chunkSize = CONSTANTS.DB_BATCH_OPERATION_CHUNK_SIZE.BATCH_GET }) {
        if (!Array.isArray(ids)) {
            return [];
        }
        ids = ids.filter(Boolean);
        if (ids.length === 0) {
            return [];
        }

        const result = [];

        for (let i = 0; i < ids.length; i += chunkSize) {
            const chunk = ids.slice(i, i + chunkSize);
            const dataChunk = await model.batchGet(chunk, attributes ? { attributes } : undefined);
            result.push(...dataChunk);
        }

        return result;
    }

    /**
     * @desc This function is being used to get users and groups
     * <AUTHOR>
     * @since 27/12/2024
     * @param {Array} messages Message Ids
     */
    static async getUserAndGroup (messages) {
        const userMap = {};
        const senderIds = new Set(messages.map(message => {
            if (message.replyMessage?.senderId) {
                return [message.senderId, message.replyMessage.senderId];
            }
            return message.senderId;
        }).flat());
        const groupIds = new Set(messages.map(message => message.groupId));
        for (const senderId of senderIds) {
            const senderData = (await User.query('id').eq(senderId).exec())[0];
            userMap[senderId] = `${senderData?.firstName ?? ''} ${senderData?.lastName ?? ''}`;
        }

        const groupIdsArray = Array.from(groupIds);
        const groupMap = await this.getGroupMap(groupIdsArray);

        return { userMap, groupMap };
    }


    /**
     * @desc This function is being used to get group map
     * <AUTHOR>
     * @since 02/01/2025
     * @param {Array} groups Groups
     */
    static async getGroupMap (groupIdsArray) {
        const groups = await this.batchGetInChunks({
            ids: groupIdsArray,
            model: Groups
        });
        if (groups.length === 0) {
            return {};
        }

        const groupMap = {};

        groups.forEach(group => {
            const groupName = group.groupType === CONSTANTS.GROUP_TYPES.EVENT
                ? group.eventMetaData.title
                : group.organizationMetaData.name;
            const groupType = group.groupType;
            groupMap[group.groupId] = { groupName, groupType };
        });

        return groupMap;
    }

    /**
     * @desc This function is being used to update flag message status
     * <AUTHOR>
     * @since 30/12/2024
     * @param {Object} req Request
     */
    static async updateFlagMessageStatus (req) {
        const { messageId, status, senderId } = req.body;
        const flaggedMessageValidator = new MessageValidator(req);
        flaggedMessageValidator.validateUpdateFlagMessageStatus();

        const flaggedMessages = await FlaggedMessage.query('messageId')
            .eq(messageId)
            .where('status')
            .eq(CONSTANTS.FLAG_MESSAGE_STATUS.PENDING)
            .using('messageId-index')
            .exec();

        if (flaggedMessages.length === 0) {
            throw new GeneralError('Flagged messages not found', 404);
        }

        switch (status) {
            case CONSTANTS.FLAG_MESSAGE_STATUS.MESSAGE_DELETED: {
                await Message.update({ id: messageId }, { $SET: { isFlagged: true } });
                const deleteResult = await this.handleDeleteGroupMessage(flaggedMessages[0]);
                if (deleteResult.statusCode !== 200) {
                    throw new GeneralError('Failed to delete group message', 500);
                }
                break;
            }

            case CONSTANTS.FLAG_MESSAGE_STATUS.USER_DISABLED_COMMENTING:
                await Message.update({ id: messageId }, { $SET: { isFlagged: true } });
                await this.handleDisableCommentingGroupMessage(flaggedMessages[0], senderId);
                break;

            case CONSTANTS.FLAG_MESSAGE_STATUS.DISMISSED:
                await Message.update({ id: messageId }, { $SET: { flagCount: 0 } });
                break;
        }

        await Promise.all(
            flaggedMessages.map(message =>
                FlaggedMessage.update({ id: message.id }, { status })
            )
        );

        return { message: 'Flagged messages updated successfully' };
    }

    /**
     * @desc This function is being used to handle delete group message
     * <AUTHOR>
     * @since 30/12/2024
     * @param {Object} flaggedMessage Flagged message
     */
    static async handleDeleteGroupMessage (flaggedMessage) {
        const res = await handleSendGroupMessage({
            body: {
                actionType: 'DELETE_GROUP_MESSAGE',
                groupId: flaggedMessage.conversationId,
                messageId: flaggedMessage.messageId
            }
        });
        return res;
    }

    /**
     * @desc This function is being used to handle disable send message to group
     * <AUTHOR>
     * @since 30/12/2024
     * @param {Object} flaggedMessage Flagged message
     * @param {String} senderId Sender Id
     */
    static async handleDisableCommentingGroupMessage (flaggedMessage, senderId) {
        const groupMemberId = await GroupMembers.query('groupId')
            .eq(flaggedMessage.conversationId)
            .using('groupId-index')
            .where('userId')
            .eq(senderId)
            .using('userId-index')
            .exec();
        if (groupMemberId.length === 0) {
            throw new GeneralError('Group member not found', 404);
        }
        const allGroupMemberIds = await this.getGroupMemberIds(flaggedMessage.conversationId);
        const messageData = {
            action: 'sendMessage',
            actionType: 'USER_DISABLED_FROM_GROUP',
            message: 'User\'s commenting has been disabled',
            data: {
                userId: senderId,
                groupId: flaggedMessage.conversationId
            }
        };
        await SendMessageService.sendMessagesToUsers(allGroupMemberIds, messageData);
        const res = await this.handleDeleteGroupMessage(flaggedMessage);
        if (res.statusCode !== 200) {
            throw new GeneralError('Failed to delete group message', 500);
        }
        const groupMember = groupMemberId[0];
        await GroupMembers.update({ id: groupMember.id }, { status: CONSTANTS.GROUP_MEMBER_STATUS.DISABLED });
        return true;
    }

    /**
     * @desc This function is being used to get group member ids
     * <AUTHOR>
     * @since 31/12/2024
     * @param {String} groupId Group Id
     */
    static async getGroupMemberIds (groupId) {
        return (
            await GroupMembers.query('groupId')
                .eq(groupId)
                .using('groupId-index')
                .attributes(['userId', 'status'])
                .where('status')
                .in([CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE, CONSTANTS.GROUP_MEMBER_STATUS.DISABLED])
                .exec()
        ).map((member) => member.userId);
    }

    /**
     * @desc This function is being used to get flag message reasons
     * <AUTHOR>
     * @since 02/01/2025
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged In User
     */
    static async getFlagMessageReasons (req, loggedInUser) {
        const validator = new MessageValidator(req);
        validator.validateGetFlagMessageReasons();

        const { messageId, organizationId } = req.query;
        const { flaggedMessage, groupDetails } = await this.getFlaggedMessage(messageId, organizationId, loggedInUser);

        const flaggedMessages = await this.getFlaggedMessagesForMessageId(messageId);

        const uniqueReporterIds = new Set(flaggedMessages.map(message => message.flaggedBy).filter(Boolean));

        const reporters = await this.getReportersMap(uniqueReporterIds);

        return this.formatResponseWithDetails(flaggedMessages, reporters, flaggedMessage, groupDetails);
    }

    /**
     * @desc This function is being used to get flagged message
     * <AUTHOR>
     * @since 02/01/2025
     * @param {String} messageId Message Id
     * @param {String} organizationId Organization Id
     * @param {Object} loggedInUser Logged In User
     */
    static async getFlaggedMessage (messageId, organizationId, loggedInUser) {
        if (!loggedInUser.associatedOrganizations?.some(org => org.organizationId === organizationId)) {
            throw new GeneralError('User is not associated with the organization', 403);
        }

        const message = await Message.get(messageId);
        if (!message) {
            throw new GeneralError('Message not found', 404);
        }

        const groupId = message.groupId;

        const group = await Groups.get(groupId, {
            attributes: ['groupId', 'groupType', 'organizationId', 'organizationMetaData', 'eventMetaData']
        });
        if (!group) {
            throw new GeneralError('Group not found', 404);
        }

        if (group.organizationId !== organizationId) {
            throw new GeneralError('Message not found', 404);
        }

        return {
            flaggedMessage: message,
            groupDetails: group
        };
    }

    /**
     * @desc This function is being used to get flagged messages for message id
     * <AUTHOR>
     * @since 02/01/2025
     * @param {String} messageId Message Id
     */
    static async getFlaggedMessagesForMessageId (messageId) {
        return await FlaggedMessage.query('messageId').eq(messageId).using('messageId-index').exec();
    }

    /**
     * @desc This function is being used to get reporters map
     * <AUTHOR>
     * @since 02/01/2025
     * @param {Set} uniqueReporterIds Unique Reporter Ids
     */
    static async getReportersMap (uniqueReporterIds) {
        const reporters = {};
        for (const reporterId of uniqueReporterIds) {
            const reporter = await User.query('id')
                .eq(reporterId)
                .attributes(['id', 'firstName', 'lastName'])
                .exec();
            if (reporter.length > 0) {
                reporters[reporterId] = {
                    id: reporter[0].id,
                    firstName: reporter[0].firstName,
                    lastName: reporter[0].lastName
                };
            }
        }
        return reporters;
    }

    /**
     * @desc This function is being used to format response with details
     * <AUTHOR>
     * @since 02/01/2025
     * @param {Array} flaggedMessages Flagged Messages
     * @param {Object} reporters Reporters
     */
    static async formatResponseWithDetails (flaggedMessages, reporters, flaggedMessage, groupDetails) {
        const flaggedMessageReasons = flaggedMessages.map(message => ({
            id: message.id,
            reasons: message.reasons,
            status: message.status,
            reporter: reporters[message.flaggedBy]
        }));

        const sender = await User.query('id').eq(flaggedMessage.senderId).exec();
        if (flaggedMessage.replyMessage) {
            const replier = await User.query('id').eq(flaggedMessage.replyMessage.senderId).exec();
            flaggedMessage.replyMessage.senderName = replier?.[0]?.firstName + ' ' + replier?.[0]?.lastName;
        }

        if (flaggedMessage.mediaName) {
            flaggedMessage.mediaUrl = await UploadService.getSignedUrl(flaggedMessage.mediaName);
            delete flaggedMessage.mediaName;
        }
        if (flaggedMessage.mediaThumbnailName) {
            flaggedMessage.mediaThumbnailUrl = await UploadService.getSignedUrl(flaggedMessage.mediaThumbnailName);
            delete flaggedMessage.mediaThumbnailName;
        }

        if (flaggedMessage.replyMessage?.mediaName) {
            flaggedMessage.replyMessage.mediaUrl = await UploadService.getSignedUrl(flaggedMessage.replyMessage.mediaName);
            delete flaggedMessage.replyMessage.mediaName;
        }

        const flaggedMessageDetails = {
            id: flaggedMessage.id,
            groupId: flaggedMessage.groupId,
            mediaDisplayName: flaggedMessage.mediaDisplayName,
            mediaUrl: flaggedMessage.mediaUrl,
            mediaType: flaggedMessage.mediaType,
            mediaThumbnailUrl: flaggedMessage.mediaThumbnailUrl,
            message: flaggedMessage.message,
            replyMessage: flaggedMessage.replyMessage,
            senderId: {
                id: flaggedMessage.senderId,
                firstName: sender?.[0]?.firstName,
                lastName: sender?.[0]?.lastName
            },
            organizationId: groupDetails.organizationId,
            groupName: groupDetails.groupType === CONSTANTS.GROUP_TYPES.EVENT
                ? groupDetails.eventMetaData?.title
                : groupDetails.organizationMetaData?.name
        };

        return {
            flaggedMessageDetails,
            flaggedMessageReasons
        };
    }

    /**
     * @desc This function is being used to get user children list
     * <AUTHOR>
     * @since 02/01/2025
     * @param {Object} req Request
     */
    static async getUserChildrenList (req) {
        const { userId, groupId } = req.query;
        const validator = new MessageValidator(req);
        validator.validateGetUserChildrenList();
        const groupMemberId = await this.getGroupMemberId(userId, groupId);
        const userDetailsWithChildrenList = await this.getChildrenList(groupMemberId);
        return userDetailsWithChildrenList;
    }

    /**
     * @desc This function is being used to get children list
     * <AUTHOR>
     * @since 02/01/2025
     * @param {String} groupMemberId Group Member Id
     */
    static async getChildrenList (groupMemberId) {
        const childrenList = await handleGetGroupMessage({
            body: {
                actionType: 'GET_USER_CHILDREN_LIST',
                groupMemberId: groupMemberId[0].id
            }
        });
        return JSON.parse(childrenList.data);
    }

    static async getGroupMemberId (userId, groupId) {
        return await GroupMembers.query('userId')
            .eq(userId)
            .using('userId-index')
            .where('groupId')
            .eq(groupId)
            .using('groupId-index')
            .attributes(['id'])
            .exec();
    }

    static async getDisabledGroupMembers (groupIds) {
        const groupMemberPromises = groupIds.map(groupId =>
            GroupMembers.query('groupId')
                .eq(groupId)
                .using('groupId-index')
                .where('status')
                .eq(CONSTANTS.GROUP_MEMBER_STATUS.DISABLED)
                .exec()
        );
        const groupMembers = await Promise.all(groupMemberPromises);
        return groupMembers.flat();
    }

    /**
     * @desc This function is being used to get disabled commenter list
     * <AUTHOR>
     * @since 02/01/2025
     * @param {Object} req Request
     * @param {Object} loggedInUser Logged In User
     */
    static async getDisabledCommenterList (req, loggedInUser) {
        const validator = new MessageValidator(req);
        validator.validateGetDisabledCommenterList();

        const { organizationId } = req.query;

        const disabledCommentersMessages = await this.getDisabledCommentersMessages(organizationId, loggedInUser);
        const disabledCommentersGroupIds = [ ...new Set(disabledCommentersMessages.map(commenter => commenter.conversationId)) ];
        const [disabledGroupMembers, groups] = await Promise.all([
            this.getDisabledGroupMembers(disabledCommentersGroupIds),
            this.batchGetInChunks({
                ids: disabledCommentersGroupIds,
                model: Groups
            })
        ]);
        const disabledUserIds = disabledGroupMembers.map(groupMember => groupMember.userId);
        const usersDetails = (await Promise.all(disabledUserIds.map(async (userId) =>
            await User.query('id').eq(userId).attributes(['id', 'firstName', 'lastName']).exec()))).flat();
        const disabledCommenters = groups.map((group) => {
            const groupMembers = disabledGroupMembers.filter(groupMember => groupMember.groupId === group.groupId) ?? [];
            const users = groupMembers.map(groupMember => {
                const user = usersDetails.find(user => user.id === groupMember.userId);
                return {
                    id: user.id,
                    firstName: user.firstName,
                    lastName: user.lastName
                };
            });
            return {
                groupName: group.groupType === CONSTANTS.GROUP_TYPES.EVENT ? group.eventMetaData.title : group.organizationMetaData.name,
                groupId: group.groupId,
                groupType: group.groupType,
                users
            };
        });
        return disabledCommenters;
    }

    /**
     * @desc This function is being used to get disabled commenters
     * <AUTHOR>
     * @since 02/01/2025
     * @param {String} organizationId Organization Id
     * @param {Object} loggedInUser Logged In User
     */
    static async getDisabledCommentersMessages (organizationId, loggedInUser) {
        if (!loggedInUser.associatedOrganizations?.some(org => org.organizationId === organizationId)) {
            throw new GeneralError('User is not associated with the organization', 403);
        }

        return await FlaggedMessage.query('orgId')
            .eq(organizationId)
            .where('status')
            .eq(CONSTANTS.FLAG_MESSAGE_STATUS.USER_DISABLED_COMMENTING)
            .exec();

    }

    /**
     * @desc This function is being used to enable disabled commenter
     * <AUTHOR> Girbide
     * @since 07/01/2025
     * @param {Object} req Request
     */
    static async enableCommenter (req, locale) {
        const { userId, groupId } = req.body;
        const validator = new MessageValidator(req, locale);
        validator.validateEnableCommenter();
        const userGroupMembers = await GroupMembers.query('groupId')
            .eq(groupId)
            .using('groupId-index')
            .where('userId')
            .eq(userId)
            .using('userId-index')
            .exec();
        if (userGroupMembers.length === 0) {
            throw new GeneralError('Group member not found', 404);
        }
        const userGroupMember = userGroupMembers[0];
        await GroupMembers.update({ id: userGroupMember.id }, { status: CONSTANTS.GROUP_MEMBER_STATUS.ACTIVE });
        const allGroupMemberIds = await this.getGroupMemberIds(groupId);
        const messageData = {
            action: 'sendMessage',
            actionType: 'USER_ENABLED_FROM_GROUP',
            message: 'User\'s commenting has been enabled',
            data: { userId, groupId, id: userGroupMember.id }
        };
        await SendMessageService.sendMessagesToUsers(allGroupMemberIds, messageData);
    }
}

module.exports = MessageService;
