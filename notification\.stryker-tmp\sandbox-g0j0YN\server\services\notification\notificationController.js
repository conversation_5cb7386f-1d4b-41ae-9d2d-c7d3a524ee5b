function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const notificationService = require('./notificationService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for notification routes.
 */
class NotificationController {
  /**
   * @desc This function is being used to get notification list
   * <AUTHOR>
   * @since 31/01/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
  static async getNotificationList(req, res) {
    if (stryMutAct_9fa48("1031")) {
      {}
    } else {
      stryCov_9fa48("1031");
      try {
        if (stryMutAct_9fa48("1032")) {
          {}
        } else {
          stryCov_9fa48("1032");
          const data = await notificationService.getNotificationList(req, res.locals.user);
          Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        }
      } catch (error) {
        if (stryMutAct_9fa48("1033")) {
          {}
        } else {
          stryCov_9fa48("1033");
          CONSOLE_LOGGER.error(stryMutAct_9fa48("1034") ? "" : (stryCov_9fa48("1034"), 'Error in getNotificationList'), error);
          Utils.sendResponse(error, null, res, error.message);
        }
      }
    }
  }

  /**
   * @desc This function is being used to create notification
   * <AUTHOR>
   * @since 31/01/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
  static async createNotification(req, res) {
    if (stryMutAct_9fa48("1035")) {
      {}
    } else {
      stryCov_9fa48("1035");
      try {
        if (stryMutAct_9fa48("1036")) {
          {}
        } else {
          stryCov_9fa48("1036");
          const data = await notificationService.createNotification(req, res.locals.user, res.__);
          Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        }
      } catch (error) {
        if (stryMutAct_9fa48("1037")) {
          {}
        } else {
          stryCov_9fa48("1037");
          CONSOLE_LOGGER.error(stryMutAct_9fa48("1038") ? "" : (stryCov_9fa48("1038"), 'Error in createNotification'), error);
          Utils.sendResponse(error, null, res, error.message);
        }
      }
    }
  }

  /**
   * @desc This function is being used to update notification read status
   * <AUTHOR>
   * @since 31/01/2024
   * @param {Object} req Request
   * @param {function} res Response
   */
  static async updateNotification(req, res) {
    if (stryMutAct_9fa48("1039")) {
      {}
    } else {
      stryCov_9fa48("1039");
      try {
        if (stryMutAct_9fa48("1040")) {
          {}
        } else {
          stryCov_9fa48("1040");
          const data = await notificationService.updateNotification(req, res.locals.user, res.__);
          Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        }
      } catch (error) {
        if (stryMutAct_9fa48("1041")) {
          {}
        } else {
          stryCov_9fa48("1041");
          CONSOLE_LOGGER.error(stryMutAct_9fa48("1042") ? "" : (stryCov_9fa48("1042"), 'Error in updateNotification'), error);
          Utils.sendResponse(error, null, res, error.message);
        }
      }
    }
  }

  /**
   * @desc This function is being used to send notification to user or group of users
   * <AUTHOR>
   * @since 20/02/2024
   * @param {Object} req Request
   * @param {function} res Response
  */
  static async sendNotification(req, res) {
    if (stryMutAct_9fa48("1043")) {
      {}
    } else {
      stryCov_9fa48("1043");
      try {
        if (stryMutAct_9fa48("1044")) {
          {}
        } else {
          stryCov_9fa48("1044");
          const data = await notificationService.sendNotification(req, res.locals.user, res.__);
          Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        }
      } catch (error) {
        if (stryMutAct_9fa48("1045")) {
          {}
        } else {
          stryCov_9fa48("1045");
          CONSOLE_LOGGER.error(stryMutAct_9fa48("1046") ? "" : (stryCov_9fa48("1046"), 'Error in sendNotification'), error);
          Utils.sendResponse(error, null, res, error.message);
        }
      }
    }
  }

  /**
   * @desc This function is being used to send notification to user or group of users
   * @param {Object} req Request
   * @param {function} res Response
  */
  static async notify(req, res) {
    if (stryMutAct_9fa48("1047")) {
      {}
    } else {
      stryCov_9fa48("1047");
      try {
        if (stryMutAct_9fa48("1048")) {
          {}
        } else {
          stryCov_9fa48("1048");
          const data = await notificationService.notify(req);
          Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        }
      } catch (error) {
        if (stryMutAct_9fa48("1049")) {
          {}
        } else {
          stryCov_9fa48("1049");
          CONSOLE_LOGGER.error(stryMutAct_9fa48("1050") ? "" : (stryCov_9fa48("1050"), 'Error in notify'), error);
          Utils.sendResponse(error, null, res, error.message);
        }
      }
    }
  }

  /**
   * @desc This function is being used to handle contact us form submissions data
   * @param {Object} req Request
   * @param {function} res Response
  */
  static async contactUs(req, res) {
    if (stryMutAct_9fa48("1051")) {
      {}
    } else {
      stryCov_9fa48("1051");
      try {
        if (stryMutAct_9fa48("1052")) {
          {}
        } else {
          stryCov_9fa48("1052");
          const data = await notificationService.contactUs(req);
          Utils.sendResponse(null, data, res, MESSAGES.SUCCESS);
        }
      } catch (error) {
        if (stryMutAct_9fa48("1053")) {
          {}
        } else {
          stryCov_9fa48("1053");
          CONSOLE_LOGGER.error(stryMutAct_9fa48("1054") ? "" : (stryCov_9fa48("1054"), 'Error in contactUs'), error);
          Utils.sendResponse(error, null, res, error.message);
        }
      }
    }
  }
}
module.exports = NotificationController;