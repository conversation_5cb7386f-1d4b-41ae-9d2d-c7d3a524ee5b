/* eslint-disable max-len */
module.exports = {
    // For AES, this is always 16
    IV_LENGTH: 16,
    LOG_LEVEL: 'debug',
    PROFILE_PICTURE: {
        MIN_SIZE: 5120,
        MAX_SIZE: 5242880,
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png']
    },
    USER_DOCUMENT_FILE: {
        MIN_SIZE: 10240,
        MAX_SIZE: 5242880,
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf']
    },
    REGEX: {
        EMAIL: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
        NAME: /^[a-zA-Z0-9,'~._^ -]{1,100}$/,
        ALPHA_ONLY: /^[a-zA-Z']*$/,
        ALPHA_SPECIAL_CHAR: /^[ A-Za-z0-9_@./#&+-]*$/,
        ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: /^[ A-Za-z_@./#&+-]*$/,
        FULL_ACCESS: /^[^<> ?//\\]+$/,
        ALPHA_NUMARIC: /^[\w@ ]+$/,
        templateName: /^[ A-Za-z0-9_@./#&+-]*$/,
        SUBJECT: /^[ A-Za-z0-9_@./#&+-]*$/,
        URL: /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/,
        MOBILE: /^([+]\d{1,2})?\d{10}$/,
        PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    },
    OTPLENGTH: 6,
    OTP_EXPIRY_DURATION: 30,
    VERIFIED: {
        PENDING: 0,
        ACTIVE: 1
    },
    EMAIL_TEMPLATE: {
        REGISTER: 'verificationOtpMail.html',
        FORGOTPASSWORD: 'forgotPassword.html'
    },
    OTP_TYPE: ['register', 'forgotPassword'],
    STATUS: {
        INACTIVE: 'inactive',
        ACTIVE: 'active',
        SUSPENDED: 'suspended',
        FREEZED: 'freezed'
    },
    ENVIRONMENT: {
        TESTING: 'testing',
        LOCAL: 'local',
        DEV: 'dev',
        PRODUCTION: 'production'
    },
    DEVELOPERS_EMAIL: '<EMAIL>',
    SES_HOST: 'email-smtp.us-east-2.amazonaws.com',
    CLIENT_INFO: {
        EMAIL: '<EMAIL>'
    },
    COMPRESSION_QUALITY: 60,
    AWS_S3_PUBLIC_BUCKET: 'vaalee-assets',
    APP_NAME: 'Vaalee',
    ROLE: {
        USER: 1,
        ADMIN: 4
    },
    CONNECTION_STATUS: {
        CONNECTED: 'connected'
    },
    PROVIDER: {
        EMAIL: 'email',
        GOOGLE: 'google',
        APPLE: 'apple'
    },
    DB_BATCH_OPERATION_CHUNK_SIZE: {
        BATCH_PUT: 25,
        BATCH_DELETE: 25,
        BATCH_GET: 100
    },
    ACCESS_LEVEL: {
        APP: 'app',
        ORG_APP: 'org_app',
        ROOT: 'root'
    },
    ORGANIZATION_CATEGORIES: {
        PTO: 'PTO',
        SCHOOL: 'School',
        HOME_ROOM: 'Homeroom',
        CLUB: 'Club',
        SUPER_ORGANIZATION: 'Super Organization',
        BUSINESS: 'Business'
    },
    FEED_LIST: [
        {
            'id': '1',
            'title': 'Sample feed 1',
            'details': 'Details for sample feed 1',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User1',
            'updatedBy': 'User1',
            'organizer': 'Organizer1',
            'status': 'Active'
        },
        {
            'id': '2',
            'title': 'Sample feed 2',
            'details': 'Details for sample feed 2',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User2',
            'updatedBy': 'User2',
            'organizer': 'Organizer2',
            'status': 'Active'
        },
        {
            'id': '3',
            'title': 'Sample feed 3',
            'details': 'Details for sample feed 3',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User3',
            'updatedBy': 'User3',
            'organizer': 'Organizer3',
            'status': 'Active'
        },
        {
            'id': '4',
            'title': 'Sample feed 4',
            'details': 'Details for sample feed 4',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User4',
            'updatedBy': 'User4',
            'organizer': 'Organizer4',
            'status': 'Active'
        },
        {
            'id': '5',
            'title': 'Sample feed 5',
            'details': 'Details for sample feed 5',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User5',
            'updatedBy': 'User5',
            'organizer': 'Organizer5',
            'status': 'Active'
        },
        {
            'id': '6',
            'title': 'Sample feed 6',
            'details': 'Details for sample feed 6',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User6',
            'updatedBy': 'User6',
            'organizer': 'Organizer6',
            'status': 'Active'
        },
        {
            'id': '7',
            'title': 'Sample feed 7',
            'details': 'Details for sample feed 7',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User7',
            'updatedBy': 'User7',
            'organizer': 'Organizer7',
            'status': 'Active'
        },
        {
            'id': '8',
            'title': 'Sample feed 8',
            'details': 'Details for sample feed 8',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User8',
            'updatedBy': 'User8',
            'organizer': 'Organizer8',
            'status': 'Active'
        },
        {
            'id': '9',
            'title': 'Sample feed 9',
            'details': 'Details for sample feed 9',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User9',
            'updatedBy': 'User9',
            'organizer': 'Organizer9',
            'status': 'Active'
        },
        {
            'id': '10',
            'title': 'Sample feed 10',
            'details': 'Details for sample feed 10',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User10',
            'updatedBy': 'User10',
            'organizer': 'Organizer10',
            'status': 'Active'
        },
        {
            'id': '11',
            'title': 'Sample feed 11',
            'details': 'Details for sample feed 11',
            'imageURL': 'https://source.unsplash.com/random/800x600',
            'createdBy': 'User11',
            'updatedBy': 'User11',
            'organizer': 'Organizer11',
            'status': 'Active'
        }
    ]
};
