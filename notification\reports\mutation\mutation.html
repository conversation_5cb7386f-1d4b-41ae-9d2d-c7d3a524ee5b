<!DOCTYPE html>
  <html>
  <head>
    <meta charset="utf-8">
    <script>
      var MutationTestElements=function(re){"use strict";/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Zt=globalThis,Ar=Zt.ShadowRoot&&(Zt.ShadyCSS===void 0||Zt.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,Ln=Symbol(),Nn=new WeakMap;let ys=class{constructor(t,r,n){if(this._$cssResult$=!0,n!==Ln)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t,this.t=r}get styleSheet(){let t=this.o;const r=this.t;if(Ar&&t===void 0){const n=r!==void 0&&r.length===1;n&&(t=Nn.get(r)),t===void 0&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),n&&Nn.set(r,t))}return t}toString(){return this.cssText}};const Te=e=>new ys(typeof e=="string"?e:e+"",void 0,Ln),ws=(e,t)=>{if(Ar)e.adoptedStyleSheets=t.map(r=>r instanceof CSSStyleSheet?r:r.styleSheet);else for(const r of t){const n=document.createElement("style"),i=Zt.litNonce;i!==void 0&&n.setAttribute("nonce",i),n.textContent=r.cssText,e.appendChild(n)}},Un=Ar?e=>e:e=>e instanceof CSSStyleSheet?(t=>{let r="";for(const n of t.cssRules)r+=n.cssText;return Te(r)})(e):e;/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const{is:ks,defineProperty:xs,getOwnPropertyDescriptor:$s,getOwnPropertyNames:_s,getOwnPropertySymbols:Ss,getPrototypeOf:As}=Object,Gt=globalThis,Hn=Gt.trustedTypes,Cs=Hn?Hn.emptyScript:"",Ms=Gt.reactiveElementPolyfillSupport,Ct=(e,t)=>e,Yt={toAttribute(e,t){switch(t){case Boolean:e=e?Cs:null;break;case Object:case Array:e=e==null?e:JSON.stringify(e)}return e},fromAttribute(e,t){let r=e;switch(t){case Boolean:r=e!==null;break;case Number:r=e===null?null:Number(e);break;case Object:case Array:try{r=JSON.parse(e)}catch{r=null}}return r}},Cr=(e,t)=>!ks(e,t),qn={attribute:!0,type:String,converter:Yt,reflect:!1,useDefault:!1,hasChanged:Cr};Symbol.metadata??=Symbol("metadata"),Gt.litPropertyMetadata??=new WeakMap;let ct=class extends HTMLElement{static addInitializer(t){this._$Ei(),(this.l??=[]).push(t)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(t,r=qn){if(r.state&&(r.attribute=!1),this._$Ei(),this.prototype.hasOwnProperty(t)&&((r=Object.create(r)).wrapped=!0),this.elementProperties.set(t,r),!r.noAccessor){const n=Symbol(),i=this.getPropertyDescriptor(t,n,r);i!==void 0&&xs(this.prototype,t,i)}}static getPropertyDescriptor(t,r,n){const{get:i,set:a}=$s(this.prototype,t)??{get(){return this[r]},set(s){this[r]=s}};return{get:i,set(s){const o=i?.call(this);a?.call(this,s),this.requestUpdate(t,o,n)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)??qn}static _$Ei(){if(this.hasOwnProperty(Ct("elementProperties")))return;const t=As(this);t.finalize(),t.l!==void 0&&(this.l=[...t.l]),this.elementProperties=new Map(t.elementProperties)}static finalize(){if(this.hasOwnProperty(Ct("finalized")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(Ct("properties"))){const r=this.properties,n=[..._s(r),...Ss(r)];for(const i of n)this.createProperty(i,r[i])}const t=this[Symbol.metadata];if(t!==null){const r=litPropertyMetadata.get(t);if(r!==void 0)for(const[n,i]of r)this.elementProperties.set(n,i)}this._$Eh=new Map;for(const[r,n]of this.elementProperties){const i=this._$Eu(r,n);i!==void 0&&this._$Eh.set(i,r)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(t){const r=[];if(Array.isArray(t)){const n=new Set(t.flat(1/0).reverse());for(const i of n)r.unshift(Un(i))}else t!==void 0&&r.push(Un(t));return r}static _$Eu(t,r){const n=r.attribute;return n===!1?void 0:typeof n=="string"?n:typeof t=="string"?t.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){this._$ES=new Promise(t=>this.enableUpdating=t),this._$AL=new Map,this._$E_(),this.requestUpdate(),this.constructor.l?.forEach(t=>t(this))}addController(t){(this._$EO??=new Set).add(t),this.renderRoot!==void 0&&this.isConnected&&t.hostConnected?.()}removeController(t){this._$EO?.delete(t)}_$E_(){const t=new Map,r=this.constructor.elementProperties;for(const n of r.keys())this.hasOwnProperty(n)&&(t.set(n,this[n]),delete this[n]);t.size>0&&(this._$Ep=t)}createRenderRoot(){const t=this.shadowRoot??this.attachShadow(this.constructor.shadowRootOptions);return ws(t,this.constructor.elementStyles),t}connectedCallback(){this.renderRoot??=this.createRenderRoot(),this.enableUpdating(!0),this._$EO?.forEach(t=>t.hostConnected?.())}enableUpdating(t){}disconnectedCallback(){this._$EO?.forEach(t=>t.hostDisconnected?.())}attributeChangedCallback(t,r,n){this._$AK(t,n)}_$ET(t,r){const n=this.constructor.elementProperties.get(t),i=this.constructor._$Eu(t,n);if(i!==void 0&&n.reflect===!0){const a=(n.converter?.toAttribute!==void 0?n.converter:Yt).toAttribute(r,n.type);this._$Em=t,a==null?this.removeAttribute(i):this.setAttribute(i,a),this._$Em=null}}_$AK(t,r){const n=this.constructor,i=n._$Eh.get(t);if(i!==void 0&&this._$Em!==i){const a=n.getPropertyOptions(i),s=typeof a.converter=="function"?{fromAttribute:a.converter}:a.converter?.fromAttribute!==void 0?a.converter:Yt;this._$Em=i,this[i]=s.fromAttribute(r,a.type)??this._$Ej?.get(i)??null,this._$Em=null}}requestUpdate(t,r,n){if(t!==void 0){const i=this.constructor,a=this[t];if(n??=i.getPropertyOptions(t),!((n.hasChanged??Cr)(a,r)||n.useDefault&&n.reflect&&a===this._$Ej?.get(t)&&!this.hasAttribute(i._$Eu(t,n))))return;this.C(t,r,n)}this.isUpdatePending===!1&&(this._$ES=this._$EP())}C(t,r,{useDefault:n,reflect:i,wrapped:a},s){n&&!(this._$Ej??=new Map).has(t)&&(this._$Ej.set(t,s??r??this[t]),a!==!0||s!==void 0)||(this._$AL.has(t)||(this.hasUpdated||n||(r=void 0),this._$AL.set(t,r)),i===!0&&this._$Em!==t&&(this._$Eq??=new Set).add(t))}async _$EP(){this.isUpdatePending=!0;try{await this._$ES}catch(r){Promise.reject(r)}const t=this.scheduleUpdate();return t!=null&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){if(!this.isUpdatePending)return;if(!this.hasUpdated){if(this.renderRoot??=this.createRenderRoot(),this._$Ep){for(const[i,a]of this._$Ep)this[i]=a;this._$Ep=void 0}const n=this.constructor.elementProperties;if(n.size>0)for(const[i,a]of n){const{wrapped:s}=a,o=this[i];s!==!0||this._$AL.has(i)||o===void 0||this.C(i,void 0,a,o)}}let t=!1;const r=this._$AL;try{t=this.shouldUpdate(r),t?(this.willUpdate(r),this._$EO?.forEach(n=>n.hostUpdate?.()),this.update(r)):this._$EM()}catch(n){throw t=!1,this._$EM(),n}t&&this._$AE(r)}willUpdate(t){}_$AE(t){this._$EO?.forEach(r=>r.hostUpdated?.()),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$EM(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(t){return!0}update(t){this._$Eq&&=this._$Eq.forEach(r=>this._$ET(r,this[r])),this._$EM()}updated(t){}firstUpdated(t){}};ct.elementStyles=[],ct.shadowRootOptions={mode:"open"},ct[Ct("elementProperties")]=new Map,ct[Ct("finalized")]=new Map,Ms?.({ReactiveElement:ct}),(Gt.reactiveElementVersions??=[]).push("2.1.0");/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Mr=globalThis,Jt=Mr.trustedTypes,Vn=Jt?Jt.createPolicy("lit-html",{createHTML:e=>e}):void 0,Wn="$lit$",Ne=`lit$${Math.random().toFixed(9).slice(2)}$`,Kn="?"+Ne,Es=`<${Kn}>`,Ye=document,Mt=()=>Ye.createComment(""),Et=e=>e===null||typeof e!="object"&&typeof e!="function",Er=Array.isArray,Ts=e=>Er(e)||typeof e?.[Symbol.iterator]=="function",Tr=`[ 	
\f\r]`,Tt=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,Zn=/-->/g,Gn=/>/g,Je=RegExp(`>|${Tr}(?:([^\\s"'>=/]+)(${Tr}*=${Tr}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`,"g"),Yn=/'/g,Jn=/"/g,Qn=/^(?:script|style|textarea|title)$/i,Xn=e=>(t,...r)=>({_$litType$:e,strings:t,values:r}),b=Xn(1),ee=Xn(2),Pe=Symbol.for("lit-noChange"),P=Symbol.for("lit-nothing"),ei=new WeakMap,Qe=Ye.createTreeWalker(Ye,129);function ti(e,t){if(!Er(e)||!e.hasOwnProperty("raw"))throw Error("invalid template strings array");return Vn!==void 0?Vn.createHTML(t):t}const Ps=(e,t)=>{const r=e.length-1,n=[];let i,a=t===2?"<svg>":t===3?"<math>":"",s=Tt;for(let o=0;o<r;o++){const l=e[o];let u,x,v=-1,$=0;for(;$<l.length&&(s.lastIndex=$,x=s.exec(l),x!==null);)$=s.lastIndex,s===Tt?x[1]==="!--"?s=Zn:x[1]!==void 0?s=Gn:x[2]!==void 0?(Qn.test(x[2])&&(i=RegExp("</"+x[2],"g")),s=Je):x[3]!==void 0&&(s=Je):s===Je?x[0]===">"?(s=i??Tt,v=-1):x[1]===void 0?v=-2:(v=s.lastIndex-x[2].length,u=x[1],s=x[3]===void 0?Je:x[3]==='"'?Jn:Yn):s===Jn||s===Yn?s=Je:s===Zn||s===Gn?s=Tt:(s=Je,i=void 0);const y=s===Je&&e[o+1].startsWith("/>")?" ":"";a+=s===Tt?l+Es:v>=0?(n.push(u),l.slice(0,v)+Wn+l.slice(v)+Ne+y):l+Ne+(v===-2?o:y)}return[ti(e,a+(e[r]||"<?>")+(t===2?"</svg>":t===3?"</math>":"")),n]};class Pt{constructor({strings:t,_$litType$:r},n){let i;this.parts=[];let a=0,s=0;const o=t.length-1,l=this.parts,[u,x]=Ps(t,r);if(this.el=Pt.createElement(u,n),Qe.currentNode=this.el.content,r===2||r===3){const v=this.el.content.firstChild;v.replaceWith(...v.childNodes)}for(;(i=Qe.nextNode())!==null&&l.length<o;){if(i.nodeType===1){if(i.hasAttributes())for(const v of i.getAttributeNames())if(v.endsWith(Wn)){const $=x[s++],y=i.getAttribute(v).split(Ne),C=/([.?@])?(.*)/.exec($);l.push({type:1,index:a,name:C[2],strings:y,ctor:C[1]==="."?Fs:C[1]==="?"?Os:C[1]==="@"?Is:Qt}),i.removeAttribute(v)}else v.startsWith(Ne)&&(l.push({type:6,index:a}),i.removeAttribute(v));if(Qn.test(i.tagName)){const v=i.textContent.split(Ne),$=v.length-1;if($>0){i.textContent=Jt?Jt.emptyScript:"";for(let y=0;y<$;y++)i.append(v[y],Mt()),Qe.nextNode(),l.push({type:2,index:++a});i.append(v[$],Mt())}}}else if(i.nodeType===8)if(i.data===Kn)l.push({type:2,index:a});else{let v=-1;for(;(v=i.data.indexOf(Ne,v+1))!==-1;)l.push({type:7,index:a}),v+=Ne.length-1}a++}}static createElement(t,r){const n=Ye.createElement("template");return n.innerHTML=t,n}}function ut(e,t,r=e,n){if(t===Pe)return t;let i=n!==void 0?r._$Co?.[n]:r._$Cl;const a=Et(t)?void 0:t._$litDirective$;return i?.constructor!==a&&(i?._$AO?.(!1),a===void 0?i=void 0:(i=new a(e),i._$AT(e,r,n)),n!==void 0?(r._$Co??=[])[n]=i:r._$Cl=i),i!==void 0&&(t=ut(e,i._$AS(e,t.values),i,n)),t}let zs=class{constructor(t,r){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=r}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){const{el:{content:r},parts:n}=this._$AD,i=(t?.creationScope??Ye).importNode(r,!0);Qe.currentNode=i;let a=Qe.nextNode(),s=0,o=0,l=n[0];for(;l!==void 0;){if(s===l.index){let u;l.type===2?u=new dt(a,a.nextSibling,this,t):l.type===1?u=new l.ctor(a,l.name,l.strings,this,t):l.type===6&&(u=new Rs(a,this,t)),this._$AV.push(u),l=n[++o]}s!==l?.index&&(a=Qe.nextNode(),s++)}return Qe.currentNode=Ye,i}p(t){let r=0;for(const n of this._$AV)n!==void 0&&(n.strings!==void 0?(n._$AI(t,n,r),r+=n.strings.length-2):n._$AI(t[r])),r++}};class dt{get _$AU(){return this._$AM?._$AU??this._$Cv}constructor(t,r,n,i){this.type=2,this._$AH=P,this._$AN=void 0,this._$AA=t,this._$AB=r,this._$AM=n,this.options=i,this._$Cv=i?.isConnected??!0}get parentNode(){let t=this._$AA.parentNode;const r=this._$AM;return r!==void 0&&t?.nodeType===11&&(t=r.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,r=this){t=ut(this,t,r),Et(t)?t===P||t==null||t===""?(this._$AH!==P&&this._$AR(),this._$AH=P):t!==this._$AH&&t!==Pe&&this._(t):t._$litType$!==void 0?this.$(t):t.nodeType!==void 0?this.T(t):Ts(t)?this.k(t):this._(t)}O(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}T(t){this._$AH!==t&&(this._$AR(),this._$AH=this.O(t))}_(t){this._$AH!==P&&Et(this._$AH)?this._$AA.nextSibling.data=t:this.T(Ye.createTextNode(t)),this._$AH=t}$(t){const{values:r,_$litType$:n}=t,i=typeof n=="number"?this._$AC(t):(n.el===void 0&&(n.el=Pt.createElement(ti(n.h,n.h[0]),this.options)),n);if(this._$AH?._$AD===i)this._$AH.p(r);else{const a=new zs(i,this),s=a.u(this.options);a.p(r),this.T(s),this._$AH=a}}_$AC(t){let r=ei.get(t.strings);return r===void 0&&ei.set(t.strings,r=new Pt(t)),r}k(t){Er(this._$AH)||(this._$AH=[],this._$AR());const r=this._$AH;let n,i=0;for(const a of t)i===r.length?r.push(n=new dt(this.O(Mt()),this.O(Mt()),this,this.options)):n=r[i],n._$AI(a),i++;i<r.length&&(this._$AR(n&&n._$AB.nextSibling,i),r.length=i)}_$AR(t=this._$AA.nextSibling,r){for(this._$AP?.(!1,!0,r);t&&t!==this._$AB;){const n=t.nextSibling;t.remove(),t=n}}setConnected(t){this._$AM===void 0&&(this._$Cv=t,this._$AP?.(t))}}class Qt{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(t,r,n,i,a){this.type=1,this._$AH=P,this._$AN=void 0,this.element=t,this.name=r,this._$AM=i,this.options=a,n.length>2||n[0]!==""||n[1]!==""?(this._$AH=Array(n.length-1).fill(new String),this.strings=n):this._$AH=P}_$AI(t,r=this,n,i){const a=this.strings;let s=!1;if(a===void 0)t=ut(this,t,r,0),s=!Et(t)||t!==this._$AH&&t!==Pe,s&&(this._$AH=t);else{const o=t;let l,u;for(t=a[0],l=0;l<a.length-1;l++)u=ut(this,o[n+l],r,l),u===Pe&&(u=this._$AH[l]),s||=!Et(u)||u!==this._$AH[l],u===P?t=P:t!==P&&(t+=(u??"")+a[l+1]),this._$AH[l]=u}s&&!i&&this.j(t)}j(t){t===P?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,t??"")}}class Fs extends Qt{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===P?void 0:t}}class Os extends Qt{constructor(){super(...arguments),this.type=4}j(t){this.element.toggleAttribute(this.name,!!t&&t!==P)}}class Is extends Qt{constructor(t,r,n,i,a){super(t,r,n,i,a),this.type=5}_$AI(t,r=this){if((t=ut(this,t,r,0)??P)===Pe)return;const n=this._$AH,i=t===P&&n!==P||t.capture!==n.capture||t.once!==n.once||t.passive!==n.passive,a=t!==P&&(n===P||i);i&&this.element.removeEventListener(this.name,this,n),a&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){typeof this._$AH=="function"?this._$AH.call(this.options?.host??this.element,t):this._$AH.handleEvent(t)}}class Rs{constructor(t,r,n){this.element=t,this.type=6,this._$AN=void 0,this._$AM=r,this.options=n}get _$AU(){return this._$AM._$AU}_$AI(t){ut(this,t)}}const Ds={I:dt},Bs=Mr.litHtmlPolyfillSupport;Bs?.(Pt,dt),(Mr.litHtmlVersions??=[]).push("3.3.0");const js=(e,t,r)=>{const n=r?.renderBefore??t;let i=n._$litPart$;if(i===void 0){const a=r?.renderBefore??null;n._$litPart$=i=new dt(t.insertBefore(Mt(),a),a,void 0,r??{})}return i._$AI(e),i};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Pr=globalThis;let zt=class extends ct{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){const t=super.createRenderRoot();return this.renderOptions.renderBefore??=t.firstChild,t}update(t){const r=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Do=js(r,this.renderRoot,this.renderOptions)}connectedCallback(){super.connectedCallback(),this._$Do?.setConnected(!0)}disconnectedCallback(){super.disconnectedCallback(),this._$Do?.setConnected(!1)}render(){return Pe}};zt._$litElement$=!0,zt.finalized=!0,Pr.litElementHydrateSupport?.({LitElement:zt});const Ls=Pr.litElementPolyfillSupport;Ls?.({LitElement:zt}),(Pr.litElementVersions??=[]).push("4.2.0");/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const se=e=>(t,r)=>{r!==void 0?r.addInitializer(()=>{customElements.define(e,t)}):customElements.define(e,t)};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Ns={attribute:!0,type:String,converter:Yt,reflect:!1,hasChanged:Cr},Us=(e=Ns,t,r)=>{const{kind:n,metadata:i}=r;let a=globalThis.litPropertyMetadata.get(i);if(a===void 0&&globalThis.litPropertyMetadata.set(i,a=new Map),n==="setter"&&((e=Object.create(e)).wrapped=!0),a.set(r.name,e),n==="accessor"){const{name:s}=r;return{set(o){const l=t.get.call(this);t.set.call(this,o),this.requestUpdate(s,l,e)},init(o){return o!==void 0&&this.C(s,void 0,e,o),o}}}if(n==="setter"){const{name:s}=r;return function(o){const l=this[s];t.call(this,o),this.requestUpdate(s,l,e)}}throw Error("Unsupported decorator location: "+n)};function I(e){return(t,r)=>typeof r=="object"?Us(e,t,r):((n,i,a)=>{const s=i.hasOwnProperty(a);return i.constructor.createProperty(a,n),s?Object.getOwnPropertyDescriptor(i,a):void 0})(e,t,r)}/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */function he(e){return I({...e,state:!0,attribute:!1})}/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Hs=(e,t,r)=>(r.configurable=!0,r.enumerable=!0,Reflect.decorate&&typeof t!="object"&&Object.defineProperty(e,t,r),r);/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */function Xt(e,t){return(r,n,i)=>{const a=s=>s.renderRoot?.querySelector(e)??null;return Hs(r,n,{get(){return a(this)}})}}/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Ft=e=>e??P,qs="/";function ri(e,t,r){const n=Object.keys(e),i=Vs(n),a=Object.create(null);return n.forEach(s=>{const o=ni(s.startsWith(t)?s.substr(t.length):s);a[ni(s.substr(i.length))]=r(e[s],o)}),a}function ni(e){return e.split(/\/|\\/).filter(Boolean).join("/")}function Vs(e){const t=e.map(n=>n.split(/\/|\\/).slice(0,-1));if(e.length)return t.reduce(r).join(qs);return"";function r(n,i){for(let a=0;a<n.length;a++)if(n[a]!==i[a])return n.splice(0,a);return n}}function Ws(e,t){const r=n=>n.file?`1${n.name}`:`0${n.name}`;return r(e).localeCompare(r(t))}function Ks(e,t){return e.reduce((r,n)=>{const i=t(n);return Object.prototype.hasOwnProperty.call(r,i)||(r[i]=[]),r[i].push(n),r},{})}var Se;(function(e){e[e.maxAsciiCharacter=127]="maxAsciiCharacter",e[e.lineFeed=10]="lineFeed",e[e.carriageReturn=13]="carriageReturn",e[e.lineSeparator=8232]="lineSeparator",e[e.paragraphSeparator=8233]="paragraphSeparator"})(Se||(Se={}));function Zs(e){return e===Se.lineFeed||e===Se.carriageReturn||e===Se.lineSeparator||e===Se.paragraphSeparator}function Gs(e){const t=[];let r=0,n=0;function i(a){t.push(n),n=a}for(i(0);r<e.length;){const a=e.charCodeAt(r);switch(r++,a){case Se.carriageReturn:e.charCodeAt(r)===Se.lineFeed&&r++,i(r);break;case Se.lineFeed:i(r);break;default:a>Se.maxAsciiCharacter&&Zs(a)&&i(r);break}}return t.push(n),t}function zr(e){if(e===void 0)throw new Error("mutant.sourceFile was not defined")}class Ys{coveredBy;description;duration;id;killedBy;location;mutatorName;replacement;static;status;statusReason;testsCompleted;get coveredByTests(){if(this.#e.size)return Array.from(this.#e.values())}set coveredByTests(t){this.#e=new Map(t.map(r=>[r.id,r]))}get killedByTests(){if(this.#t.size)return Array.from(this.#t.values())}set killedByTests(t){this.#t=new Map(t.map(r=>[r.id,r]))}#e=new Map;#t=new Map;constructor(t){this.coveredBy=t.coveredBy,this.description=t.description,this.duration=t.duration,this.id=t.id,this.killedBy=t.killedBy,this.location=t.location,this.mutatorName=t.mutatorName,this.replacement=t.replacement,this.static=t.static,this.status=t.status,this.statusReason=t.statusReason,this.testsCompleted=t.testsCompleted}addCoveredBy(t){this.#e.set(t.id,t)}addKilledBy(t){this.#t.set(t.id,t)}getMutatedLines(){return zr(this.sourceFile),this.sourceFile.getMutationLines(this)}getOriginalLines(){return zr(this.sourceFile),this.sourceFile.getLines(this.location)}get fileName(){return zr(this.sourceFile),this.sourceFile.name}update(){this.sourceFile?.result?.file&&this.sourceFile.result.updateAllMetrics()}}function ii(e){if(e===void 0)throw new Error("sourceFile.source is undefined")}class ai{lineMap;getLineMap(){return ii(this.source),this.lineMap??(this.lineMap=Gs(this.source))}getLines(t){ii(this.source);const r=this.getLineMap();return this.source.substring(r[t.start.line],r[(t.end??t.start).line+1])}}class Js extends ai{name;language;source;mutants;result;constructor(t,r){super(),this.name=r,this.language=t.language,this.source=t.source,this.mutants=t.mutants.map(n=>{const i=new Ys(n);return i.sourceFile=this,i})}getMutationLines(t){const r=this.getLineMap(),n=r[t.location.start.line],i=r[t.location.end.line],a=r[t.location.end.line+1];return`${this.source.substr(n,t.location.start.column-1)}${t.replacement??t.description??t.mutatorName}${this.source.substring(i+t.location.end.column-1,a)}`}}class si{parent;name;file;childResults;metrics;constructor(t,r,n,i){this.name=t,this.childResults=r,this.metrics=n,this.file=i}updateParent(t){this.parent=t,this.childResults.forEach(r=>r.updateParent(this))}updateAllMetrics(){if(this.parent!==void 0){this.parent.updateAllMetrics();return}this.updateMetrics()}updateMetrics(){if(this.file===void 0){this.childResults.forEach(r=>{r.updateMetrics()});const t=this.#e(this.childResults);if(t.length===0)return;t[0].tests?this.metrics=Or(t):this.metrics=er(t);return}this.file.tests?this.metrics=Or([this.file]):this.metrics=er([this.file])}#e(t){const r=[];return t.length===0||t.forEach(n=>{if(n.file){r.push(n.file);return}r.push(...this.#e(n.childResults))}),r}}function oi(e){if(e===void 0)throw new Error("test.sourceFile was not defined")}function Qs(e){if(e===void 0)throw new Error("test.location was not defined")}var ne;(function(e){e.Killing="Killing",e.Covering="Covering",e.NotCovering="NotCovering"})(ne||(ne={}));class Xs{id;name;location;get killedMutants(){if(this.#e.size)return Array.from(this.#e.values())}get coveredMutants(){if(this.#t.size)return Array.from(this.#t.values())}#e=new Map;#t=new Map;addCovered(t){this.#t.set(t.id,t)}addKilled(t){this.#e.set(t.id,t)}constructor(t){Object.entries(t).forEach(([r,n])=>{this[r]=n})}getLines(){return oi(this.sourceFile),Qs(this.location),this.sourceFile.getLines(this.location)}get fileName(){return oi(this.sourceFile),this.sourceFile.name}get status(){return this.#e.size?ne.Killing:this.#t.size?ne.Covering:ne.NotCovering}update(){this.sourceFile?.result?.file&&this.sourceFile.result.updateAllMetrics()}}class li extends ai{name;tests;source;result;constructor(t,r){super(),this.name=r,this.source=t.source,this.tests=t.tests.map(n=>{const i=new Xs(n);return i.sourceFile=this,i})}}const ci=NaN,ui="All files",eo="All tests";function to(e){const{files:t,testFiles:r,projectRoot:n=""}=e,i=ri(t,n,(a,s)=>new Js(a,s));if(r&&Object.keys(r).length){const a=ri(r,n,(s,o)=>new li(s,o));return no(Object.values(i).flatMap(s=>s.mutants),Object.values(a).flatMap(s=>s.tests)),{systemUnderTestMetrics:Fr(ui,i,er),testMetrics:Fr(eo,a,Or)}}return{systemUnderTestMetrics:Fr(ui,i,er),testMetrics:void 0}}function Fr(e,t,r){const n=Object.keys(t);return n.length===1&&n[0]===""?hi(e,t[n[0]],r):di(e,t,r)}function di(e,t,r){const n=r(Object.values(t)),i=ro(t,r);return new si(e,i,n)}function hi(e,t,r){return new si(e,[],r([t]),t)}function ro(e,t){const r=Ks(Object.entries(e),n=>n[0].split("/")[0]);return Object.keys(r).map(n=>{if(r[n].length>1||r[n][0][0]!==n){const i={};return r[n].forEach(a=>i[a[0].substr(n.length+1)]=a[1]),di(n,i,t)}else{const[i,a]=r[n][0];return hi(i,a,t)}}).sort(Ws)}function no(e,t){const r=new Map(t.map(n=>[n.id,n]));for(const n of e){const i=n.coveredBy??[];for(const s of i){const o=r.get(s);o&&(n.addCoveredBy(o),o.addCovered(n))}const a=n.killedBy??[];for(const s of a){const o=r.get(s);o&&(n.addKilledBy(o),o.addKilled(n))}}}function Or(e){const t=e.flatMap(n=>n.tests),r=n=>t.filter(i=>i.status===n).length;return{total:t.length,killing:r(ne.Killing),covering:r(ne.Covering),notCovering:r(ne.NotCovering)}}function er(e){const t=e.flatMap(H=>H.mutants),r=H=>t.filter(X=>X.status===H).length,n=r("Pending"),i=r("Killed"),a=r("Timeout"),s=r("Survived"),o=r("NoCoverage"),l=r("RuntimeError"),u=r("CompileError"),x=r("Ignored"),v=a+i,$=s+o,y=v+s,C=$+v,z=l+u;return{pending:n,killed:i,timeout:a,survived:s,noCoverage:o,runtimeErrors:l,compileErrors:u,ignored:x,totalDetected:v,totalUndetected:$,totalCovered:y,totalValid:C,totalInvalid:z,mutationScore:C>0?v/C*100:ci,totalMutants:C+z+x+n,mutationScoreBasedOnCoveredCode:C>0?v/y*100||0:ci}}var Ir=function(e,t){return Ir=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(r[i]=n[i])},Ir(e,t)};function Ue(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Ir(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}function io(e,t,r,n){function i(a){return a instanceof r?a:new r(function(s){s(a)})}return new(r||(r=Promise))(function(a,s){function o(x){try{u(n.next(x))}catch(v){s(v)}}function l(x){try{u(n.throw(x))}catch(v){s(v)}}function u(x){x.done?a(x.value):i(x.value).then(o,l)}u((n=n.apply(e,t||[])).next())})}function pi(e,t){var r={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},n,i,a,s=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return s.next=o(0),s.throw=o(1),s.return=o(2),typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function o(u){return function(x){return l([u,x])}}function l(u){if(n)throw new TypeError("Generator is already executing.");for(;s&&(s=0,u[0]&&(r=0)),r;)try{if(n=1,i&&(a=u[0]&2?i.return:u[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,u[1])).done)return a;switch(i=0,a&&(u=[u[0]&2,a.value]),u[0]){case 0:case 1:a=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,i=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(a=r.trys,!(a=a.length>0&&a[a.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!a||u[1]>a[0]&&u[1]<a[3])){r.label=u[1];break}if(u[0]===6&&r.label<a[1]){r.label=a[1],a=u;break}if(a&&r.label<a[2]){r.label=a[2],r.ops.push(u);break}a[2]&&r.ops.pop(),r.trys.pop();continue}u=t.call(e,r)}catch(x){u=[6,x],i=0}finally{n=a=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function ht(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function pt(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),i,a=[],s;try{for(;(t===void 0||t-- >0)&&!(i=n.next()).done;)a.push(i.value)}catch(o){s={error:o}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return a}function Ot(e,t,r){if(r||arguments.length===2)for(var n=0,i=t.length,a;n<i;n++)(a||!(n in t))&&(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))}function ft(e){return this instanceof ft?(this.v=e,this):new ft(e)}function ao(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(e,t||[]),i,a=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",s),i[Symbol.asyncIterator]=function(){return this},i;function s(y){return function(C){return Promise.resolve(C).then(y,v)}}function o(y,C){n[y]&&(i[y]=function(z){return new Promise(function(H,X){a.push([y,z,H,X])>1||l(y,z)})},C&&(i[y]=C(i[y])))}function l(y,C){try{u(n[y](C))}catch(z){$(a[0][3],z)}}function u(y){y.value instanceof ft?Promise.resolve(y.value.v).then(x,v):$(a[0][2],y)}function x(y){l("next",y)}function v(y){l("throw",y)}function $(y,C){y(C),a.shift(),a.length&&l(a[0][0],a[0][1])}}function so(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],r;return t?t.call(e):(e=typeof ht=="function"?ht(e):e[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(a){r[a]=e[a]&&function(s){return new Promise(function(o,l){s=e[a](s),i(o,l,s.done,s.value)})}}function i(a,s,o,l){Promise.resolve(l).then(function(u){a({value:u,done:o})},s)}}typeof SuppressedError=="function"&&SuppressedError;function K(e){return typeof e=="function"}function fi(e){var t=function(n){Error.call(n),n.stack=new Error().stack},r=e(t);return r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r}var Rr=fi(function(e){return function(r){e(this),this.message=r?r.length+` errors occurred during unsubscription:
`+r.map(function(n,i){return i+1+") "+n.toString()}).join(`
  `):"",this.name="UnsubscriptionError",this.errors=r}});function tr(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var gt=function(){function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){var t,r,n,i,a;if(!this.closed){this.closed=!0;var s=this._parentage;if(s)if(this._parentage=null,Array.isArray(s))try{for(var o=ht(s),l=o.next();!l.done;l=o.next()){var u=l.value;u.remove(this)}}catch(z){t={error:z}}finally{try{l&&!l.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}else s.remove(this);var x=this.initialTeardown;if(K(x))try{x()}catch(z){a=z instanceof Rr?z.errors:[z]}var v=this._finalizers;if(v){this._finalizers=null;try{for(var $=ht(v),y=$.next();!y.done;y=$.next()){var C=y.value;try{vi(C)}catch(z){a=a??[],z instanceof Rr?a=Ot(Ot([],pt(a)),pt(z.errors)):a.push(z)}}}catch(z){n={error:z}}finally{try{y&&!y.done&&(i=$.return)&&i.call($)}finally{if(n)throw n.error}}}if(a)throw new Rr(a)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)vi(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(r=this._finalizers)!==null&&r!==void 0?r:[]).push(t)}},e.prototype._hasParent=function(t){var r=this._parentage;return r===t||Array.isArray(r)&&r.includes(t)},e.prototype._addParent=function(t){var r=this._parentage;this._parentage=Array.isArray(r)?(r.push(t),r):r?[r,t]:t},e.prototype._removeParent=function(t){var r=this._parentage;r===t?this._parentage=null:Array.isArray(r)&&tr(r,t)},e.prototype.remove=function(t){var r=this._finalizers;r&&tr(r,t),t instanceof e&&t._removeParent(this)},e.EMPTY=function(){var t=new e;return t.closed=!0,t}(),e}(),gi=gt.EMPTY;function mi(e){return e instanceof gt||e&&"closed"in e&&K(e.remove)&&K(e.add)&&K(e.unsubscribe)}function vi(e){K(e)?e():e.unsubscribe()}var oo={Promise:void 0},lo={setTimeout:function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return setTimeout.apply(void 0,Ot([e,t],pt(r)))},clearTimeout:function(e){return clearTimeout(e)},delegate:void 0};function bi(e){lo.setTimeout(function(){throw e})}function Dr(){}function rr(e){e()}var Br=function(e){Ue(t,e);function t(r){var n=e.call(this)||this;return n.isStopped=!1,r?(n.destination=r,mi(r)&&r.add(n)):n.destination=ho,n}return t.create=function(r,n,i){return new jr(r,n,i)},t.prototype.next=function(r){this.isStopped||this._next(r)},t.prototype.error=function(r){this.isStopped||(this.isStopped=!0,this._error(r))},t.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(r){this.destination.next(r)},t.prototype._error=function(r){try{this.destination.error(r)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(gt),co=function(){function e(t){this.partialObserver=t}return e.prototype.next=function(t){var r=this.partialObserver;if(r.next)try{r.next(t)}catch(n){nr(n)}},e.prototype.error=function(t){var r=this.partialObserver;if(r.error)try{r.error(t)}catch(n){nr(n)}else nr(t)},e.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(r){nr(r)}},e}(),jr=function(e){Ue(t,e);function t(r,n,i){var a=e.call(this)||this,s;return K(r)||!r?s={next:r??void 0,error:n??void 0,complete:i??void 0}:s=r,a.destination=new co(s),a}return t}(Br);function nr(e){bi(e)}function uo(e){throw e}var ho={closed:!0,next:Dr,error:uo,complete:Dr},Lr=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}();function Nr(e){return e}function po(e){return e.length===0?Nr:e.length===1?e[0]:function(r){return e.reduce(function(n,i){return i(n)},r)}}var pe=function(){function e(t){t&&(this._subscribe=t)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(t,r,n){var i=this,a=go(t)?t:new jr(t,r,n);return rr(function(){var s=i,o=s.operator,l=s.source;a.add(o?o.call(a,l):l?i._subscribe(a):i._trySubscribe(a))}),a},e.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(r){t.error(r)}},e.prototype.forEach=function(t,r){var n=this;return r=yi(r),new r(function(i,a){var s=new jr({next:function(o){try{t(o)}catch(l){a(l),s.unsubscribe()}},error:a,complete:i});n.subscribe(s)})},e.prototype._subscribe=function(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)},e.prototype[Lr]=function(){return this},e.prototype.pipe=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return po(t)(this)},e.prototype.toPromise=function(t){var r=this;return t=yi(t),new t(function(n,i){var a;r.subscribe(function(s){return a=s},function(s){return i(s)},function(){return n(a)})})},e.create=function(t){return new e(t)},e}();function yi(e){var t;return(t=e??oo.Promise)!==null&&t!==void 0?t:Promise}function fo(e){return e&&K(e.next)&&K(e.error)&&K(e.complete)}function go(e){return e&&e instanceof Br||fo(e)&&mi(e)}function mo(e){return K(e?.lift)}function mt(e){return function(t){if(mo(t))return t.lift(function(r){try{return e(r,this)}catch(n){this.error(n)}});throw new TypeError("Unable to lift unknown Observable type")}}function Xe(e,t,r,n,i){return new vo(e,t,r,n,i)}var vo=function(e){Ue(t,e);function t(r,n,i,a,s,o){var l=e.call(this,r)||this;return l.onFinalize=s,l.shouldUnsubscribe=o,l._next=n?function(u){try{n(u)}catch(x){r.error(x)}}:e.prototype._next,l._error=a?function(u){try{a(u)}catch(x){r.error(x)}finally{this.unsubscribe()}}:e.prototype._error,l._complete=i?function(){try{i()}catch(u){r.error(u)}finally{this.unsubscribe()}}:e.prototype._complete,l}return t.prototype.unsubscribe=function(){var r;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var n=this.closed;e.prototype.unsubscribe.call(this),!n&&((r=this.onFinalize)===null||r===void 0||r.call(this))}},t}(Br),bo=fi(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),wi=function(e){Ue(t,e);function t(){var r=e.call(this)||this;return r.closed=!1,r.currentObservers=null,r.observers=[],r.isStopped=!1,r.hasError=!1,r.thrownError=null,r}return t.prototype.lift=function(r){var n=new ki(this,this);return n.operator=r,n},t.prototype._throwIfClosed=function(){if(this.closed)throw new bo},t.prototype.next=function(r){var n=this;rr(function(){var i,a;if(n._throwIfClosed(),!n.isStopped){n.currentObservers||(n.currentObservers=Array.from(n.observers));try{for(var s=ht(n.currentObservers),o=s.next();!o.done;o=s.next()){var l=o.value;l.next(r)}}catch(u){i={error:u}}finally{try{o&&!o.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}}})},t.prototype.error=function(r){var n=this;rr(function(){if(n._throwIfClosed(),!n.isStopped){n.hasError=n.isStopped=!0,n.thrownError=r;for(var i=n.observers;i.length;)i.shift().error(r)}})},t.prototype.complete=function(){var r=this;rr(function(){if(r._throwIfClosed(),!r.isStopped){r.isStopped=!0;for(var n=r.observers;n.length;)n.shift().complete()}})},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var r;return((r=this.observers)===null||r===void 0?void 0:r.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(r){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,r)},t.prototype._subscribe=function(r){return this._throwIfClosed(),this._checkFinalizedStatuses(r),this._innerSubscribe(r)},t.prototype._innerSubscribe=function(r){var n=this,i=this,a=i.hasError,s=i.isStopped,o=i.observers;return a||s?gi:(this.currentObservers=null,o.push(r),new gt(function(){n.currentObservers=null,tr(o,r)}))},t.prototype._checkFinalizedStatuses=function(r){var n=this,i=n.hasError,a=n.thrownError,s=n.isStopped;i?r.error(a):s&&r.complete()},t.prototype.asObservable=function(){var r=new pe;return r.source=this,r},t.create=function(r,n){return new ki(r,n)},t}(pe),ki=function(e){Ue(t,e);function t(r,n){var i=e.call(this)||this;return i.destination=r,i.source=n,i}return t.prototype.next=function(r){var n,i;(i=(n=this.destination)===null||n===void 0?void 0:n.next)===null||i===void 0||i.call(n,r)},t.prototype.error=function(r){var n,i;(i=(n=this.destination)===null||n===void 0?void 0:n.error)===null||i===void 0||i.call(n,r)},t.prototype.complete=function(){var r,n;(n=(r=this.destination)===null||r===void 0?void 0:r.complete)===null||n===void 0||n.call(r)},t.prototype._subscribe=function(r){var n,i;return(i=(n=this.source)===null||n===void 0?void 0:n.subscribe(r))!==null&&i!==void 0?i:gi},t}(wi),yo={now:function(){return Date.now()}},wo=function(e){Ue(t,e);function t(r,n){return e.call(this)||this}return t.prototype.schedule=function(r,n){return this},t}(gt),xi={setInterval:function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return setInterval.apply(void 0,Ot([e,t],pt(r)))},clearInterval:function(e){return clearInterval(e)},delegate:void 0},ko=function(e){Ue(t,e);function t(r,n){var i=e.call(this,r,n)||this;return i.scheduler=r,i.work=n,i.pending=!1,i}return t.prototype.schedule=function(r,n){var i;if(n===void 0&&(n=0),this.closed)return this;this.state=r;var a=this.id,s=this.scheduler;return a!=null&&(this.id=this.recycleAsyncId(s,a,n)),this.pending=!0,this.delay=n,this.id=(i=this.id)!==null&&i!==void 0?i:this.requestAsyncId(s,this.id,n),this},t.prototype.requestAsyncId=function(r,n,i){return i===void 0&&(i=0),xi.setInterval(r.flush.bind(r,this),i)},t.prototype.recycleAsyncId=function(r,n,i){if(i===void 0&&(i=0),i!=null&&this.delay===i&&this.pending===!1)return n;n!=null&&xi.clearInterval(n)},t.prototype.execute=function(r,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var i=this._execute(r,n);if(i)return i;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(r,n){var i=!1,a;try{this.work(r)}catch(s){i=!0,a=s||new Error("Scheduled action threw falsy error")}if(i)return this.unsubscribe(),a},t.prototype.unsubscribe=function(){if(!this.closed){var r=this,n=r.id,i=r.scheduler,a=i.actions;this.work=this.state=this.scheduler=null,this.pending=!1,tr(a,this),n!=null&&(this.id=this.recycleAsyncId(i,n,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(wo),$i=function(){function e(t,r){r===void 0&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(t,r,n){return r===void 0&&(r=0),new this.schedulerActionCtor(this,t).schedule(n,r)},e.now=yo.now,e}(),xo=function(e){Ue(t,e);function t(r,n){n===void 0&&(n=$i.now);var i=e.call(this,r,n)||this;return i.actions=[],i._active=!1,i}return t.prototype.flush=function(r){var n=this.actions;if(this._active){n.push(r);return}var i;this._active=!0;do if(i=r.execute(r.state,r.delay))break;while(r=n.shift());if(this._active=!1,i){for(;r=n.shift();)r.unsubscribe();throw i}},t}($i),Ur=new xo(ko),$o=Ur,_o=new pe(function(e){return e.complete()});function _i(e){return e&&K(e.schedule)}function Si(e){return e[e.length-1]}function Ai(e){return _i(Si(e))?e.pop():void 0}function So(e,t){return typeof Si(e)=="number"?e.pop():t}var Hr=function(e){return e&&typeof e.length=="number"&&typeof e!="function"};function Ci(e){return K(e?.then)}function Mi(e){return K(e[Lr])}function Ei(e){return Symbol.asyncIterator&&K(e?.[Symbol.asyncIterator])}function Ti(e){return new TypeError("You provided "+(e!==null&&typeof e=="object"?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}function Ao(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Pi=Ao();function zi(e){return K(e?.[Pi])}function Fi(e){return ao(this,arguments,function(){var r,n,i,a;return pi(this,function(s){switch(s.label){case 0:r=e.getReader(),s.label=1;case 1:s.trys.push([1,,9,10]),s.label=2;case 2:return[4,ft(r.read())];case 3:return n=s.sent(),i=n.value,a=n.done,a?[4,ft(void 0)]:[3,5];case 4:return[2,s.sent()];case 5:return[4,ft(i)];case 6:return[4,s.sent()];case 7:return s.sent(),[3,2];case 8:return[3,10];case 9:return r.releaseLock(),[7];case 10:return[2]}})})}function Oi(e){return K(e?.getReader)}function He(e){if(e instanceof pe)return e;if(e!=null){if(Mi(e))return Co(e);if(Hr(e))return Mo(e);if(Ci(e))return Eo(e);if(Ei(e))return Ii(e);if(zi(e))return To(e);if(Oi(e))return Po(e)}throw Ti(e)}function Co(e){return new pe(function(t){var r=e[Lr]();if(K(r.subscribe))return r.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Mo(e){return new pe(function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()})}function Eo(e){return new pe(function(t){e.then(function(r){t.closed||(t.next(r),t.complete())},function(r){return t.error(r)}).then(null,bi)})}function To(e){return new pe(function(t){var r,n;try{for(var i=ht(e),a=i.next();!a.done;a=i.next()){var s=a.value;if(t.next(s),t.closed)return}}catch(o){r={error:o}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}t.complete()})}function Ii(e){return new pe(function(t){zo(e,t).catch(function(r){return t.error(r)})})}function Po(e){return Ii(Fi(e))}function zo(e,t){var r,n,i,a;return io(this,void 0,void 0,function(){var s,o;return pi(this,function(l){switch(l.label){case 0:l.trys.push([0,5,6,11]),r=so(e),l.label=1;case 1:return[4,r.next()];case 2:if(n=l.sent(),!!n.done)return[3,4];if(s=n.value,t.next(s),t.closed)return[2];l.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return o=l.sent(),i={error:o},[3,11];case 6:return l.trys.push([6,,9,10]),n&&!n.done&&(a=r.return)?[4,a.call(r)]:[3,8];case 7:l.sent(),l.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})}function et(e,t,r,n,i){n===void 0&&(n=0),i===void 0&&(i=!1);var a=t.schedule(function(){r(),i?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(a),!i)return a}function Ri(e,t){return t===void 0&&(t=0),mt(function(r,n){r.subscribe(Xe(n,function(i){return et(n,e,function(){return n.next(i)},t)},function(){return et(n,e,function(){return n.complete()},t)},function(i){return et(n,e,function(){return n.error(i)},t)}))})}function Di(e,t){return t===void 0&&(t=0),mt(function(r,n){n.add(e.schedule(function(){return r.subscribe(n)},t))})}function Fo(e,t){return He(e).pipe(Di(t),Ri(t))}function Oo(e,t){return He(e).pipe(Di(t),Ri(t))}function Io(e,t){return new pe(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})})}function Ro(e,t){return new pe(function(r){var n;return et(r,t,function(){n=e[Pi](),et(r,t,function(){var i,a,s;try{i=n.next(),a=i.value,s=i.done}catch(o){r.error(o);return}s?r.complete():r.next(a)},0,!0)}),function(){return K(n?.return)&&n.return()}})}function Bi(e,t){if(!e)throw new Error("Iterable cannot be null");return new pe(function(r){et(r,t,function(){var n=e[Symbol.asyncIterator]();et(r,t,function(){n.next().then(function(i){i.done?r.complete():r.next(i.value)})},0,!0)})})}function Do(e,t){return Bi(Fi(e),t)}function Bo(e,t){if(e!=null){if(Mi(e))return Fo(e,t);if(Hr(e))return Io(e,t);if(Ci(e))return Oo(e,t);if(Ei(e))return Bi(e,t);if(zi(e))return Ro(e,t);if(Oi(e))return Do(e,t)}throw Ti(e)}function ji(e,t){return t?Bo(e,t):He(e)}function jo(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Ai(e);return ji(e,r)}function Lo(e){return e instanceof Date&&!isNaN(e)}function qr(e,t){return mt(function(r,n){var i=0;r.subscribe(Xe(n,function(a){n.next(e.call(t,a,i++))}))})}var No=Array.isArray;function Uo(e,t){return No(t)?e.apply(void 0,Ot([],pt(t))):e(t)}function Ho(e){return qr(function(t){return Uo(e,t)})}function qo(e,t,r,n,i,a,s,o){var l=[],u=0,x=0,v=!1,$=function(){v&&!l.length&&!u&&t.complete()},y=function(z){return u<n?C(z):l.push(z)},C=function(z){u++;var H=!1;He(r(z,x++)).subscribe(Xe(t,function(X){t.next(X)},function(){H=!0},void 0,function(){if(H)try{u--;for(var X=function(){var p=l.shift();s||C(p)};l.length&&u<n;)X();$()}catch(p){t.error(p)}}))};return e.subscribe(Xe(t,y,function(){v=!0,$()})),function(){}}function Vr(e,t,r){return r===void 0&&(r=1/0),K(t)?Vr(function(n,i){return qr(function(a,s){return t(n,a,i,s)})(He(e(n,i)))},r):(typeof t=="number"&&(r=t),mt(function(n,i){return qo(n,i,e,r)}))}function Vo(e){return e===void 0&&(e=1/0),Vr(Nr,e)}var Wo=["addListener","removeListener"],Ko=["addEventListener","removeEventListener"],Zo=["on","off"];function It(e,t,r,n){if(K(r)&&(n=r,r=void 0),n)return It(e,t,r).pipe(Ho(n));var i=pt(Jo(e)?Ko.map(function(o){return function(l){return e[o](t,l,r)}}):Go(e)?Wo.map(Li(e,t)):Yo(e)?Zo.map(Li(e,t)):[],2),a=i[0],s=i[1];if(!a&&Hr(e))return Vr(function(o){return It(o,t,r)})(He(e));if(!a)throw new TypeError("Invalid event target");return new pe(function(o){var l=function(){for(var u=[],x=0;x<arguments.length;x++)u[x]=arguments[x];return o.next(1<u.length?u:u[0])};return a(l),function(){return s(l)}})}function Li(e,t){return function(r){return function(n){return e[r](t,n)}}}function Go(e){return K(e.addListener)&&K(e.removeListener)}function Yo(e){return K(e.on)&&K(e.off)}function Jo(e){return K(e.addEventListener)&&K(e.removeEventListener)}function Qo(e,t,r){r===void 0&&(r=$o);var n=-1;return _i(t)?r=t:n=t,new pe(function(i){var a=Lo(e)?100-r.now():e;a<0&&(a=0);var s=0;return r.schedule(function(){i.closed||(i.next(s++),0<=n?this.schedule(void 0,n):i.complete())},a)})}function Xo(e,t){return t===void 0&&(t=Ur),Qo(e,e,t)}function el(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Ai(e),n=So(e,1/0),i=e;return i.length?i.length===1?He(i[0]):Vo(n)(ji(i,r)):_o}function tl(e){return mt(function(t,r){var n=!1,i=null;t.subscribe(Xe(r,function(a){n=!0,i=a})),He(e).subscribe(Xe(r,function(){if(n){n=!1;var a=i;i=null,r.next(a)}},Dr))})}function rl(e,t){return t===void 0&&(t=Ur),tl(Xo(e,t))}function nl(e,t,r){var n=K(e)||t||r?{next:e,error:t,complete:r}:e;return n?mt(function(i,a){var s;(s=n.subscribe)===null||s===void 0||s.call(n);var o=!0;i.subscribe(Xe(a,function(l){var u;(u=n.next)===null||u===void 0||u.call(n,l),a.next(l)},function(){var l;o=!1,(l=n.complete)===null||l===void 0||l.call(n),a.complete()},function(l){var u;o=!1,(u=n.error)===null||u===void 0||u.call(n,l),a.error(l)},function(){var l,u;o&&((l=n.unsubscribe)===null||l===void 0||l.call(n)),(u=n.finalize)===null||u===void 0||u.call(n)}))}):Nr}function Ni(){const e="test";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch{return!1}}function Ae(e,t,r){return new CustomEvent(e,{detail:t,...r})}/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */let il=class{constructor(t,{target:r,config:n,callback:i,skipInitial:a}){this.t=new Set,this.o=!1,this.i=!1,this.h=t,r!==null&&this.t.add(r??t),this.l=n,this.o=a??this.o,this.callback=i,window.ResizeObserver?(this.u=new ResizeObserver(s=>{this.handleChanges(s),this.h.requestUpdate()}),t.addController(this)):console.warn("ResizeController error: browser does not support ResizeObserver.")}handleChanges(t){this.value=this.callback?.(t,this.u)}hostConnected(){for(const t of this.t)this.observe(t)}hostDisconnected(){this.disconnect()}async hostUpdated(){!this.o&&this.i&&this.handleChanges([]),this.i=!1}observe(t){this.t.add(t),this.u.observe(t,this.l),this.i=!0,this.h.requestUpdate()}unobserve(t){this.t.delete(t),this.u.unobserve(t)}disconnect(){this.u.disconnect()}};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Wr={ATTRIBUTE:1,CHILD:2},Kr=e=>(...t)=>({_$litDirective$:e,values:t});class Zr{constructor(t){}get _$AU(){return this._$AM._$AU}_$AT(t,r,n){this._$Ct=t,this._$AM=r,this._$Ci=n}_$AS(t,r){return this.update(t,r)}update(t,r){return this.render(...r)}}/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Gr=Kr(class extends Zr{constructor(e){if(super(e),e.type!==Wr.ATTRIBUTE||e.name!=="class"||e.strings?.length>2)throw Error("`classMap()` can only be used in the `class` attribute and must be the only part in the attribute.")}render(e){return" "+Object.keys(e).filter(t=>e[t]).join(" ")+" "}update(e,[t]){if(this.st===void 0){this.st=new Set,e.strings!==void 0&&(this.nt=new Set(e.strings.join(" ").split(/\s/).filter(n=>n!=="")));for(const n in t)t[n]&&!this.nt?.has(n)&&this.st.add(n);return this.render(t)}const r=e.element.classList;for(const n of this.st)n in t||(r.remove(n),this.st.delete(n));for(const n in t){const i=!!t[n];i===this.st.has(n)||this.nt?.has(n)||(i?(r.add(n),this.st.add(n)):(r.remove(n),this.st.delete(n)))}return Pe}});var Ui=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function al(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Yr={exports:{}},Hi;function sl(){return Hi||(Hi=1,function(e){var t=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{};/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */var r=function(n){var i=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,a=0,s={},o={manual:n.Prism&&n.Prism.manual,disableWorkerMessageHandler:n.Prism&&n.Prism.disableWorkerMessageHandler,util:{encode:function p(h){return h instanceof l?new l(h.type,p(h.content),h.alias):Array.isArray(h)?h.map(p):h.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(p){return Object.prototype.toString.call(p).slice(8,-1)},objId:function(p){return p.__id||Object.defineProperty(p,"__id",{value:++a}),p.__id},clone:function p(h,m){m=m||{};var d,g;switch(o.util.type(h)){case"Object":if(g=o.util.objId(h),m[g])return m[g];d={},m[g]=d;for(var _ in h)h.hasOwnProperty(_)&&(d[_]=p(h[_],m));return d;case"Array":return g=o.util.objId(h),m[g]?m[g]:(d=[],m[g]=d,h.forEach(function(O,E){d[E]=p(O,m)}),d);default:return h}},getLanguage:function(p){for(;p;){var h=i.exec(p.className);if(h)return h[1].toLowerCase();p=p.parentElement}return"none"},setLanguage:function(p,h){p.className=p.className.replace(RegExp(i,"gi"),""),p.classList.add("language-"+h)},currentScript:function(){if(typeof document>"u")return null;if(document.currentScript&&document.currentScript.tagName==="SCRIPT")return document.currentScript;try{throw new Error}catch(d){var p=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(d.stack)||[])[1];if(p){var h=document.getElementsByTagName("script");for(var m in h)if(h[m].src==p)return h[m]}return null}},isActive:function(p,h,m){for(var d="no-"+h;p;){var g=p.classList;if(g.contains(h))return!0;if(g.contains(d))return!1;p=p.parentElement}return!!m}},languages:{plain:s,plaintext:s,text:s,txt:s,extend:function(p,h){var m=o.util.clone(o.languages[p]);for(var d in h)m[d]=h[d];return m},insertBefore:function(p,h,m,d){d=d||o.languages;var g=d[p],_={};for(var O in g)if(g.hasOwnProperty(O)){if(O==h)for(var E in m)m.hasOwnProperty(E)&&(_[E]=m[E]);m.hasOwnProperty(O)||(_[O]=g[O])}var Z=d[p];return d[p]=_,o.languages.DFS(o.languages,function(q,ie){ie===Z&&q!=p&&(this[q]=_)}),_},DFS:function p(h,m,d,g){g=g||{};var _=o.util.objId;for(var O in h)if(h.hasOwnProperty(O)){m.call(h,O,h[O],d||O);var E=h[O],Z=o.util.type(E);Z==="Object"&&!g[_(E)]?(g[_(E)]=!0,p(E,m,null,g)):Z==="Array"&&!g[_(E)]&&(g[_(E)]=!0,p(E,m,O,g))}}},plugins:{},highlightAll:function(p,h){o.highlightAllUnder(document,p,h)},highlightAllUnder:function(p,h,m){var d={callback:m,container:p,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};o.hooks.run("before-highlightall",d),d.elements=Array.prototype.slice.apply(d.container.querySelectorAll(d.selector)),o.hooks.run("before-all-elements-highlight",d);for(var g=0,_;_=d.elements[g++];)o.highlightElement(_,h===!0,d.callback)},highlightElement:function(p,h,m){var d=o.util.getLanguage(p),g=o.languages[d];o.util.setLanguage(p,d);var _=p.parentElement;_&&_.nodeName.toLowerCase()==="pre"&&o.util.setLanguage(_,d);var O=p.textContent,E={element:p,language:d,grammar:g,code:O};function Z(ie){E.highlightedCode=ie,o.hooks.run("before-insert",E),E.element.innerHTML=E.highlightedCode,o.hooks.run("after-highlight",E),o.hooks.run("complete",E),m&&m.call(E.element)}if(o.hooks.run("before-sanity-check",E),_=E.element.parentElement,_&&_.nodeName.toLowerCase()==="pre"&&!_.hasAttribute("tabindex")&&_.setAttribute("tabindex","0"),!E.code){o.hooks.run("complete",E),m&&m.call(E.element);return}if(o.hooks.run("before-highlight",E),!E.grammar){Z(o.util.encode(E.code));return}if(h&&n.Worker){var q=new Worker(o.filename);q.onmessage=function(ie){Z(ie.data)},q.postMessage(JSON.stringify({language:E.language,code:E.code,immediateClose:!0}))}else Z(o.highlight(E.code,E.grammar,E.language))},highlight:function(p,h,m){var d={code:p,grammar:h,language:m};if(o.hooks.run("before-tokenize",d),!d.grammar)throw new Error('The language "'+d.language+'" has no grammar.');return d.tokens=o.tokenize(d.code,d.grammar),o.hooks.run("after-tokenize",d),l.stringify(o.util.encode(d.tokens),d.language)},tokenize:function(p,h){var m=h.rest;if(m){for(var d in m)h[d]=m[d];delete h.rest}var g=new v;return $(g,g.head,p),x(p,g,h,g.head,0),C(g)},hooks:{all:{},add:function(p,h){var m=o.hooks.all;m[p]=m[p]||[],m[p].push(h)},run:function(p,h){var m=o.hooks.all[p];if(!(!m||!m.length))for(var d=0,g;g=m[d++];)g(h)}},Token:l};n.Prism=o;function l(p,h,m,d){this.type=p,this.content=h,this.alias=m,this.length=(d||"").length|0}l.stringify=function p(h,m){if(typeof h=="string")return h;if(Array.isArray(h)){var d="";return h.forEach(function(Z){d+=p(Z,m)}),d}var g={type:h.type,content:p(h.content,m),tag:"span",classes:["token",h.type],attributes:{},language:m},_=h.alias;_&&(Array.isArray(_)?Array.prototype.push.apply(g.classes,_):g.classes.push(_)),o.hooks.run("wrap",g);var O="";for(var E in g.attributes)O+=" "+E+'="'+(g.attributes[E]||"").replace(/"/g,"&quot;")+'"';return"<"+g.tag+' class="'+g.classes.join(" ")+'"'+O+">"+g.content+"</"+g.tag+">"};function u(p,h,m,d){p.lastIndex=h;var g=p.exec(m);if(g&&d&&g[1]){var _=g[1].length;g.index+=_,g[0]=g[0].slice(_)}return g}function x(p,h,m,d,g,_){for(var O in m)if(!(!m.hasOwnProperty(O)||!m[O])){var E=m[O];E=Array.isArray(E)?E:[E];for(var Z=0;Z<E.length;++Z){if(_&&_.cause==O+","+Z)return;var q=E[Z],ie=q.inside,Ee=!!q.lookbehind,ke=!!q.greedy,Be=q.alias;if(ke&&!q.pattern.global){var xe=q.pattern.toString().match(/[imsuy]*$/)[0];q.pattern=RegExp(q.pattern.source,xe+"g")}for(var je=q.pattern||q,B=d.next,G=g;B!==h.tail&&!(_&&G>=_.reach);G+=B.value.length,B=B.next){var j=B.value;if(h.length>p.length)return;if(!(j instanceof l)){var ce=1,ue;if(ke){if(ue=u(je,G,p,Ee),!ue||ue.index>=p.length)break;var w=ue.index,At=ue.index+ue[0].length,f=G;for(f+=B.value.length;w>=f;)B=B.next,f+=B.value.length;if(f-=B.value.length,G=f,B.value instanceof l)continue;for(var c=B;c!==h.tail&&(f<At||typeof c.value=="string");c=c.next)ce++,f+=c.value.length;ce--,j=p.slice(G,f),ue.index-=G}else if(ue=u(je,0,j,Ee),!ue)continue;var w=ue.index,A=ue[0],M=j.slice(0,w),S=j.slice(w+A.length),k=G+j.length;_&&k>_.reach&&(_.reach=k);var F=B.prev;M&&(F=$(h,F,M),G+=M.length),y(h,F,ce);var T=new l(O,ie?o.tokenize(A,ie):A,Be,A);if(B=$(h,F,T),S&&$(h,B,S),ce>1){var R={cause:O+","+Z,reach:k};x(p,h,m,B.prev,G,R),_&&R.reach>_.reach&&(_.reach=R.reach)}}}}}}function v(){var p={value:null,prev:null,next:null},h={value:null,prev:p,next:null};p.next=h,this.head=p,this.tail=h,this.length=0}function $(p,h,m){var d=h.next,g={value:m,prev:h,next:d};return h.next=g,d.prev=g,p.length++,g}function y(p,h,m){for(var d=h.next,g=0;g<m&&d!==p.tail;g++)d=d.next;h.next=d,d.prev=h,p.length-=g}function C(p){for(var h=[],m=p.head.next;m!==p.tail;)h.push(m.value),m=m.next;return h}if(!n.document)return n.addEventListener&&(o.disableWorkerMessageHandler||n.addEventListener("message",function(p){var h=JSON.parse(p.data),m=h.language,d=h.code,g=h.immediateClose;n.postMessage(o.highlight(d,o.languages[m],m)),g&&n.close()},!1)),o;var z=o.util.currentScript();z&&(o.filename=z.src,z.hasAttribute("data-manual")&&(o.manual=!0));function H(){o.manual||o.highlightAll()}if(!o.manual){var X=document.readyState;X==="loading"||X==="interactive"&&z&&z.defer?document.addEventListener("DOMContentLoaded",H):window.requestAnimationFrame?window.requestAnimationFrame(H):window.setTimeout(H,16)}return o}(t);e.exports&&(e.exports=r),typeof Ui<"u"&&(Ui.Prism=r)}(Yr)),Yr.exports}var qi=sl();Prism.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},Prism.languages.javascript=Prism.languages.extend("clike",{"class-name":[Prism.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source)+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),Prism.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,Prism.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:Prism.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:Prism.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),Prism.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:Prism.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),Prism.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),Prism.languages.markup&&(Prism.languages.markup.tag.addInlined("script","javascript"),Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),Prism.languages.js=Prism.languages.javascript;var Vi={},Wi;function ol(){return Wi||(Wi=1,function(e){e.languages.typescript=e.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),e.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete e.languages.typescript.parameter,delete e.languages.typescript["literal-property"];var t=e.languages.extend("typescript",{});delete t["class-name"],e.languages.typescript["class-name"].inside=t,e.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:t}}}}),e.languages.ts=e.languages.typescript}(Prism)),Vi}ol();var Ki={},Zi;function ll(){return Zi||(Zi=1,function(e){function t(G,j){return G.replace(/<<(\d+)>>/g,function(ce,ue){return"(?:"+j[+ue]+")"})}function r(G,j,ce){return RegExp(t(G,j),"")}function n(G,j){for(var ce=0;ce<j;ce++)G=G.replace(/<<self>>/g,function(){return"(?:"+G+")"});return G.replace(/<<self>>/g,"[^\\s\\S]")}var i={type:"bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void",typeDeclaration:"class enum interface record struct",contextual:"add alias and ascending async await by descending from(?=\\s*(?:\\w|$)) get global group into init(?=\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\s*{)",other:"abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield"};function a(G){return"\\b(?:"+G.trim().replace(/ /g,"|")+")\\b"}var s=a(i.typeDeclaration),o=RegExp(a(i.type+" "+i.typeDeclaration+" "+i.contextual+" "+i.other)),l=a(i.typeDeclaration+" "+i.contextual+" "+i.other),u=a(i.type+" "+i.typeDeclaration+" "+i.other),x=n(/<(?:[^<>;=+\-*/%&|^]|<<self>>)*>/.source,2),v=n(/\((?:[^()]|<<self>>)*\)/.source,2),$=/@?\b[A-Za-z_]\w*\b/.source,y=t(/<<0>>(?:\s*<<1>>)?/.source,[$,x]),C=t(/(?!<<0>>)<<1>>(?:\s*\.\s*<<1>>)*/.source,[l,y]),z=/\[\s*(?:,\s*)*\]/.source,H=t(/<<0>>(?:\s*(?:\?\s*)?<<1>>)*(?:\s*\?)?/.source,[C,z]),X=t(/[^,()<>[\];=+\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source,[x,v,z]),p=t(/\(<<0>>+(?:,<<0>>+)+\)/.source,[X]),h=t(/(?:<<0>>|<<1>>)(?:\s*(?:\?\s*)?<<2>>)*(?:\s*\?)?/.source,[p,C,z]),m={keyword:o,punctuation:/[<>()?,.:[\]]/},d=/'(?:[^\r\n'\\]|\\.|\\[Uux][\da-fA-F]{1,8})'/.source,g=/"(?:\\.|[^\\"\r\n])*"/.source,_=/@"(?:""|\\[\s\S]|[^\\"])*"(?!")/.source;e.languages.csharp=e.languages.extend("clike",{string:[{pattern:r(/(^|[^$\\])<<0>>/.source,[_]),lookbehind:!0,greedy:!0},{pattern:r(/(^|[^@$\\])<<0>>/.source,[g]),lookbehind:!0,greedy:!0}],"class-name":[{pattern:r(/(\busing\s+static\s+)<<0>>(?=\s*;)/.source,[C]),lookbehind:!0,inside:m},{pattern:r(/(\busing\s+<<0>>\s*=\s*)<<1>>(?=\s*;)/.source,[$,h]),lookbehind:!0,inside:m},{pattern:r(/(\busing\s+)<<0>>(?=\s*=)/.source,[$]),lookbehind:!0},{pattern:r(/(\b<<0>>\s+)<<1>>/.source,[s,y]),lookbehind:!0,inside:m},{pattern:r(/(\bcatch\s*\(\s*)<<0>>/.source,[C]),lookbehind:!0,inside:m},{pattern:r(/(\bwhere\s+)<<0>>/.source,[$]),lookbehind:!0},{pattern:r(/(\b(?:is(?:\s+not)?|as)\s+)<<0>>/.source,[H]),lookbehind:!0,inside:m},{pattern:r(/\b<<0>>(?=\s+(?!<<1>>|with\s*\{)<<2>>(?:\s*[=,;:{)\]]|\s+(?:in|when)\b))/.source,[h,u,$]),inside:m}],keyword:o,number:/(?:\b0(?:x[\da-f_]*[\da-f]|b[01_]*[01])|(?:\B\.\d+(?:_+\d+)*|\b\d+(?:_+\d+)*(?:\.\d+(?:_+\d+)*)?)(?:e[-+]?\d+(?:_+\d+)*)?)(?:[dflmu]|lu|ul)?\b/i,operator:/>>=?|<<=?|[-=]>|([-+&|])\1|~|\?\?=?|[-+*/%&|^!=<>]=?/,punctuation:/\?\.?|::|[{}[\];(),.:]/}),e.languages.insertBefore("csharp","number",{range:{pattern:/\.\./,alias:"operator"}}),e.languages.insertBefore("csharp","punctuation",{"named-parameter":{pattern:r(/([(,]\s*)<<0>>(?=\s*:)/.source,[$]),lookbehind:!0,alias:"punctuation"}}),e.languages.insertBefore("csharp","class-name",{namespace:{pattern:r(/(\b(?:namespace|using)\s+)<<0>>(?:\s*\.\s*<<0>>)*(?=\s*[;{])/.source,[$]),lookbehind:!0,inside:{punctuation:/\./}},"type-expression":{pattern:r(/(\b(?:default|sizeof|typeof)\s*\(\s*(?!\s))(?:[^()\s]|\s(?!\s)|<<0>>)*(?=\s*\))/.source,[v]),lookbehind:!0,alias:"class-name",inside:m},"return-type":{pattern:r(/<<0>>(?=\s+(?:<<1>>\s*(?:=>|[({]|\.\s*this\s*\[)|this\s*\[))/.source,[h,C]),inside:m,alias:"class-name"},"constructor-invocation":{pattern:r(/(\bnew\s+)<<0>>(?=\s*[[({])/.source,[h]),lookbehind:!0,inside:m,alias:"class-name"},"generic-method":{pattern:r(/<<0>>\s*<<1>>(?=\s*\()/.source,[$,x]),inside:{function:r(/^<<0>>/.source,[$]),generic:{pattern:RegExp(x),alias:"class-name",inside:m}}},"type-list":{pattern:r(/\b((?:<<0>>\s+<<1>>|record\s+<<1>>\s*<<5>>|where\s+<<2>>)\s*:\s*)(?:<<3>>|<<4>>|<<1>>\s*<<5>>|<<6>>)(?:\s*,\s*(?:<<3>>|<<4>>|<<6>>))*(?=\s*(?:where|[{;]|=>|$))/.source,[s,y,$,h,o.source,v,/\bnew\s*\(\s*\)/.source]),lookbehind:!0,inside:{"record-arguments":{pattern:r(/(^(?!new\s*\()<<0>>\s*)<<1>>/.source,[y,v]),lookbehind:!0,greedy:!0,inside:e.languages.csharp},keyword:o,"class-name":{pattern:RegExp(h),greedy:!0,inside:m},punctuation:/[,()]/}},preprocessor:{pattern:/(^[\t ]*)#.*/m,lookbehind:!0,alias:"property",inside:{directive:{pattern:/(#)\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\b/,lookbehind:!0,alias:"keyword"}}}});var O=g+"|"+d,E=t(/\/(?![*/])|\/\/[^\r\n]*[\r\n]|\/\*(?:[^*]|\*(?!\/))*\*\/|<<0>>/.source,[O]),Z=n(t(/[^"'/()]|<<0>>|\(<<self>>*\)/.source,[E]),2),q=/\b(?:assembly|event|field|method|module|param|property|return|type)\b/.source,ie=t(/<<0>>(?:\s*\(<<1>>*\))?/.source,[C,Z]);e.languages.insertBefore("csharp","class-name",{attribute:{pattern:r(/((?:^|[^\s\w>)?])\s*\[\s*)(?:<<0>>\s*:\s*)?<<1>>(?:\s*,\s*<<1>>)*(?=\s*\])/.source,[q,ie]),lookbehind:!0,greedy:!0,inside:{target:{pattern:r(/^<<0>>(?=\s*:)/.source,[q]),alias:"keyword"},"attribute-arguments":{pattern:r(/\(<<0>>*\)/.source,[Z]),inside:e.languages.csharp},"class-name":{pattern:RegExp(C),inside:{punctuation:/\./}},punctuation:/[:,]/}}});var Ee=/:[^}\r\n]+/.source,ke=n(t(/[^"'/()]|<<0>>|\(<<self>>*\)/.source,[E]),2),Be=t(/\{(?!\{)(?:(?![}:])<<0>>)*<<1>>?\}/.source,[ke,Ee]),xe=n(t(/[^"'/()]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|<<0>>|\(<<self>>*\)/.source,[O]),2),je=t(/\{(?!\{)(?:(?![}:])<<0>>)*<<1>>?\}/.source,[xe,Ee]);function B(G,j){return{interpolation:{pattern:r(/((?:^|[^{])(?:\{\{)*)<<0>>/.source,[G]),lookbehind:!0,inside:{"format-string":{pattern:r(/(^\{(?:(?![}:])<<0>>)*)<<1>>(?=\}$)/.source,[j,Ee]),lookbehind:!0,inside:{punctuation:/^:/}},punctuation:/^\{|\}$/,expression:{pattern:/[\s\S]+/,alias:"language-csharp",inside:e.languages.csharp}}},string:/[\s\S]+/}}e.languages.insertBefore("csharp","string",{"interpolation-string":[{pattern:r(/(^|[^\\])(?:\$@|@\$)"(?:""|\\[\s\S]|\{\{|<<0>>|[^\\{"])*"/.source,[Be]),lookbehind:!0,greedy:!0,inside:B(Be,ke)},{pattern:r(/(^|[^@\\])\$"(?:\\.|\{\{|<<0>>|[^\\"{])*"/.source,[je]),lookbehind:!0,greedy:!0,inside:B(je,xe)}],char:{pattern:RegExp(d),greedy:!0}}),e.languages.dotnet=e.languages.cs=e.languages.csharp}(Prism)),Ki}ll();var Gi={},Yi;function cl(){return Yi||(Yi=1,function(e){var t=/\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\s*[(){}[\]<>=%~.:,;?+\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\b/,r=/(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/.source,n={pattern:RegExp(/(^|[^\w.])/.source+r+/[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),lookbehind:!0,inside:{namespace:{pattern:/^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,inside:{punctuation:/\./}},punctuation:/\./}};e.languages.java=e.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"/,lookbehind:!0,greedy:!0},"class-name":[n,{pattern:RegExp(/(^|[^\w.])/.source+r+/[A-Z]\w*(?=\s+\w+\s*[;,=()]|\s*(?:\[[\s,]*\]\s*)?::\s*new\b)/.source),lookbehind:!0,inside:n.inside},{pattern:RegExp(/(\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\s+)/.source+r+/[A-Z]\w*\b/.source),lookbehind:!0,inside:n.inside}],keyword:t,function:[e.languages.clike.function,{pattern:/(::\s*)[a-z_]\w*/,lookbehind:!0}],number:/\b0b[01][01_]*L?\b|\b0x(?:\.[\da-f_p+-]+|[\da-f_]+(?:\.[\da-f_p+-]+)?)\b|(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?\d[\d_]*)?[dfl]?/i,operator:{pattern:/(^|[^.])(?:<<=?|>>>?=?|->|--|\+\+|&&|\|\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,lookbehind:!0},constant:/\b[A-Z][A-Z_\d]+\b/}),e.languages.insertBefore("java","string",{"triple-quoted-string":{pattern:/"""[ \t]*[\r\n](?:(?:"|"")?(?:\\.|[^"\\]))*"""/,greedy:!0,alias:"string"},char:{pattern:/'(?:\\.|[^'\\\r\n]){1,6}'/,greedy:!0}}),e.languages.insertBefore("java","class-name",{annotation:{pattern:/(^|[^.])@\w+(?:\s*\.\s*\w+)*/,lookbehind:!0,alias:"punctuation"},generics:{pattern:/<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&))*>)*>)*>)*>/,inside:{"class-name":n,keyword:t,punctuation:/[<>(),.:]/,operator:/[?&|]/}},import:[{pattern:RegExp(/(\bimport\s+)/.source+r+/(?:[A-Z]\w*|\*)(?=\s*;)/.source),lookbehind:!0,inside:{namespace:n.inside.namespace,punctuation:/\./,operator:/\*/,"class-name":/\w+/}},{pattern:RegExp(/(\bimport\s+static\s+)/.source+r+/(?:\w+|\*)(?=\s*;)/.source),lookbehind:!0,alias:"static",inside:{namespace:n.inside.namespace,static:/\b\w+$/,punctuation:/\./,operator:/\*/,"class-name":/\w+/}}],namespace:{pattern:RegExp(/(\b(?:exports|import(?:\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\s+)(?!<keyword>)[a-z]\w*(?:\.[a-z]\w*)*\.?/.source.replace(/<keyword>/g,function(){return t.source})),lookbehind:!0,inside:{punctuation:/\./}}})}(Prism)),Gi}cl(),Prism.languages.scala=Prism.languages.extend("java",{"triple-quoted-string":{pattern:/"""[\s\S]*?"""/,greedy:!0,alias:"string"},string:{pattern:/("|')(?:\\.|(?!\1)[^\\\r\n])*\1/,greedy:!0},keyword:/<-|=>|\b(?:abstract|case|catch|class|def|derives|do|else|enum|extends|extension|final|finally|for|forSome|given|if|implicit|import|infix|inline|lazy|match|new|null|object|opaque|open|override|package|private|protected|return|sealed|self|super|this|throw|trait|transparent|try|type|using|val|var|while|with|yield)\b/,number:/\b0x(?:[\da-f]*\.)?[\da-f]+|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e\d+)?[dfl]?/i,builtin:/\b(?:Any|AnyRef|AnyVal|Boolean|Byte|Char|Double|Float|Int|Long|Nothing|Short|String|Unit)\b/,symbol:/'[^\d\s\\]\w*/}),Prism.languages.insertBefore("scala","triple-quoted-string",{"string-interpolation":{pattern:/\b[a-z]\w*(?:"""(?:[^$]|\$(?:[^{]|\{(?:[^{}]|\{[^{}]*\})*\}))*?"""|"(?:[^$"\r\n]|\$(?:[^{]|\{(?:[^{}]|\{[^{}]*\})*\}))*")/i,greedy:!0,inside:{id:{pattern:/^\w+/,greedy:!0,alias:"function"},escape:{pattern:/\\\$"|\$[$"]/,greedy:!0,alias:"symbol"},interpolation:{pattern:/\$(?:\w+|\{(?:[^{}]|\{[^{}]*\})*\})/,greedy:!0,inside:{punctuation:/^\$\{?|\}$/,expression:{pattern:/[\s\S]+/,inside:Prism.languages.scala}}},string:/[\s\S]+/}}}),delete Prism.languages.scala["class-name"],delete Prism.languages.scala.function,delete Prism.languages.scala.constant,function(e){var t=/(?:\r?\n|\r)[ \t]*\|.+\|(?:(?!\|).)*/.source;e.languages.gherkin={pystring:{pattern:/("""|''')[\s\S]+?\1/,alias:"string"},comment:{pattern:/(^[ \t]*)#.*/m,lookbehind:!0},tag:{pattern:/(^[ \t]*)@\S*/m,lookbehind:!0},feature:{pattern:/((?:^|\r?\n|\r)[ \t]*)(?:Ability|Ahoy matey!|Arwedd|Aspekt|Besigheid Behoefte|Business Need|Caracteristica|Característica|Egenskab|Egenskap|Eiginleiki|Feature|Fīča|Fitur|Fonctionnalité|Fonksyonalite|Funcionalidade|Funcionalitat|Functionalitate|Funcţionalitate|Funcționalitate|Functionaliteit|Fungsi|Funkcia|Funkcija|Funkcionalitāte|Funkcionalnost|Funkcja|Funksie|Funktionalität|Funktionalitéit|Funzionalità|Hwaet|Hwæt|Jellemző|Karakteristik|Lastnost|Mak|Mogucnost|laH|Mogućnost|Moznosti|Možnosti|OH HAI|Omadus|Ominaisuus|Osobina|Özellik|Potrzeba biznesowa|perbogh|poQbogh malja'|Požadavek|Požiadavka|Pretty much|Qap|Qu'meH 'ut|Savybė|Tính năng|Trajto|Vermoë|Vlastnosť|Właściwość|Značilnost|Δυνατότητα|Λειτουργία|Могућност|Мөмкинлек|Особина|Свойство|Үзенчәлеклелек|Функционал|Функционалност|Функция|Функціонал|תכונה|خاصية|خصوصیت|صلاحیت|کاروبار کی ضرورت|وِیژگی|रूप लेख|ਖਾਸੀਅਤ|ਨਕਸ਼ ਨੁਹਾਰ|ਮੁਹਾਂਦਰਾ|గుణము|ಹೆಚ್ಚಳ|ความต้องการทางธุรกิจ|ความสามารถ|โครงหลัก|기능|フィーチャ|功能|機能):(?:[^:\r\n]+(?:\r?\n|\r|$))*/,lookbehind:!0,inside:{important:{pattern:/(:)[^\r\n]+/,lookbehind:!0},keyword:/[^:\r\n]+:/}},scenario:{pattern:/(^[ \t]*)(?:Abstract Scenario|Abstrakt Scenario|Achtergrond|Aer|Ær|Agtergrond|All y'all|Antecedentes|Antecedents|Atburðarás|Atburðarásir|Awww, look mate|B4|Background|Baggrund|Bakgrund|Bakgrunn|Bakgrunnur|Beispiele|Beispiller|Bối cảnh|Cefndir|Cenario|Cenário|Cenario de Fundo|Cenário de Fundo|Cenarios|Cenários|Contesto|Context|Contexte|Contexto|Conto|Contoh|Contone|Dæmi|Dasar|Dead men tell no tales|Delineacao do Cenario|Delineação do Cenário|Dis is what went down|Dữ liệu|Dyagram Senaryo|Dyagram senaryo|Egzanp|Ejemplos|Eksempler|Ekzemploj|Enghreifftiau|Esbozo do escenario|Escenari|Escenario|Esempi|Esquema de l'escenari|Esquema del escenario|Esquema do Cenario|Esquema do Cenário|EXAMPLZ|Examples|Exempel|Exemple|Exemples|Exemplos|First off|Fono|Forgatókönyv|Forgatókönyv vázlat|Fundo|Geçmiş|Grundlage|Hannergrond|ghantoH|Háttér|Heave to|Istorik|Juhtumid|Keadaan|Khung kịch bản|Khung tình huống|Kịch bản|Koncept|Konsep skenario|Kontèks|Kontekst|Kontekstas|Konteksts|Kontext|Konturo de la scenaro|Latar Belakang|lut chovnatlh|lut|lutmey|Lýsing Atburðarásar|Lýsing Dæma|MISHUN SRSLY|MISHUN|Menggariskan Senario|mo'|Náčrt Scenára|Náčrt Scénáře|Náčrt Scenáru|Oris scenarija|Örnekler|Osnova|Osnova Scenára|Osnova scénáře|Osnutek|Ozadje|Paraugs|Pavyzdžiai|Példák|Piemēri|Plan du scénario|Plan du Scénario|Plan Senaryo|Plan senaryo|Plang vum Szenario|Pozadí|Pozadie|Pozadina|Príklady|Příklady|Primer|Primeri|Primjeri|Przykłady|Raamstsenaarium|Reckon it's like|Rerefons|Scenár|Scénář|Scenarie|Scenarij|Scenarijai|Scenarijaus šablonas|Scenariji|Scenārijs|Scenārijs pēc parauga|Scenarijus|Scenario|Scénario|Scenario Amlinellol|Scenario Outline|Scenario Template|Scenariomal|Scenariomall|Scenarios|Scenariu|Scenariusz|Scenaro|Schema dello scenario|Se ðe|Se the|Se þe|Senario|Senaryo Deskripsyon|Senaryo deskripsyon|Senaryo|Senaryo taslağı|Shiver me timbers|Situācija|Situai|Situasie Uiteensetting|Situasie|Skenario konsep|Skenario|Skica|Structura scenariu|Structură scenariu|Struktura scenarija|Stsenaarium|Swa hwaer swa|Swa|Swa hwær swa|Szablon scenariusza|Szenario|Szenariogrundriss|Tapaukset|Tapaus|Tapausaihio|Taust|Tausta|Template Keadaan|Template Senario|Template Situai|The thing of it is|Tình huống|Variantai|Voorbeelde|Voorbeelden|Wharrimean is|Yo-ho-ho|You'll wanna|Założenia|Παραδείγματα|Περιγραφή Σεναρίου|Σενάρια|Σενάριο|Υπόβαθρο|Кереш|Контекст|Концепт|Мисаллар|Мисоллар|Основа|Передумова|Позадина|Предистория|Предыстория|Приклади|Пример|Примери|Примеры|Рамка на сценарий|Скица|Структура сценарија|Структура сценария|Структура сценарію|Сценарий|Сценарий структураси|Сценарийның төзелеше|Сценарији|Сценарио|Сценарій|Тарих|Үрнәкләр|דוגמאות|רקע|תבנית תרחיש|תרחיש|الخلفية|الگوی سناریو|امثلة|پس منظر|زمینه|سناریو|سيناريو|سيناريو مخطط|مثالیں|منظر نامے کا خاکہ|منظرنامہ|نمونه ها|उदाहरण|परिदृश्य|परिदृश्य रूपरेखा|पृष्ठभूमि|ਉਦਾਹਰਨਾਂ|ਪਟਕਥਾ|ਪਟਕਥਾ ਢਾਂਚਾ|ਪਟਕਥਾ ਰੂਪ ਰੇਖਾ|ਪਿਛੋਕੜ|ఉదాహరణలు|కథనం|నేపథ్యం|సన్నివేశం|ಉದಾಹರಣೆಗಳು|ಕಥಾಸಾರಾಂಶ|ವಿವರಣೆ|ಹಿನ್ನೆಲೆ|โครงสร้างของเหตุการณ์|ชุดของตัวอย่าง|ชุดของเหตุการณ์|แนวคิด|สรุปเหตุการณ์|เหตุการณ์|배경|시나리오|시나리오 개요|예|サンプル|シナリオ|シナリオアウトライン|シナリオテンプレ|シナリオテンプレート|テンプレ|例|例子|剧本|剧本大纲|劇本|劇本大綱|场景|场景大纲|場景|場景大綱|背景):[^:\r\n]*/m,lookbehind:!0,inside:{important:{pattern:/(:)[^\r\n]*/,lookbehind:!0},keyword:/[^:\r\n]+:/}},"table-body":{pattern:RegExp("("+t+")(?:"+t+")+"),lookbehind:!0,inside:{outline:{pattern:/<[^>]+>/,alias:"variable"},td:{pattern:/\s*[^\s|][^|]*/,alias:"string"},punctuation:/\|/}},"table-head":{pattern:RegExp(t),inside:{th:{pattern:/\s*[^\s|][^|]*/,alias:"variable"},punctuation:/\|/}},atrule:{pattern:/(^[ \t]+)(?:'a|'ach|'ej|7|a|A také|A taktiež|A tiež|A zároveň|Aber|Ac|Adott|Akkor|Ak|Aleshores|Ale|Ali|Allora|Alors|Als|Ama|Amennyiben|Amikor|Ampak|an|AN|Ananging|And y'all|And|Angenommen|Anrhegedig a|An|Apabila|Atès|Atesa|Atunci|Avast!|Aye|A|awer|Bagi|Banjur|Bet|Biết|Blimey!|Buh|But at the end of the day I reckon|But y'all|But|BUT|Cal|Când|Cand|Cando|Ce|Cuando|Če|Ða ðe|Ða|Dadas|Dada|Dados|Dado|DaH ghu' bejlu'|dann|Dann|Dano|Dan|Dar|Dat fiind|Data|Date fiind|Date|Dati fiind|Dati|Daţi fiind|Dați fiind|DEN|Dato|De|Den youse gotta|Dengan|Diberi|Diyelim ki|Donada|Donat|Donitaĵo|Do|Dun|Duota|Ðurh|Eeldades|Ef|Eğer ki|Entao|Então|Entón|E|En|Entonces|Epi|És|Etant donnée|Etant donné|Et|Étant données|Étant donnée|Étant donné|Etant données|Etant donnés|Étant donnés|Fakat|Gangway!|Gdy|Gegeben seien|Gegeben sei|Gegeven|Gegewe|ghu' noblu'|Gitt|Given y'all|Given|Givet|Givun|Ha|Cho|I CAN HAZ|In|Ir|It's just unbelievable|I|Ja|Jeśli|Jeżeli|Kad|Kada|Kadar|Kai|Kaj|Když|Keď|Kemudian|Ketika|Khi|Kiedy|Ko|Kuid|Kui|Kun|Lan|latlh|Le sa a|Let go and haul|Le|Lè sa a|Lè|Logo|Lorsqu'<|Lorsque|mä|Maar|Mais|Mając|Ma|Majd|Maka|Manawa|Mas|Men|Menawa|Mutta|Nalika|Nalikaning|Nanging|Når|När|Nato|Nhưng|Niin|Njuk|O zaman|Och|Og|Oletetaan|Ond|Onda|Oraz|Pak|Pero|Però|Podano|Pokiaľ|Pokud|Potem|Potom|Privzeto|Pryd|Quan|Quand|Quando|qaSDI'|Så|Sed|Se|Siis|Sipoze ke|Sipoze Ke|Sipoze|Si|Şi|Și|Soit|Stel|Tada|Tad|Takrat|Tak|Tapi|Ter|Tetapi|Tha the|Tha|Then y'all|Then|Thì|Thurh|Toda|Too right|Un|Und|ugeholl|Và|vaj|Vendar|Ve|wann|Wanneer|WEN|Wenn|When y'all|When|Wtedy|Wun|Y'know|Yeah nah|Yna|Youse know like when|Youse know when youse got|Y|Za predpokladu|Za předpokladu|Zadan|Zadani|Zadano|Zadate|Zadato|Zakładając|Zaradi|Zatati|Þa þe|Þa|Þá|Þegar|Þurh|Αλλά|Δεδομένου|Και|Όταν|Τότε|А також|Агар|Але|Али|Аммо|А|Әгәр|Әйтик|Әмма|Бирок|Ва|Вә|Дадено|Дано|Допустим|Если|Задате|Задати|Задато|И|І|К тому же|Када|Кад|Когато|Когда|Коли|Ләкин|Лекин|Нәтиҗәдә|Нехай|Но|Онда|Припустимо, що|Припустимо|Пусть|Также|Та|Тогда|Тоді|То|Унда|Һәм|Якщо|אבל|אזי|אז|בהינתן|וגם|כאשר|آنگاه|اذاً|اگر|اما|اور|با فرض|بالفرض|بفرض|پھر|تب|ثم|جب|عندما|فرض کیا|لكن|لیکن|متى|هنگامی|و|अगर|और|कदा|किन्तु|चूंकि|जब|तथा|तदा|तब|परन्तु|पर|यदि|ਅਤੇ|ਜਦੋਂ|ਜਿਵੇਂ ਕਿ|ਜੇਕਰ|ਤਦ|ਪਰ|అప్పుడు|ఈ పరిస్థితిలో|కాని|చెప్పబడినది|మరియు|ಆದರೆ|ನಂತರ|ನೀಡಿದ|ಮತ್ತು|ಸ್ಥಿತಿಯನ್ನು|กำหนดให้|ดังนั้น|แต่|เมื่อ|และ|그러면<|그리고<|단<|만약<|만일<|먼저<|조건<|하지만<|かつ<|しかし<|ただし<|ならば<|もし<|並且<|但し<|但是<|假如<|假定<|假設<|假设<|前提<|同时<|同時<|并且<|当<|當<|而且<|那么<|那麼<)(?=[ \t])/m,lookbehind:!0},string:{pattern:/"(?:\\.|[^"\\\r\n])*"|'(?:\\.|[^'\\\r\n])*'/,inside:{outline:{pattern:/<[^>]+>/,alias:"variable"}}},outline:{pattern:/<[^>]+>/,alias:"variable"}}}(Prism);var Ji={},Qi;function ul(){return Qi||(Qi=1,function(e){for(var t=/\/\*(?:[^*/]|\*(?!\/)|\/(?!\*)|<self>)*\*\//.source,r=0;r<2;r++)t=t.replace(/<self>/g,function(){return t});t=t.replace(/<self>/g,function(){return/[^\s\S]/.source}),e.languages.rust={comment:[{pattern:RegExp(/(^|[^\\])/.source+t),lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/b?"(?:\\[\s\S]|[^\\"])*"|b?r(#*)"(?:[^"]|"(?!\1))*"\1/,greedy:!0},char:{pattern:/b?'(?:\\(?:x[0-7][\da-fA-F]|u\{(?:[\da-fA-F]_*){1,6}\}|.)|[^\\\r\n\t'])'/,greedy:!0},attribute:{pattern:/#!?\[(?:[^\[\]"]|"(?:\\[\s\S]|[^\\"])*")*\]/,greedy:!0,alias:"attr-name",inside:{string:null}},"closure-params":{pattern:/([=(,:]\s*|\bmove\s*)\|[^|]*\||\|[^|]*\|(?=\s*(?:\{|->))/,lookbehind:!0,greedy:!0,inside:{"closure-punctuation":{pattern:/^\||\|$/,alias:"punctuation"},rest:null}},"lifetime-annotation":{pattern:/'\w+/,alias:"symbol"},"fragment-specifier":{pattern:/(\$\w+:)[a-z]+/,lookbehind:!0,alias:"punctuation"},variable:/\$\w+/,"function-definition":{pattern:/(\bfn\s+)\w+/,lookbehind:!0,alias:"function"},"type-definition":{pattern:/(\b(?:enum|struct|trait|type|union)\s+)\w+/,lookbehind:!0,alias:"class-name"},"module-declaration":[{pattern:/(\b(?:crate|mod)\s+)[a-z][a-z_\d]*/,lookbehind:!0,alias:"namespace"},{pattern:/(\b(?:crate|self|super)\s*)::\s*[a-z][a-z_\d]*\b(?:\s*::(?:\s*[a-z][a-z_\d]*\s*::)*)?/,lookbehind:!0,alias:"namespace",inside:{punctuation:/::/}}],keyword:[/\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\b/,/\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\b/],function:/\b[a-z_]\w*(?=\s*(?:::\s*<|\())/,macro:{pattern:/\b\w+!/,alias:"property"},constant:/\b[A-Z_][A-Z_\d]+\b/,"class-name":/\b[A-Z]\w*\b/,namespace:{pattern:/(?:\b[a-z][a-z_\d]*\s*::\s*)*\b[a-z][a-z_\d]*\s*::(?!\s*<)/,inside:{punctuation:/::/}},number:/\b(?:0x[\dA-Fa-f](?:_?[\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\d(?:_?\d)*)?\.)?\d(?:_?\d)*(?:[Ee][+-]?\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\b/,boolean:/\b(?:false|true)\b/,punctuation:/->|\.\.=|\.{1,3}|::|[{}[\];(),:]/,operator:/[-+*\/%!^]=?|=[=>]?|&[&=]?|\|[|=]?|<<?=?|>>?=?|[@?]/},e.languages.rust["closure-params"].inside.rest=e.languages.rust,e.languages.rust.attribute.inside.string=e.languages.rust.string}(Prism)),Ji}ul(),Prism.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},Prism.languages.markup.tag.inside["attr-value"].inside.entity=Prism.languages.markup.entity,Prism.languages.markup.doctype.inside["internal-subset"].inside=Prism.languages.markup,Prism.hooks.add("wrap",function(e){e.type==="entity"&&(e.attributes.title=e.content.replace(/&amp;/,"&"))}),Object.defineProperty(Prism.languages.markup.tag,"addInlined",{value:function(t,r){var n={};n["language-"+r]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:Prism.languages[r]},n.cdata=/^<!\[CDATA\[|\]\]>$/i;var i={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}};i["language-"+r]={pattern:/[\s\S]+/,inside:Prism.languages[r]};var a={};a[t]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return t}),"i"),lookbehind:!0,greedy:!0,inside:i},Prism.languages.insertBefore("markup","cdata",a)}}),Object.defineProperty(Prism.languages.markup.tag,"addAttribute",{value:function(e,t){Prism.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+e+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[t,"language-"+t],inside:Prism.languages[t]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),Prism.languages.html=Prism.languages.markup,Prism.languages.mathml=Prism.languages.markup,Prism.languages.svg=Prism.languages.markup,Prism.languages.xml=Prism.languages.extend("markup",{}),Prism.languages.ssml=Prism.languages.xml,Prism.languages.atom=Prism.languages.xml,Prism.languages.rss=Prism.languages.xml,function(e){function t(r,n){return"___"+r.toUpperCase()+n+"___"}Object.defineProperties(e.languages["markup-templating"]={},{buildPlaceholders:{value:function(r,n,i,a){if(r.language===n){var s=r.tokenStack=[];r.code=r.code.replace(i,function(o){if(typeof a=="function"&&!a(o))return o;for(var l=s.length,u;r.code.indexOf(u=t(n,l))!==-1;)++l;return s[l]=o,u}),r.grammar=e.languages.markup}}},tokenizePlaceholders:{value:function(r,n){if(r.language!==n||!r.tokenStack)return;r.grammar=e.languages[n];var i=0,a=Object.keys(r.tokenStack);function s(o){for(var l=0;l<o.length&&!(i>=a.length);l++){var u=o[l];if(typeof u=="string"||u.content&&typeof u.content=="string"){var x=a[i],v=r.tokenStack[x],$=typeof u=="string"?u:u.content,y=t(n,x),C=$.indexOf(y);if(C>-1){++i;var z=$.substring(0,C),H=new e.Token(n,e.tokenize(v,r.grammar),"language-"+n,v),X=$.substring(C+y.length),p=[];z&&p.push.apply(p,s([z])),p.push(H),X&&p.push.apply(p,s([X])),typeof u=="string"?o.splice.apply(o,[l,1].concat(p)):u.content=p}}else u.content&&s(u.content)}return o}s(r.tokens)}}})}(Prism);var Xi={},ea;function dl(){return ea||(ea=1,function(e){var t=/\/\*[\s\S]*?\*\/|\/\/.*|#(?!\[).*/,r=[{pattern:/\b(?:false|true)\b/i,alias:"boolean"},{pattern:/(::\s*)\b[a-z_]\w*\b(?!\s*\()/i,greedy:!0,lookbehind:!0},{pattern:/(\b(?:case|const)\s+)\b[a-z_]\w*(?=\s*[;=])/i,greedy:!0,lookbehind:!0},/\b(?:null)\b/i,/\b[A-Z_][A-Z0-9_]*\b(?!\s*\()/],n=/\b0b[01]+(?:_[01]+)*\b|\b0o[0-7]+(?:_[0-7]+)*\b|\b0x[\da-f]+(?:_[\da-f]+)*\b|(?:\b\d+(?:_\d+)*\.?(?:\d+(?:_\d+)*)?|\B\.\d+)(?:e[+-]?\d+)?/i,i=/<?=>|\?\?=?|\.{3}|\??->|[!=]=?=?|::|\*\*=?|--|\+\+|&&|\|\||<<|>>|[?~]|[/^|%*&<>.+-]=?/,a=/[{}\[\](),:;]/;e.languages.php={delimiter:{pattern:/\?>$|^<\?(?:php(?=\s)|=)?/i,alias:"important"},comment:t,variable:/\$+(?:\w+\b|(?=\{))/,package:{pattern:/(namespace\s+|use\s+(?:function\s+)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,lookbehind:!0,inside:{punctuation:/\\/}},"class-name-definition":{pattern:/(\b(?:class|enum|interface|trait)\s+)\b[a-z_]\w*(?!\\)\b/i,lookbehind:!0,alias:"class-name"},"function-definition":{pattern:/(\bfunction\s+)[a-z_]\w*(?=\s*\()/i,lookbehind:!0,alias:"function"},keyword:[{pattern:/(\(\s*)\b(?:array|bool|boolean|float|int|integer|object|string)\b(?=\s*\))/i,alias:"type-casting",greedy:!0,lookbehind:!0},{pattern:/([(,?]\s*)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|object|self|static|string)\b(?=\s*\$)/i,alias:"type-hint",greedy:!0,lookbehind:!0},{pattern:/(\)\s*:\s*(?:\?\s*)?)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|never|object|self|static|string|void)\b/i,alias:"return-type",greedy:!0,lookbehind:!0},{pattern:/\b(?:array(?!\s*\()|bool|float|int|iterable|mixed|object|string|void)\b/i,alias:"type-declaration",greedy:!0},{pattern:/(\|\s*)(?:false|null)\b|\b(?:false|null)(?=\s*\|)/i,alias:"type-declaration",greedy:!0,lookbehind:!0},{pattern:/\b(?:parent|self|static)(?=\s*::)/i,alias:"static-context",greedy:!0},{pattern:/(\byield\s+)from\b/i,lookbehind:!0},/\bclass\b/i,{pattern:/((?:^|[^\s>:]|(?:^|[^-])>|(?:^|[^:]):)\s*)\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|never|new|or|parent|print|private|protected|public|readonly|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\b/i,lookbehind:!0}],"argument-name":{pattern:/([(,]\s*)\b[a-z_]\w*(?=\s*:(?!:))/i,lookbehind:!0},"class-name":[{pattern:/(\b(?:extends|implements|instanceof|new(?!\s+self|\s+static))\s+|\bcatch\s*\()\b[a-z_]\w*(?!\\)\b/i,greedy:!0,lookbehind:!0},{pattern:/(\|\s*)\b[a-z_]\w*(?!\\)\b/i,greedy:!0,lookbehind:!0},{pattern:/\b[a-z_]\w*(?!\\)\b(?=\s*\|)/i,greedy:!0},{pattern:/(\|\s*)(?:\\?\b[a-z_]\w*)+\b/i,alias:"class-name-fully-qualified",greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/(?:\\?\b[a-z_]\w*)+\b(?=\s*\|)/i,alias:"class-name-fully-qualified",greedy:!0,inside:{punctuation:/\\/}},{pattern:/(\b(?:extends|implements|instanceof|new(?!\s+self\b|\s+static\b))\s+|\bcatch\s*\()(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,alias:"class-name-fully-qualified",greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/\b[a-z_]\w*(?=\s*\$)/i,alias:"type-declaration",greedy:!0},{pattern:/(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,alias:["class-name-fully-qualified","type-declaration"],greedy:!0,inside:{punctuation:/\\/}},{pattern:/\b[a-z_]\w*(?=\s*::)/i,alias:"static-context",greedy:!0},{pattern:/(?:\\?\b[a-z_]\w*)+(?=\s*::)/i,alias:["class-name-fully-qualified","static-context"],greedy:!0,inside:{punctuation:/\\/}},{pattern:/([(,?]\s*)[a-z_]\w*(?=\s*\$)/i,alias:"type-hint",greedy:!0,lookbehind:!0},{pattern:/([(,?]\s*)(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,alias:["class-name-fully-qualified","type-hint"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/(\)\s*:\s*(?:\?\s*)?)\b[a-z_]\w*(?!\\)\b/i,alias:"return-type",greedy:!0,lookbehind:!0},{pattern:/(\)\s*:\s*(?:\?\s*)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,alias:["class-name-fully-qualified","return-type"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}}],constant:r,function:{pattern:/(^|[^\\\w])\\?[a-z_](?:[\w\\]*\w)?(?=\s*\()/i,lookbehind:!0,inside:{punctuation:/\\/}},property:{pattern:/(->\s*)\w+/,lookbehind:!0},number:n,operator:i,punctuation:a};var s={pattern:/\{\$(?:\{(?:\{[^{}]+\}|[^{}]+)\}|[^{}])+\}|(^|[^\\{])\$+(?:\w+(?:\[[^\r\n\[\]]+\]|->\w+)?)/,lookbehind:!0,inside:e.languages.php},o=[{pattern:/<<<'([^']+)'[\r\n](?:.*[\r\n])*?\1;/,alias:"nowdoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<<'[^']+'|[a-z_]\w*;$/i,alias:"symbol",inside:{punctuation:/^<<<'?|[';]$/}}}},{pattern:/<<<(?:"([^"]+)"[\r\n](?:.*[\r\n])*?\1;|([a-z_]\w*)[\r\n](?:.*[\r\n])*?\2;)/i,alias:"heredoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<<(?:"[^"]+"|[a-z_]\w*)|[a-z_]\w*;$/i,alias:"symbol",inside:{punctuation:/^<<<"?|[";]$/}},interpolation:s}},{pattern:/`(?:\\[\s\S]|[^\\`])*`/,alias:"backtick-quoted-string",greedy:!0},{pattern:/'(?:\\[\s\S]|[^\\'])*'/,alias:"single-quoted-string",greedy:!0},{pattern:/"(?:\\[\s\S]|[^\\"])*"/,alias:"double-quoted-string",greedy:!0,inside:{interpolation:s}}];e.languages.insertBefore("php","variable",{string:o,attribute:{pattern:/#\[(?:[^"'\/#]|\/(?![*/])|\/\/.*$|#(?!\[).*$|\/\*(?:[^*]|\*(?!\/))*\*\/|"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*')+\](?=\s*[a-z$#])/im,greedy:!0,inside:{"attribute-content":{pattern:/^(#\[)[\s\S]+(?=\]$)/,lookbehind:!0,inside:{comment:t,string:o,"attribute-class-name":[{pattern:/([^:]|^)\b[a-z_]\w*(?!\\)\b/i,alias:"class-name",greedy:!0,lookbehind:!0},{pattern:/([^:]|^)(?:\\?\b[a-z_]\w*)+/i,alias:["class-name","class-name-fully-qualified"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}}],constant:r,number:n,operator:i,punctuation:a}},delimiter:{pattern:/^#\[|\]$/,alias:"punctuation"}}}}),e.hooks.add("before-tokenize",function(l){if(/<\?/.test(l.code)){var u=/<\?(?:[^"'/#]|\/(?![*/])|("|')(?:\\[\s\S]|(?!\1)[^\\])*\1|(?:\/\/|#(?!\[))(?:[^?\n\r]|\?(?!>))*(?=$|\?>|[\r\n])|#\[|\/\*(?:[^*]|\*(?!\/))*(?:\*\/|$))*?(?:\?>|$)/g;e.languages["markup-templating"].buildPlaceholders(l,"php",u)}}),e.hooks.add("after-tokenize",function(l){e.languages["markup-templating"].tokenizePlaceholders(l,"php")})}(Prism)),Xi}dl();const ta="(if|else if|await|then|catch|each|html|debug)";Prism.languages.svelte=Prism.languages.extend("markup",{each:{pattern:new RegExp("{[#/]each(?:(?:\\{(?:(?:\\{(?:[^{}])*\\})|(?:[^{}]))*\\})|(?:[^{}]))*}"),inside:{"language-javascript":[{pattern:/(as[\s\S]*)\([\s\S]*\)(?=\s*\})/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(as[\s]*)[\s\S]*(?=\s*)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(#each[\s]*)[\s\S]*(?=as)/,lookbehind:!0,inside:Prism.languages.javascript}],keyword:/[#/]each|as/,punctuation:/{|}/}},block:{pattern:new RegExp("{[#:/@]/s"+ta+"(?:(?:\\{(?:(?:\\{(?:[^{}])*\\})|(?:[^{}]))*\\})|(?:[^{}]))*}"),inside:{punctuation:/^{|}$/,keyword:[new RegExp("[#:/@]"+ta+"( )*"),/as/,/then/],"language-javascript":{pattern:/[\s\S]*/,inside:Prism.languages.javascript}}},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?:"[^"]*"|'[^']*'|{[\s\S]+?}(?=[\s/>])))|(?=[\s/>])))+)?\s*\/?>/i,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/i,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"language-javascript":{pattern:/\{(?:(?:\{(?:(?:\{(?:[^{}])*\})|(?:[^{}]))*\})|(?:[^{}]))*\}/,inside:Prism.languages.javascript},"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/i,inside:{punctuation:[/^=/,{pattern:/^(\s*)["']|["']$/,lookbehind:!0}],"language-javascript":{pattern:/{[\s\S]+}/,inside:Prism.languages.javascript}}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},"language-javascript":{pattern:/\{(?:(?:\{(?:(?:\{(?:[^{}])*\})|(?:[^{}]))*\})|(?:[^{}]))*\}/,lookbehind:!0,inside:Prism.languages.javascript}}),Prism.languages.svelte.tag.inside["attr-value"].inside.entity=Prism.languages.svelte.entity,Prism.hooks.add("wrap",e=>{e.type==="entity"&&(e.attributes.title=e.content.replace(/&amp;/,"&"))}),Object.defineProperty(Prism.languages.svelte.tag,"addInlined",{value:function(t,r){const n={};n["language-"+r]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:Prism.languages[r]},n.cdata=/^<!\[CDATA\[|\]\]>$/i;const i={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}};i["language-"+r]={pattern:/[\s\S]+/,inside:Prism.languages[r]};const a={};a[t]={pattern:RegExp(/(<__[\s\S]*?>)(?:<!\[CDATA\[[\s\S]*?\]\]>\s*|[\s\S])*?(?=<\/__>)/.source.replace(/__/g,t),"i"),lookbehind:!0,greedy:!0,inside:i},Prism.languages.insertBefore("svelte","cdata",a)}}),Prism.languages.svelte.tag.addInlined("style","css"),Prism.languages.svelte.tag.addInlined("script","javascript"),function(){typeof Prism>"u"||typeof document>"u"||!document.createRange||(Prism.plugins.KeepMarkup=!0,Prism.hooks.add("before-highlight",function(e){if(!e.element.children.length||!Prism.util.isActive(e.element,"keep-markup",!0))return;var t=Prism.util.isActive(e.element,"drop-tokens",!1);function r(o){return!(t&&o.nodeName.toLowerCase()==="span"&&o.classList.contains("token"))}var n=0,i=[];function a(o){if(!r(o)){s(o);return}var l={element:o,posOpen:n};i.push(l),s(o),l.posClose=n}function s(o){for(var l=0,u=o.childNodes.length;l<u;l++){var x=o.childNodes[l];x.nodeType===1?a(x):x.nodeType===3&&(n+=x.data.length)}}s(e.element),i.length&&(e.keepMarkup=i)}),Prism.hooks.add("after-highlight",function(e){if(e.keepMarkup&&e.keepMarkup.length){var t=function(r,n){for(var i=0,a=r.childNodes.length;i<a;i++){var s=r.childNodes[i];if(s.nodeType===1){if(!t(s,n))return!1}else s.nodeType===3&&(!n.nodeStart&&n.pos+s.data.length>n.node.posOpen&&(n.nodeStart=s,n.nodeStartPos=n.node.posOpen-n.pos),n.nodeStart&&n.pos+s.data.length>=n.node.posClose&&(n.nodeEnd=s,n.nodeEndPos=n.node.posClose-n.pos),n.pos+=s.data.length);if(n.nodeStart&&n.nodeEnd){var o=document.createRange();return o.setStart(n.nodeStart,n.nodeStartPos),o.setEnd(n.nodeEnd,n.nodeEndPos),n.node.element.innerHTML="",n.node.element.appendChild(o.extractContents()),o.insertNode(n.node.element),o.detach(),!1}}return!0};e.keepMarkup.forEach(function(r){t(e.element,{node:r,pos:0})}),e.highlightedCode=e.element.innerHTML}}))}();const hl="code[class*=language-],pre[class*=language-]{color:var(--prism-maintext);text-align:left;white-space:pre;word-spacing:normal;word-break:normal;tab-size:4;-webkit-hyphens:none;hyphens:none;direction:ltr;font-size:1em;line-height:1.5}pre>code[class*=language-]{font-size:1em}pre[class*=language-]{border:1px solid var(--prism-border);border-radius:.25rem;margin:.5em 0;padding:1em;overflow:auto}:not(pre)>code[class*=language-],pre[class*=language-]{background:var(--prism-background)}.token.comment,.token.prolog,.token.doctype,.token.italic,.token.cdata{font-style:italic}.token.important,.token.function,.token.bold{font-weight:700}.token.namespace{opacity:.7}.token.atrule{color:var(--prism-atrule)}.token.attr{color:var(--prism-attr)}.token.attr-name{color:var(--prism-attr-name)}.token.boolean{color:var(--prism-boolean)}.token.builtin{color:var(--prism-builtin)}.token.cdata{color:var(--prism-cdata)}.token.changed{color:var(--prism-changed)}.token.char{color:var(--prism-char)}.token.comment{color:var(--prism-comment)}.token.constant{color:var(--prism-constant)}.token.deleted{color:var(--prism-deleted)}.token.doctype{color:var(--prism-doctype)}.token.entity{color:var(--prism-entity);cursor:help}.token.function{color:var(--prism-function)}.token.function-variable{color:var(--prism-function-variable,var(--prism-function))}.token.inserted{color:var(--prism-inserted)}.token.keyword{color:var(--prism-keyword)}.token.number{color:var(--prism-number)}.token.operator{color:var(--prism-operator)}.token.prolog{color:var(--prism-prolog)}.token.property{color:var(--prism-property)}.token.punctuation{color:var(--prism-punctuation)}.token.regex{color:var(--prism-regex)}.token.selector{color:var(--prism-selector)}.token.string{color:var(--prism-string)}.token.symbol{color:var(--prism-symbol)}.token.tag{color:var(--prism-tag)}.token.url{color:var(--prism-url)}.token.variable{color:var(--prism-variable)}.token.placeholder{color:var(--prism-placeholder)}.token.statement{color:var(--prism-statement)}.token.attr-value{color:var(--prism-attr-value)}.token.control{color:var(--prism-control)}.token.directive{color:var(--prism-directive)}.token.unit{color:var(--prism-unit)}.token.important{color:var(--prism-important)}.token.class-name{color:var(--prism-class-name)}",ze=Te(`/*! tailwindcss v4.1.6 | MIT License | https://tailwindcss.com */
@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-scroll-snap-strictness:proximity;--tw-space-y-reverse:0;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-font-weight:initial;--tw-tracking:initial;--tw-ordinal:initial;--tw-slashed-zero:initial;--tw-numeric-figure:initial;--tw-numeric-spacing:initial;--tw-numeric-fraction:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-content:"";--tw-duration:initial}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-100:#ffe2e2;--color-red-500:#fb2c36;--color-red-600:#e40014;--color-red-700:#bf000f;--color-red-800:#9f0712;--color-orange-100:#ffedd5;--color-orange-500:#fe6e00;--color-orange-800:#9f2d00;--color-amber-400:#fcbb00;--color-yellow-100:#fef9c2;--color-yellow-400:#fac800;--color-yellow-600:#cd8900;--color-yellow-800:#874b00;--color-green-100:#dcfce7;--color-green-600:#00a544;--color-green-700:#008138;--color-green-800:#016630;--color-sky-50:#f0f9ff;--color-sky-100:#dff2fe;--color-sky-200:#b8e6fe;--color-sky-300:#77d4ff;--color-sky-400:#00bcfe;--color-sky-500:#00a5ef;--color-sky-600:#0084cc;--color-sky-700:#0069a4;--color-sky-800:#005986;--color-sky-900:#024a70;--color-sky-950:#052f4a;--color-blue-600:#155dfc;--color-zinc-50:#fafafa;--color-zinc-100:#f4f4f5;--color-zinc-200:#e4e4e7;--color-zinc-300:#d4d4d8;--color-zinc-400:#9f9fa9;--color-zinc-500:#71717b;--color-zinc-600:#52525c;--color-zinc-700:#3f3f46;--color-zinc-800:#27272a;--color-zinc-900:#18181b;--color-zinc-950:#09090b;--color-neutral-400:#a1a1a1;--color-white:var(--mut-white,#fff);--spacing:.25rem;--container-6xl:72rem;--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-5xl:3rem;--text-5xl--line-height:1;--font-weight-light:300;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--tracking-tight:-.025em;--radius-sm:.25rem;--radius-md:.375rem;--radius-lg:.5rem;--radius-3xl:1.5rem;--blur-lg:16px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--transition-property-max-width:max-width;--transition-property-stroke-opacity:stroke-opacity;--spacing-drawer-half-open:var(--mte-drawer-height-half-open,120px)}@supports (color:lab(0% 0 0)){:root,:host{--color-red-100:#ffe2e2;--color-red-500:#fb2c36;--color-red-600:#e40014;--color-red-700:#bf000f;--color-red-800:#9f0712;--color-orange-100:#ffedd5;--color-orange-500:#fe6e00;--color-orange-800:#9f2d00;--color-amber-400:#fcbb00;--color-yellow-100:#fef9c2;--color-yellow-400:#fac800;--color-yellow-600:#cd8900;--color-yellow-800:#874b00;--color-green-100:#dcfce7;--color-green-600:#00a544;--color-green-700:#008138;--color-green-800:#016630;--color-sky-50:#f0f9ff;--color-sky-100:#dff2fe;--color-sky-200:#b8e6fe;--color-sky-300:#77d4ff;--color-sky-400:#00bcfe;--color-sky-500:#00a5ef;--color-sky-600:#0084cc;--color-sky-700:#0069a4;--color-sky-800:#005986;--color-sky-900:#024a70;--color-sky-950:#052f4a;--color-blue-600:#155dfc;--color-zinc-50:#fafafa;--color-zinc-100:#f4f4f5;--color-zinc-200:#e4e4e7;--color-zinc-300:#d4d4d8;--color-zinc-400:#9f9fa9;--color-zinc-500:#71717b;--color-zinc-600:#52525c;--color-zinc-700:#3f3f46;--color-zinc-800:#27272a;--color-zinc-900:#18181b;--color-zinc-950:#09090b;--color-neutral-400:#a1a1a1}@supports (color:lab(0% 0 0)){:root,:host{--color-red-100:lab(92.243% 10.2865 3.83865);--color-red-500:lab(55.4814% 75.0732 48.8528);--color-red-600:lab(48.4493% 77.4328 61.5452);--color-red-700:lab(40.4273% 67.2623 53.7441);--color-red-800:lab(33.7174% 55.8993 41.0293);--color-orange-100:lab(94.7127% 3.58394 14.3151);--color-orange-500:lab(64.272% 57.1788 90.3583);--color-orange-800:lab(37.1566% 46.6433 50.5562);--color-amber-400:lab(80.1641% 16.6016 99.2089);--color-yellow-100:lab(97.3564% -4.51407 27.344);--color-yellow-400:lab(83.2664% 8.65132 106.895);--color-yellow-600:lab(62.7799% 22.4198 86.1544);--color-yellow-800:lab(38.7484% 23.5833 51.4916);--color-green-100:lab(96.1861% -13.8464 6.52365);--color-green-600:lab(59.0978% -58.6621 41.2579);--color-green-700:lab(47.0329% -47.0239 31.4788);--color-green-800:lab(37.4616% -36.7971 22.9692);--color-sky-50:lab(97.3623% -2.33802 -4.13098);--color-sky-100:lab(94.3709% -4.56053 -8.23453);--color-sky-200:lab(88.6983% -11.3978 -16.8488);--color-sky-300:lab(80.3307% -20.2945 -31.385);--color-sky-400:lab(70.687% -23.6078 -45.9483);--color-sky-500:lab(63.3038% -18.433 -51.0407);--color-sky-600:lab(51.7754% -11.4712 -49.8349);--color-sky-700:lab(41.6013% -9.10805 -42.5647);--color-sky-800:lab(35.164% -9.57692 -34.4068);--color-sky-900:lab(29.1959% -8.34689 -28.2453);--color-sky-950:lab(17.8299% -5.31271 -21.1584);--color-blue-600:lab(44.0605% 29.0279 -86.0352);--color-zinc-50:lab(98.26% -.0000298023 0);--color-zinc-100:lab(96.1634% .0993311 -.364041);--color-zinc-200:lab(90.6853% .399232 -1.45452);--color-zinc-300:lab(84.9837% .601292 -2.17987);--color-zinc-400:lab(65.6464% 1.53497 -5.42429);--color-zinc-500:lab(47.8878% 1.65477 -5.77283);--color-zinc-600:lab(35.1166% 1.78212 -6.1173);--color-zinc-700:lab(26.8019% 1.35387 -4.68303);--color-zinc-800:lab(15.7305% .613764 -2.16959);--color-zinc-900:lab(8.30603% .618212 -2.16573);--color-zinc-950:lab(2.51107% .242703 -.886115);--color-neutral-400:lab(66.128% 0 0)}}}:host{--mte-drawer-height-half-open:120px}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){appearance:button}::file-selector-button{appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}[type=text],input:where(:not([type])),[type=email],[type=url],[type=password],[type=number],[type=date],[type=datetime-local],[type=month],[type=search],[type=tel],[type=time],[type=week],[multiple],textarea,select{appearance:none;border-color:var(--mut-gray-500,var(--color-zinc-500));--tw-shadow:0 0 #0000;background-color:#fff;border-width:1px;border-radius:0;padding:.5rem .75rem;font-size:1rem;line-height:1.5rem}:is([type=text],input:where(:not([type])),[type=email],[type=url],[type=password],[type=number],[type=date],[type=datetime-local],[type=month],[type=search],[type=tel],[type=time],[type=week],[multiple],textarea,select):focus{outline-offset:2px;--tw-ring-inset:var(--tw-empty, );--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:#155dfc;--tw-ring-offset-shadow:var(--tw-ring-inset)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);border-color:#155dfc;border-color:lab(44.0605% 29.0279 -86.0352);outline:2px solid #0000}@supports (color:lab(0% 0 0)){:is([type=text],input:where(:not([type])),[type=email],[type=url],[type=password],[type=number],[type=date],[type=datetime-local],[type=month],[type=search],[type=tel],[type=time],[type=week],[multiple],textarea,select):focus{--tw-ring-color:#155dfc}@supports (color:lab(0% 0 0)){:is([type=text],input:where(:not([type])),[type=email],[type=url],[type=password],[type=number],[type=date],[type=datetime-local],[type=month],[type=search],[type=tel],[type=time],[type=week],[multiple],textarea,select):focus{--tw-ring-color:lab(44.0605% 29.0279 -86.0352)}}}input::placeholder,textarea::placeholder{color:var(--mut-gray-500,var(--color-zinc-500));opacity:1}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-date-and-time-value{min-height:1.5em}::-webkit-date-and-time-value{text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-year-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-month-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-day-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-hour-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-minute-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-second-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-millisecond-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-meridiem-field{padding-top:0;padding-bottom:0}select{print-color-adjust:exact;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='oklch(55.1%25 0.027 264.364)' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");background-position:right .5rem center;background-repeat:no-repeat;background-size:1.5em 1.5em;padding-right:2.5rem}[multiple],[size]:where(select:not([size="1"])){background-image:initial;background-position:initial;background-repeat:unset;background-size:initial;print-color-adjust:unset;padding-right:.75rem}[type=checkbox],[type=radio]{appearance:none;print-color-adjust:exact;vertical-align:middle;-webkit-user-select:none;user-select:none;color:#155dfc;color:lab(44.0605% 29.0279 -86.0352);border-color:var(--mut-gray-500,var(--color-zinc-500));--tw-shadow:0 0 #0000;background-color:#fff;background-origin:border-box;border-width:1px;flex-shrink:0;width:1rem;height:1rem;padding:0;display:inline-block}[type=checkbox]{border-radius:0}[type=radio]{border-radius:100%}[type=checkbox]:focus,[type=radio]:focus{outline-offset:2px;--tw-ring-inset:var(--tw-empty, );--tw-ring-offset-width:2px;--tw-ring-offset-color:#fff;--tw-ring-color:#155dfc;--tw-ring-offset-shadow:var(--tw-ring-inset)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);outline:2px solid #0000}@supports (color:lab(0% 0 0)){[type=checkbox]:focus,[type=radio]:focus{--tw-ring-color:#155dfc}@supports (color:lab(0% 0 0)){[type=checkbox]:focus,[type=radio]:focus{--tw-ring-color:lab(44.0605% 29.0279 -86.0352)}}}[type=checkbox]:checked,[type=radio]:checked{background-color:currentColor;background-position:50%;background-repeat:no-repeat;background-size:100% 100%;border-color:#0000}[type=checkbox]:checked{background-image:url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e")}@media (forced-colors:active){[type=checkbox]:checked{appearance:auto}}[type=radio]:checked{background-image:url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e")}@media (forced-colors:active){[type=radio]:checked{appearance:auto}}[type=checkbox]:checked:hover,[type=checkbox]:checked:focus,[type=radio]:checked:hover,[type=radio]:checked:focus{background-color:currentColor;border-color:#0000}[type=checkbox]:indeterminate{background-color:currentColor;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");background-position:50%;background-repeat:no-repeat;background-size:100% 100%;border-color:#0000}@media (forced-colors:active){[type=checkbox]:indeterminate{appearance:auto}}[type=checkbox]:indeterminate:hover,[type=checkbox]:indeterminate:focus{background-color:currentColor;border-color:#0000}[type=file]{background:unset;border-color:inherit;font-size:unset;line-height:inherit;border-width:0;border-radius:0;padding:0}[type=file]:focus{outline:1px solid buttontext;outline:1px auto -webkit-focus-ring-color}}@layer components;@layer utilities{.pointer-events-none{pointer-events:none}.invisible{visibility:hidden}.visible{visibility:visible}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.sticky{position:sticky}.top-offset{top:var(--top-offset,0)}.bottom-0{bottom:calc(var(--spacing)*0)}.left-0{left:calc(var(--spacing)*0)}.z-10{z-index:10}.z-20{z-index:20}.float-right{float:right}.container{width:100%}@media (min-width:2000px){.container{max-width:2000px}}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.container{margin-inline:auto}.mx-0\\.5{margin-inline:calc(var(--spacing)*.5)}.mx-1{margin-inline:calc(var(--spacing)*1)}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-auto{margin-inline:auto}.my-3{margin-block:calc(var(--spacing)*3)}.my-4{margin-block:calc(var(--spacing)*4)}.ms-1{margin-inline-start:calc(var(--spacing)*1)}.ms-3{margin-inline-start:calc(var(--spacing)*3)}.me-1{margin-inline-end:calc(var(--spacing)*1)}.me-2{margin-inline-end:calc(var(--spacing)*2)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-4{margin-top:calc(var(--spacing)*4)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-4{margin-right:calc(var(--spacing)*4)}.mr-6{margin-right:calc(var(--spacing)*6)}.mr-auto{margin-right:auto}.-mb-px{margin-bottom:-1px}.mb-0{margin-bottom:calc(var(--spacing)*0)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-2{margin-left:calc(var(--spacing)*2)}.ml-4{margin-left:calc(var(--spacing)*4)}.ml-6{margin-left:calc(var(--spacing)*6)}.ml-auto{margin-left:auto}.block{display:block}.contents{display:contents}.flex{display:flex}.hidden{display:none}.inline{display:inline}.inline-block{display:inline-block}.inline-flex{display:inline-flex}.table{display:table}.size-4{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.size-6{width:calc(var(--spacing)*6);height:calc(var(--spacing)*6)}.h-2{height:calc(var(--spacing)*2)}.h-3{height:calc(var(--spacing)*3)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-8{height:calc(var(--spacing)*8)}.h-100{height:calc(var(--spacing)*100)}.h-fit{height:fit-content}.h-full{height:100%}.max-h-\\[33rem\\]{max-height:33rem}.w-4{width:calc(var(--spacing)*4)}.w-5{width:calc(var(--spacing)*5)}.w-12{width:calc(var(--spacing)*12)}.w-24{width:calc(var(--spacing)*24)}.w-full{width:100%}.max-w-6xl{max-width:var(--container-6xl)}.max-w-\\[40rem\\]{max-width:40rem}.min-w-\\[24px\\]{min-width:24px}.shrink-0{flex-shrink:0}.grow{flex-grow:1}.table-auto{table-layout:auto}.rotate-180{rotate:180deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.cursor-help{cursor:help}.cursor-pointer{cursor:pointer}.resize{resize:both}.snap-y{scroll-snap-type:y var(--tw-scroll-snap-strictness)}.snap-start{scroll-snap-align:start}.flex-col{flex-direction:column}.flex-row{flex-direction:row}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.justify-around{justify-content:space-around}.justify-center{justify-content:center}.justify-start{justify-content:flex-start}.gap-2{gap:calc(var(--spacing)*2)}.gap-4{gap:calc(var(--spacing)*4)}.gap-5{gap:calc(var(--spacing)*5)}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.divide-y>:not(:last-child)){--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))}:where(.divide-gray-200>:not(:last-child)){border-color:var(--mut-gray-200,var(--color-zinc-200))}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.overflow-y-auto{overflow-y:auto}.rounded{border-radius:.25rem}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius-lg)}.rounded-md{border-radius:var(--radius-md)}.rounded-sm{border-radius:var(--radius-sm)}.rounded-t-3xl{border-top-left-radius:var(--radius-3xl);border-top-right-radius:var(--radius-3xl)}.rounded-t-lg{border-top-left-radius:var(--radius-lg);border-top-right-radius:var(--radius-lg)}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-none{--tw-border-style:none;border-style:none}.border-gray-200{border-color:var(--mut-gray-200,var(--color-zinc-200))}.border-primary-600{border-color:var(--mut-primary-600,var(--color-sky-600))}.border-transparent{border-color:#0000}.bg-blue-600{background-color:var(--color-blue-600)}.bg-gray-100{background-color:var(--mut-gray-100,var(--color-zinc-100))}.bg-gray-200{background-color:var(--mut-gray-200,var(--color-zinc-200))}.bg-gray-200\\/60{background-color:var(--mut-gray-200,#e4e4e7)}@supports (color:lab(0% 0 0)){.bg-gray-200\\/60{background-color:var(--mut-gray-200,#e4e4e7)}@supports (color:lab(0% 0 0)){.bg-gray-200\\/60{background-color:var(--mut-gray-200,lab(90.6853% .399232 -1.45452))}}}@supports (color:color-mix(in lab, red, red)){.bg-gray-200\\/60{background-color:color-mix(in oklab,var(--mut-gray-200,var(--color-zinc-200))60%,transparent)}}.bg-gray-300{background-color:var(--mut-gray-300,var(--color-zinc-300))}.bg-green-100{background-color:var(--color-green-100)}.bg-green-600{background-color:var(--color-green-600)}.bg-inherit{background-color:inherit}.bg-orange-100{background-color:var(--color-orange-100)}.bg-primary-100{background-color:var(--mut-primary-100,var(--color-sky-100))}.bg-primary-600{background-color:var(--mut-primary-600,var(--color-sky-600))}.bg-red-100{background-color:var(--color-red-100)}.bg-red-600{background-color:var(--color-red-600)}.bg-transparent{background-color:#0000}.bg-white{background-color:var(--mut-white,#fff)}.bg-yellow-100{background-color:var(--color-yellow-100)}.bg-yellow-400{background-color:var(--color-yellow-400)}.bg-yellow-600{background-color:var(--color-yellow-600)}.stroke-gray-800{stroke:var(--mut-gray-800,var(--color-zinc-800))}.p-1{padding:calc(var(--spacing)*1)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-2\\.5{padding-inline:calc(var(--spacing)*2.5)}.px-4{padding-inline:calc(var(--spacing)*4)}.py-0\\.5{padding-block:calc(var(--spacing)*.5)}.py-2{padding-block:calc(var(--spacing)*2)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-6{padding-block:calc(var(--spacing)*6)}.pe-4{padding-inline-end:calc(var(--spacing)*4)}.pt-7{padding-top:calc(var(--spacing)*7)}.pr-2{padding-right:calc(var(--spacing)*2)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pb-drawer-half-open{padding-bottom:var(--mte-drawer-height-half-open,120px)}.pl-1{padding-left:calc(var(--spacing)*1)}.text-center{text-align:center}.text-left{text-align:left}.align-middle{vertical-align:middle}.font-sans{font-family:var(--font-sans)}.text-5xl{font-size:var(--text-5xl);line-height:var(--tw-leading,var(--text-5xl--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-light{--tw-font-weight:var(--font-weight-light);font-weight:var(--font-weight-light)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-tight{--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight)}.whitespace-pre-wrap{white-space:pre-wrap}.text-gray-200{color:var(--mut-gray-200,var(--color-zinc-200))}.text-gray-400{color:var(--mut-gray-400,var(--color-zinc-400))}.text-gray-600{color:var(--mut-gray-600,var(--color-zinc-600))}.text-gray-700{color:var(--mut-gray-700,var(--color-zinc-700))}.text-gray-800{color:var(--mut-gray-800,var(--color-zinc-800))}.text-gray-900{color:var(--mut-gray-900,var(--color-zinc-900))}.text-green-700{color:var(--color-green-700)}.text-green-800{color:var(--color-green-800)}.text-orange-800{color:var(--color-orange-800)}.text-primary-500{color:var(--mut-primary-500,var(--color-sky-500))}.text-primary-800{color:var(--mut-primary-800,var(--color-sky-800))}.text-red-700{color:var(--color-red-700)}.text-red-800{color:var(--color-red-800)}.text-white{color:var(--mut-white,#fff)}.text-yellow-600{color:var(--color-yellow-600)}.text-yellow-800{color:var(--color-yellow-800)}.capitalize{text-transform:capitalize}.lowercase{text-transform:lowercase}.ordinal{--tw-ordinal:ordinal;font-variant-numeric:var(--tw-ordinal,)var(--tw-slashed-zero,)var(--tw-numeric-figure,)var(--tw-numeric-spacing,)var(--tw-numeric-fraction,)}.underline{text-decoration-line:underline}.decoration-dotted{text-decoration-style:dotted}.opacity-0{opacity:0}.opacity-100{opacity:1}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xl{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-offset-gray-200\\!{--tw-ring-offset-color:var(--mut-gray-200,var(--color-zinc-200))!important}.outline-hidden{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.outline-hidden{outline-offset:2px;outline:2px solid #0000}}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.backdrop-blur-lg{--tw-backdrop-blur:blur(var(--blur-lg));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-shadow{transition-property:box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-stroke-opacity{transition-property:var(--transition-property-stroke-opacity);transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.\\[httparchive\\:summary_pages\\.2018_12_15_desktop\\]{httparchive:summary pages.2018 12 15 desktop}@media (hover:hover){.group-hover\\:bg-gray-200\\!:is(:where(.group):hover *){background-color:var(--mut-gray-200,var(--color-zinc-200))!important}}.group-aria-selected\\:text-gray-200:is(:where(.group)[aria-selected=true] *){color:var(--mut-gray-200,var(--color-zinc-200))}.group-aria-selected\\:text-primary-50:is(:where(.group)[aria-selected=true] *){color:var(--mut-primary-50,var(--color-sky-50))}.group-aria-selected\\:underline:is(:where(.group)[aria-selected=true] *){text-decoration-line:underline}.backdrop\\:bg-gray-950\\/50::backdrop{background-color:var(--mut-gray-950,#09090b)}@supports (color:lab(0% 0 0)){.backdrop\\:bg-gray-950\\/50::backdrop{background-color:var(--mut-gray-950,#09090b)}@supports (color:lab(0% 0 0)){.backdrop\\:bg-gray-950\\/50::backdrop{background-color:var(--mut-gray-950,lab(2.51107% .242703 -.886115))}}}@supports (color:color-mix(in lab, red, red)){.backdrop\\:bg-gray-950\\/50::backdrop{background-color:color-mix(in oklab,var(--mut-gray-950,var(--color-zinc-950))50%,transparent)}}.backdrop\\:backdrop-blur-lg::backdrop{--tw-backdrop-blur:blur(var(--blur-lg));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.after\\:text-gray-800:after{content:var(--tw-content);color:var(--mut-gray-800,var(--color-zinc-800))}.after\\:content-\\[\\'\\/\\'\\]:after{content:var(--tw-content);--tw-content:"/";content:var(--tw-content)}.last\\:mr-12:last-child{margin-right:calc(var(--spacing)*12)}.odd\\:bg-gray-100:nth-child(odd),.even\\:bg-gray-100:nth-child(2n){background-color:var(--mut-gray-100,var(--color-zinc-100))}.checked\\:bg-primary-600:checked{background-color:var(--mut-primary-600,var(--color-sky-600))}@media (hover:hover){.hover\\:cursor-pointer:hover{cursor:pointer}.hover\\:border-gray-300:hover{border-color:var(--mut-gray-300,var(--color-zinc-300))}.hover\\:bg-gray-100:hover{background-color:var(--mut-gray-100,var(--color-zinc-100))}.hover\\:bg-gray-200:hover{background-color:var(--mut-gray-200,var(--color-zinc-200))}.hover\\:bg-primary-700:hover{background-color:var(--mut-primary-700,var(--color-sky-700))}.hover\\:text-gray-700:hover{color:var(--mut-gray-700,var(--color-zinc-700))}.hover\\:text-gray-900:hover{color:var(--mut-gray-900,var(--color-zinc-900))}.hover\\:text-primary-on:hover{color:var(--mut-primary-on,var(--color-sky-700))}.hover\\:underline:hover{text-decoration-line:underline}}.focus\\:shadow-none:focus{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\\:ring-primary-500:focus{--tw-ring-color:var(--mut-primary-500,var(--color-sky-500))}.focus\\:outline-hidden:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus\\:outline-hidden:focus{outline-offset:2px;outline:2px solid #0000}}.active\\:bg-gray-200:active{background-color:var(--mut-gray-200,var(--color-zinc-200))}.aria-selected\\:border-b-\\[3px\\][aria-selected=true]{border-bottom-style:var(--tw-border-style);border-bottom-width:3px}.aria-selected\\:border-solid[aria-selected=true]{--tw-border-style:solid;border-style:solid}.aria-selected\\:border-primary-700[aria-selected=true]{border-color:var(--mut-primary-700,var(--color-sky-700))}.aria-selected\\:bg-primary-500[aria-selected=true]{background-color:var(--mut-primary-500,var(--color-sky-500))}.aria-selected\\:text-gray-50[aria-selected=true]{color:var(--mut-gray-50,var(--color-zinc-50))}.aria-selected\\:text-primary-on[aria-selected=true]{color:var(--mut-primary-on,var(--color-sky-700))}.aria-selected\\:shadow-lg[aria-selected=true]{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (prefers-reduced-motion:no-preference){.motion-safe\\:transition-\\[height\\,max-width\\]{transition-property:height,max-width;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.motion-safe\\:transition-max-width{transition-property:var(--transition-property-max-width);transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.motion-safe\\:duration-200{--tw-duration:.2s;transition-duration:.2s}}@media (min-width:48rem){.md\\:ml-2{margin-left:calc(var(--spacing)*2)}.md\\:w-1\\/2{width:50%}.md\\:after\\:pl-1:after{content:var(--tw-content);padding-left:calc(var(--spacing)*1)}}@media (min-width:96rem){.\\32 xl\\:w-28{width:calc(var(--spacing)*28)}}}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-scroll-snap-strictness{syntax:"*";inherits:false;initial-value:proximity}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-ordinal{syntax:"*";inherits:false}@property --tw-slashed-zero{syntax:"*";inherits:false}@property --tw-numeric-figure{syntax:"*";inherits:false}@property --tw-numeric-spacing{syntax:"*";inherits:false}@property --tw-numeric-fraction{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-content{syntax:"*";inherits:false;initial-value:""}@property --tw-duration{syntax:"*";inherits:false}`),ra=Te(hl);if(ze.styleSheet&&document?.adoptedStyleSheets&&!document.adoptedStyleSheets.some(e=>e.cssRules[0]?.cssText===ze.styleSheet.cssRules[0].cssText)){const e=new CSSStyleSheet;let t=ze.cssText;t=t.replaceAll("inherits: false","inherits: true").substring(t.indexOf("@property")),e.replaceSync(t),document.adoptedStyleSheets.push(e)}class qe extends zt{static styles=[ze]}const ir=(e,t)=>b`<li title=${e||P} class="my-3 rounded-sm bg-white px-2 py-3 shadow-sm">${t}</li>`,tt=(e,t)=>b`<p title=${t||P}>${e}</p>`,na=e=>b`<div class="mt-2 mr-6 mb-6 flex flex-col gap-4">${e}</div>`,Q=(e,t)=>b`<span role="img" aria-label="${t}">${e}</span>`,pl=":host([mode=closed]){height:0}:host([mode=half]){height:var(--spacing-drawer-half-open)}:host([mode=open]){height:50%}";var ia=Object.defineProperty,fl=Object.getOwnPropertyDescriptor,aa=e=>{throw TypeError(e)},gl=(e,t,r)=>t in e?ia(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Rt=(e,t,r,n)=>{for(var i=n>1?void 0:n?fl(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&ia(t,r,i),i},sa=(e,t,r)=>gl(e,typeof t!="symbol"?t+"":t,r),oa=(e,t,r)=>t.has(e)||aa("Cannot "+r),ar=(e,t,r)=>(oa(e,t,"read from private field"),t.get(e)),Jr=(e,t,r)=>t.has(e)?aa("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),la=(e,t,r,n)=>(oa(e,t,"write to private field"),t.set(e,r),r),sr,Dt,Qr;const ml=120;let rt=class extends qe{constructor(){super(),Jr(this,sr),Jr(this,Dt),sa(this,"toggleReadMore",e=>{this.mode==="open"?this.mode="half":this.mode="open",e.preventDefault(),e.stopImmediatePropagation()}),Jr(this,Qr,e=>{e.key==="Escape"&&(this.mode="closed")}),this.mode="closed",this.hasDetail=!1,la(this,Dt,new AbortController),la(this,sr,new il(this,{callback:e=>{const t=e[0]?.contentRect.height??0,r=this.header?.clientHeight??0;return t-r}}))}get toggleMoreLabel(){switch(this.mode){case"half":return b`${Q("🔼","up arrow")} More`;case"open":return b`${Q("🔽","down arrow")} Less`;case"closed":return P}}connectedCallback(){super.connectedCallback(),window.addEventListener("keydown",ar(this,Qr),{signal:ar(this,Dt).signal})}disconnectedCallback(){ar(this,Dt).abort(),super.disconnectedCallback()}render(){const e=this.mode==="open",t=ar(this,sr).value;return b`<aside @click="${r=>r.stopPropagation()}" class="mr-4 ml-6">
      <header class="w-full py-4">
        <h2>
          <slot name="header"></slot>
          ${vt(this.hasDetail,b`<button data-testId="btnReadMoreToggle" class="ml-2 cursor-pointer align-middle" @click="${this.toggleReadMore}">
              ${this.toggleMoreLabel}
            </button>`)}
        </h2>
      </header>
      <div
        style="${t&&e?`height: ${t}px;`:P}"
        class="${Gr({"mb-4 motion-safe:transition-max-width":!0,"overflow-y-auto":e})}"
      >
        <slot name="summary"></slot>
        ${vt(this.hasDetail&&this.mode==="open",b`<slot name="detail"></slot>`)}
      </div>
    </aside>`}};sr=new WeakMap,Dt=new WeakMap,Qr=new WeakMap,sa(rt,"styles",[Te(pl),ze]),Rt([I({reflect:!0})],rt.prototype,"mode",2),Rt([I({reflect:!0,type:Boolean,attribute:"has-detail"})],rt.prototype,"hasDetail",2),Rt([I({attribute:!1})],rt.prototype,"toggleMoreLabel",1),Rt([Xt("header")],rt.prototype,"header",2),rt=Rt([se("mte-drawer")],rt);function vt(e,t){return e?typeof t=="function"?t():t:P}function Bt(e,t){return e==null?P:t(e)}function vl(e){switch(e){case"Killed":return"success";case"NoCoverage":return"caution";case"Survived":return"danger";case"Timeout":return"warning";case"Ignored":case"RuntimeError":case"Pending":case"CompileError":return"secondary"}}function bl(e){switch(e){case ne.Killing:return"success";case ne.Covering:return"warning";case ne.NotCovering:return"caution"}}function Xr(e){switch(e){case ne.Killing:return Q("✅",e);case ne.Covering:return Q("☂",e);case ne.NotCovering:return Q("🌧",e)}}function ca(e){switch(e){case"Killed":return Q("✅",e);case"NoCoverage":return Q("🙈",e);case"Ignored":return Q("🤥",e);case"Survived":return Q("👽",e);case"Timeout":return Q("⏰",e);case"Pending":return Q("⌛",e);case"RuntimeError":case"CompileError":return Q("💥",e)}}function en(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")}function bt(...e){const t=e.filter(Boolean).join("/");{const r=new URL(window.location.href);return new URL(`#${t}`,r).href}}function ua(e){return e.length>1?"s":""}function da({fileName:e,location:t}){return e?`${e}${t?`:${t.start.line}:${t.start.column}`:""}`:""}function ha(e){e&&!yl(e)&&e.scrollIntoView({block:"center",behavior:"smooth"})}function yl(e){const{top:t,bottom:r}=e.getBoundingClientRect();return t>=0&&r<=(window.innerHeight||document.documentElement.clientHeight)-ml}const pa=new wi,wl=el(jo(1),It(window,"hashchange").pipe(nl(e=>e.preventDefault()))).pipe(qr(()=>window.location.hash.substr(1).split("/").filter(Boolean).map(decodeURIComponent)));var me=(e=>(e.mutant="mutant",e.test="test",e))(me||{});class Fe extends qe{shouldReactivate(){return!0}reactivate(){this.requestUpdate()}#e=new gt;connectedCallback(){super.connectedCallback(),this.#e.add(pa.subscribe(()=>this.shouldReactivate()&&this.reactivate()))}disconnectedCallback(){super.disconnectedCallback(),this.#e.unsubscribe()}}const kl=`:host(:not([theme=dark])){--prism-maintext:#393a34;--prism-background:#f6f8fa;--prism-border:#ddd;--prism-cdata:#998;--prism-comment:var(--prism-cdata);--prism-doctype:var(--prism-cdata);--prism-prolog:var(--prism-cdata);--prism-attr-value:#e3116c;--prism-string:var(--prism-attr-value);--prism-boolean:#36acaa;--prism-entity:var(--prism-boolean);--prism-url:var(--prism-boolean);--prism-constant:var(--prism-boolean);--prism-inserted:var(--prism-boolean);--prism-number:var(--prism-boolean);--prism-property:var(--prism-boolean);--prism-regex:var(--prism-boolean);--prism-symbol:var(--prism-boolean);--prism-variable:var(--prism-boolean);--prism-atrule:#00a4db;--prism-attr-name:var(--prism-atrule);--prism-attr:var(--prism-atrule);--prism-operator:var(--prism-maintext);--prism-punctuation:var(--prism-maintext);--prism-deleted:#9a050f;--prism-function:var(--prism-deleted);--prism-function-variable:#6f42c1;--prism-selector:#00009f;--prism-tag:var(--prism-selector);--prism-keyword:var(--prism-selector)}:host([theme=dark]){--prism-maintext:#d3d0c8;--prism-background:#1d1f21;--prism-border:var(--mut-gray-200);--prism-cdata:#7c7c7c;--prism-comment:var(--prism-cdata);--prism-doctype:var(--prism-cdata);--prism-prolog:var(--prism-cdata);--prism-punctuation:#c5c8c6;--prism-tag:#96cbfe;--prism-property:var(--prism-tag);--prism-keyword:var(--prism-tag);--prism-class-name:#ffffb6;--prism-boolean:#9c9;--prism-constant:var(--prism-boolean);--prism-symbol:#f92672;--prism-deleted:var(--prism-symbol);--prism-number:#ff73fd;--prism-inserted:#a8ff60;--prism-selector:var(--prism-inserted);--prism-attr-name:var(--prism-inserted);--prism-string:var(--prism-inserted);--prism-char:var(--prism-inserted);--prism-builtin:var(--prism-inserted);--prism-variable:#c6c5fe;--prism-operator:#ededed;--prism-entity:#ffffb6;--prism-url:#96cbfe;--prism-attr-value:#f9ee98;--prism-atrule:var(--prism-attr-value);--prism-function:#dad085;--prism-regex:#e9c062;--prism-important:#fd971f}:host(:not([theme=dark])){--mut-file-ts-color:#498ba7;--mut-file-ts-test-color:#b7b73b;--mut-file-scala-color:#b8383d;--mut-file-java-color:#b8383d;--mut-file-js-color:#b7b73b;--mut-file-js-test-color:#cc6d2e;--mut-file-php-color:#9068b0;--mut-file-html-color:#498ba7;--mut-file-csharp-color:#498ba7;--mut-file-vue-color:#7fae42;--mut-file-gherkin-color:#00a818;--mut-file-svelte-color:#b8383d;--mut-file-rust-color:#627379}:host([theme=dark]){--mut-file-ts-color:#519aba;--mut-file-ts-test-color:#cbcb41;--mut-file-scala-color:#cc3e44;--mut-file-java-color:#cc3e44;--mut-file-js-color:#cbcb41;--mut-file-js-test-color:#e37933;--mut-file-php-color:#a074c4;--mut-file-html-color:#519aba;--mut-file-csharp-color:#519aba;--mut-file-vue-color:#8dc149;--mut-file-gherkin-color:#10b828;--mut-file-svelte-color:#cc3e44;--mut-file-rust-color:#6d8086}:host{--mut-squiggly-Survived:url("data:image/svg+xml;charset=UTF8,<svg xmlns='http://www.w3.org/2000/svg' height='3' width='6'><g fill='oklch(0.637 0.237 25.331)'><path d='m5.5 0-3 3H1.1l3-3z'/><path d='m4 0 2 2V.6L5.4 0zM0 2l1 1h1.4L0 .6z'/></g></svg>");--mut-squiggly-NoCoverage:url("data:image/svg+xml;charset=UTF8,<svg xmlns='http://www.w3.org/2000/svg' height='3' width='6'><g fill='oklch(0.75 0.183 55.934)'><path d='m5.5 0-3 3H1.1l3-3z'/><path d='m4 0 2 2V.6L5.4 0zM0 2l1 1h1.4L0 .6z'/></g></svg>");color:var(--c)}:host(:not([theme=dark])){--mut-octicon-icon-color:#498ba7;--mut-line-number:#6e7781;--mut-diff-add-bg:#e6ffec;--mut-diff-add-bg-line-number:#ccffd8;--mut-diff-add-line-number:#24292f;--mut-diff-del-bg:#ffebe9;--mut-diff-del-bg-line-number:#ffd7d5;--mut-diff-del-line-number:var(--mut-diff-add-line-number);--mut-badge-info-bg:#54c6ec;--mut-badge-info:#212529;--mut-code-lense:#919191}:host([theme=dark]){--lightningcss-light: ;--lightningcss-dark:initial;--lightningcss-light: ;--lightningcss-dark:initial;color-scheme:dark;--mut-octicon-icon-color:#519aba;--mut-line-number:#484f58;--mut-diff-add-bg:#2ea04326;--mut-diff-add-bg-line-number:#3fb9504d;--mut-diff-add-line-number:#c9d1d9;--mut-diff-del-bg:#f8514926;--mut-diff-del-bg-line-number:#f851494d;--mut-diff-del-line-number:#c9d1d9;--mut-badge-info-bg:#17a3b8;--mut-badge-info:#fff;--mut-code-lense:#999;--mut-white:var(--color-zinc-900);--mut-gray-50:var(--color-zinc-900);--mut-gray-100:var(--color-zinc-800);--mut-gray-200:var(--color-zinc-700);--mut-gray-300:var(--color-zinc-600);--mut-gray-400:var(--color-zinc-500);--mut-gray-500:var(--color-zinc-400);--mut-gray-600:var(--color-zinc-300);--mut-gray-700:var(--color-zinc-200);--mut-gray-800:var(--color-zinc-100);--mut-gray-900:var(--color-zinc-50);--mut-primary-100:var(--color-sky-800);--mut-primary-800:var(--color-sky-100);--mut-primary-900:var(--color-sky-50);--mut-primary-on:var(--color-sky-500)}`;var fa=Object.defineProperty,xl=Object.getOwnPropertyDescriptor,ga=e=>{throw TypeError(e)},$l=(e,t,r)=>t in e?fa(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ve=(e,t,r,n)=>{for(var i=n>1?void 0:n?xl(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&fa(t,r,i),i},Oe=(e,t,r)=>$l(e,typeof t!="symbol"?t+"":t,r),_l=(e,t,r)=>t.has(e)||ga("Cannot "+r),tn=(e,t,r)=>(_l(e,t,"read from private field"),r?r.call(e):t.get(e)),ma=(e,t,r)=>t.has(e)?ga("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),or,rn;const Sl=100;re.MutationTestReportAppComponent=class extends Fe{constructor(){super(),ma(this,or,new AbortController),Oe(this,"mutants",new Map),Oe(this,"tests",new Map),ma(this,rn,()=>{this.theme=this.getTheme()}),Oe(this,"themeSwitch",t=>{this.theme=t.detail,Ni()&&localStorage.setItem("mutation-testing-elements-theme",this.theme)}),Oe(this,"subscriptions",[]),Oe(this,"source"),Oe(this,"sseSubscriptions",new Set),Oe(this,"theMutant"),Oe(this,"theTest"),this.context={view:me.mutant,path:[]},this.path=[]}get themeBackgroundColor(){return getComputedStyle(this).getPropertyValue("--color-white")}get title(){return this.context.result?this.titlePostfix?`${this.context.result.name} - ${this.titlePostfix}`:this.context.result.name:""}firstUpdated(){(this.path.length===0||this.path[0]!==me.mutant&&this.path[0]!==me.test)&&window.location.replace(bt(`${me.mutant}`))}async loadData(){if(this.src)try{const t=await fetch(this.src);this.report=await t.json()}catch(t){const r=String(t);this.errorMessage=r}}willUpdate(t){this.report&&(this.theme??=this.getTheme(),t.has("report")&&this.updateModel(this.report),(t.has("path")||t.has("report"))&&(this.updateContext(),this.updateTitle())),t.has("src")&&this.loadData()}updated(t){t.has("theme")&&this.theme&&this.dispatchEvent(Ae("theme-changed",{theme:this.theme,themeBackgroundColor:this.themeBackgroundColor}))}getTheme(){const t=Ni()&&localStorage.getItem("mutation-testing-elements-theme");return t||(window.matchMedia?.("(prefers-color-scheme: dark)")?.matches?"dark":"light")}updateModel(t){this.rootModel=to(t),r((n,i)=>{n.result=i,n.mutants.forEach(a=>this.mutants.set(a.id,a))})(this.rootModel?.systemUnderTestMetrics),r((n,i)=>{n.result=i,n.tests.forEach(a=>this.tests.set(a.id,a))})(this.rootModel?.testMetrics),this.rootModel.systemUnderTestMetrics.updateParent(),this.rootModel.testMetrics?.updateParent();function r(n){return function i(a){a?.file&&n(a.file,a),a?.childResults.forEach(s=>{i(s)})}}}updateContext(){if(this.rootModel){const t=(n,i)=>i.reduce((a,s)=>a?.childResults.find(o=>o.name===s),n),r=this.path.slice(1);this.path[0]===me.test&&this.rootModel.testMetrics?this.context={view:me.test,path:r,result:t(this.rootModel.testMetrics,this.path.slice(1))}:this.context={view:me.mutant,path:r,result:t(this.rootModel.systemUnderTestMetrics,this.path.slice(1))}}}updateTitle(){document.title=this.title}connectedCallback(){super.connectedCallback(),window.matchMedia("(prefers-color-scheme: dark)").addEventListener?.("change",tn(this,rn),{signal:tn(this,or).signal}),this.subscriptions.push(wl.subscribe(t=>this.path=t)),this.initializeSse()}initializeSse(){if(!this.sse)return;this.source=new EventSource(this.sse);const t=It(this.source,"mutant-tested").subscribe(n=>{const i=JSON.parse(n.data);if(!this.report)return;const a=this.mutants.get(i.id);if(a!==void 0){this.theMutant=a;for(const[s,o]of Object.entries(i))this.theMutant[s]=o;i.killedBy&&i.killedBy.forEach(s=>{const o=this.tests.get(s);o!==void 0&&(this.theTest=o,o.addKilled(this.theMutant),this.theMutant.addKilledBy(o))}),i.coveredBy&&i.coveredBy.forEach(s=>{const o=this.tests.get(s);o!==void 0&&(this.theTest=o,o.addCovered(this.theMutant),this.theMutant.addCoveredBy(o))})}}),r=It(this.source,"mutant-tested").pipe(rl(Sl)).subscribe(()=>{this.applyChanges()});this.sseSubscriptions.add(t),this.sseSubscriptions.add(r),this.source.addEventListener("finished",()=>{this.source?.close(),this.applyChanges(),this.sseSubscriptions.forEach(n=>n.unsubscribe())})}applyChanges(){this.theMutant?.update(),this.theTest?.update(),pa.next()}disconnectedCallback(){super.disconnectedCallback(),tn(this,or).abort(),this.subscriptions.forEach(t=>t.unsubscribe())}renderTitle(){return this.context.result?b`
        <h1 class="mt-4 text-5xl font-bold tracking-tight">
          ${this.context.result.name}${this.titlePostfix?b`<small class="text-light-muted ml-4 font-light">${this.titlePostfix}</small>`:P}
        </h1>
      `:P}render(){return this.context.result??this.errorMessage?b`
        <mte-file-picker .rootModel="${this.rootModel}"></mte-file-picker>
        <div class="container space-y-4 bg-white pb-4 font-sans text-gray-800 transition-colors motion-safe:transition-max-width">
          ${this.renderErrorMessage()}
          <mte-theme-switch @theme-switch="${this.themeSwitch}" class="sticky top-offset z-20 float-right mb-0 pt-7" .theme="${this.theme}">
          </mte-theme-switch>
          ${this.renderTitle()} ${this.renderTabs()}
          <mte-breadcrumb
            @mte-file-picker-open="${()=>this.filePicker.open()}"
            .view="${this.context.view}"
            .path="${this.context.path}"
          ></mte-breadcrumb>
          <mte-result-status-bar
            detected="${Ft(this.rootModel?.systemUnderTestMetrics.metrics.totalDetected)}"
            no-coverage="${Ft(this.rootModel?.systemUnderTestMetrics.metrics.noCoverage)}"
            pending="${Ft(this.rootModel?.systemUnderTestMetrics.metrics.pending)}"
            survived="${Ft(this.rootModel?.systemUnderTestMetrics.metrics.survived)}"
            total="${Ft(this.rootModel?.systemUnderTestMetrics.metrics.totalValid)}"
          ></mte-result-status-bar>
          ${this.context.view==="mutant"&&this.context.result?b`<mte-mutant-view
                id="mte-mutant-view"
                .result="${this.context.result}"
                .thresholds="${this.report.thresholds}"
                .path="${this.path}"
              ></mte-mutant-view>`:P}
          ${this.context.view==="test"&&this.context.result?b`<mte-test-view id="mte-test-view" .result="${this.context.result}" .path="${this.path}"></mte-test-view>`:P}
        </div>
      `:P}renderErrorMessage(){return this.errorMessage?b`<div class="my-4 rounded-lg bg-red-100 p-4 text-sm text-red-700" role="alert">${this.errorMessage}</div>`:P}renderTabs(){if(this.rootModel?.testMetrics){const t=this.context.view==="mutant",r=this.context.view==="test";return b`
        <nav class="border-b border-gray-200 text-center text-sm font-medium text-gray-600">
          <ul class="-mb-px flex flex-wrap" role="tablist">
            ${[{type:"mutant",isActive:t,text:"👽 Mutants"},{type:"test",isActive:r,text:"🧪 Tests"}].map(({type:n,isActive:i,text:a})=>b`<li class="mr-2" role="presentation">
                  <a
                    class="inline-block rounded-t-lg border-b-2 border-transparent p-4 transition-colors hover:border-gray-300 hover:bg-gray-200 hover:text-gray-700 aria-selected:border-b-[3px] aria-selected:border-solid aria-selected:border-primary-700 aria-selected:text-primary-on"
                    role="tab"
                    href="${bt(n)}"
                    aria-selected="${i}"
                    aria-controls="mte-${n}-view"
                    >${a}</a
                  >
                </li>`)}
          </ul>
        </nav>
      `}else return P}},or=new WeakMap,rn=new WeakMap,Oe(re.MutationTestReportAppComponent,"styles",[Te(kl),ze]),ve([I({attribute:!1})],re.MutationTestReportAppComponent.prototype,"report",2),ve([I({attribute:!1})],re.MutationTestReportAppComponent.prototype,"rootModel",2),ve([I()],re.MutationTestReportAppComponent.prototype,"src",2),ve([I()],re.MutationTestReportAppComponent.prototype,"sse",2),ve([I({attribute:!1})],re.MutationTestReportAppComponent.prototype,"errorMessage",2),ve([I({attribute:!1})],re.MutationTestReportAppComponent.prototype,"context",2),ve([I({type:Array})],re.MutationTestReportAppComponent.prototype,"path",2),ve([I({attribute:"title-postfix"})],re.MutationTestReportAppComponent.prototype,"titlePostfix",2),ve([I({reflect:!0})],re.MutationTestReportAppComponent.prototype,"theme",2),ve([I({attribute:!1})],re.MutationTestReportAppComponent.prototype,"themeBackgroundColor",1),ve([Xt("mte-file-picker")],re.MutationTestReportAppComponent.prototype,"filePicker",2),ve([I()],re.MutationTestReportAppComponent.prototype,"title",1),re.MutationTestReportAppComponent=ve([se("mutation-test-report-app")],re.MutationTestReportAppComponent);/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */function*Ve(e,t){if(e!==void 0){let r=0;for(const n of e)yield t(n,r++)}}var ye=(e=>(e.csharp="cs",e.java="java",e.javascript="javascript",e.html="html",e.php="php",e.scala="scala",e.typescript="typescript",e.vue="vue",e.gherkin="gherkin",e.svelte="svelte",e.rust="rust",e))(ye||{});function Al(e){return e.substr(e.lastIndexOf(".")+1).toLocaleLowerCase()}function nn(e){switch(Al(e)){case"cs":return"cs";case"html":return"html";case"java":return"java";case"js":case"cjs":case"mjs":return"javascript";case"ts":case"tsx":case"cts":case"mts":return"typescript";case"sc":case"sbt":case"scala":return"scala";case"php":return"php";case"vue":return"vue";case"feature":return"gherkin";case"svelte":return"svelte";case"rs":return"rust";default:return}}function an(e,t){const r=nn(t)??"plain";let n=r;return r==="vue"&&(n="html"),qi.highlight(e,qi.languages[n],n)}function sn(e,t){let r=[];const n=[],i={column:0,line:1,offset:-1},a=[];let s=!1,o=0;for(;o<e.length;){switch(s&&!lr(e[o])&&(u(),s=!1),e[o]){case"\r":i.offset++;break;case`
`:$(),i.offset++,i.line++,i.column=0,s=!0;break;case"<":{const d=C();d.isClosing?H(d):z(d);break}case"&":y(m());break;default:y(e[o]);break}o++}return $(),n;function l(...d){r.push(...d)}function u(){a.forEach(d=>l(v(d)))}function x(){a.forEach(d=>l(v({...d,isClosing:!0})))}function v({attributes:d,elementName:g,isClosing:_}){return _?`</${g}>`:`<${g}${Object.entries(d??{}).reduce((O,[E,Z])=>Z===void 0?`${O} ${E}`:`${O} ${E}="${Z}"`,"")}>`}function $(){x(),n.push(r.join("")),r=[]}function y(d){if(i.column++,i.offset++,t)for(const g of t(i))g.isClosing?H(g):(l(v(g)),a.push(g));l(d)}function C(){o++;const d=e[o]==="/"?!0:void 0;d&&o++;const g=o;for(;!lr(e[o])&&e[o]!==">";)o++;const _=e.substring(g,o),O=X();return{elementName:_,attributes:O,isClosing:d}}function z(d){a.push(d),l(v(d))}function H(d){let g;for(g=a.length-1;g>=0;g--){const _=a[g];if(d.elementName===_.elementName&&_.id===d.id){l(v(d)),a.splice(g,1);for(let O=g;O<a.length;O++)l(v(a[O]));break}l(v({..._,isClosing:!0}))}if(g===-1)throw new Error(`Cannot find corresponding opening tag for ${v(d)}`)}function X(){const d=Object.create(null);for(;o<e.length;){const g=e[o];if(g===">")return d;if(!lr(g)){const{name:_,value:O}=p();d[_]=O}o++}throw new Error(`Missing closing tag near ${e.substr(o-10)}`)}function p(){const d=o;for(;e[o]!=="=";)o++;const g=e.substring(d,o);o++;const _=h();return{name:g,value:_}}function h(){e[o]==='"'&&o++;const d=o;for(;e[o]!=='"';)o++;return e.substring(d,o)}function m(){const d=o;for(;e[o]!==";";)o++;return e.substring(d,o+1)}}function lr(e){return e===`
`||e===" "||e==="	"}function Cl(e,t){let r=0,n=t.length-1;for(;e[r]===t[r]&&r<t.length;)r++;const i=e.length-t.length;for(;e[n+i]===t[n]&&n>r;)n--;n===r&&(lr(t[r-1])||r--),n++;const a=t.substring(r,n);return["true","false"].forEach(s=>{a===s.substr(0,s.length-1)&&s.endsWith(t[n])&&n++,a===s.substr(1,s.length)&&s.startsWith(t[r-1])&&r--}),[r,n]}function cr(e,t){return e.line>t.line||e.line===t.line&&e.column>=t.column}const Ml='#report-code-block{background:var(--prism-background);border:1px solid var(--prism-border);overflow:auto visible}.line-numbers{counter-reset:mte-line-number}.line .line-number{text-align:right;color:var(--mut-line-number);counter-increment:mte-line-number;padding:0 10px 0 15px}.line .line-number:before{content:counter(mte-line-number)}.line-marker:before{content:" ";padding:0 5px}.NoCoverage{--mut-status-color:var(--color-orange-500);--mut-squiggly-line:var(--mut-squiggly-NoCoverage)}.Survived{--mut-status-color:var(--color-red-500);--mut-squiggly-line:var(--mut-squiggly-Survived)}.Pending{--mut-status-color:var(--color-neutral-400)}.Killed{--mut-status-color:var(--color-green-600)}.Timeout{--mut-status-color:var(--color-amber-400)}.CompileError,.RuntimeError,.Ignored{--mut-status-color:var(--color-neutral-400)}svg.mutant-dot{fill:var(--mut-status-color)}.mte-selected-Pending .mutant.Pending,.mte-selected-Killed .mutant.Killed,.mte-selected-Timeout .mutant.Timeout,.mte-selected-CompileError .mutant.CompileError,.mte-selected-RuntimeError .mutant.RuntimeError,.mte-selected-Ignored .mutant.Ignored{-webkit-text-decoration:solid underline var(--mut-status-color)2px;-webkit-text-decoration:solid underline var(--mut-status-color)2px;text-decoration:solid underline var(--mut-status-color)2px;-webkit-text-decoration-skip-ink:none;text-decoration-skip-ink:none;text-underline-offset:3px;cursor:pointer}.mte-selected-Survived .mutant.Survived,.mte-selected-NoCoverage .mutant.NoCoverage{border-bottom-style:solid;border-image-slice:0 0 4;border-image-width:4px;border-image-outset:6px;border-image-repeat:repeat;border-image-source:var(--mut-squiggly-line);cursor:pointer}:is(.mte-selected-Survived .mutant.Survived,.mte-selected-NoCoverage .mutant.NoCoverage) .mutant.Survived,:is(.mte-selected-Survived .mutant.Survived,.mte-selected-NoCoverage .mutant.NoCoverage) .mutant.NoCoverage{border-bottom-style:none;border-image-source:none;text-decoration-line:none}.diff-old{background-color:var(--mut-diff-del-bg)}.diff-focus{background-color:var(--mut-diff-add-bg-line-number)}.diff-old .line-number{background-color:var(--mut-diff-del-bg-line-number);color:var(--mut-diff-del-line-number)}.diff-old .line-marker:before{content:"-"}.diff-new{background-color:var(--mut-diff-add-bg)}.diff-new .empty-line-number{background-color:var(--mut-diff-add-bg-line-number);color:var(--mut-diff-add-line-number)}.diff-new .line-marker:before{content:"+"}';/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */class on extends Zr{constructor(t){if(super(t),this.it=P,t.type!==Wr.CHILD)throw Error(this.constructor.directiveName+"() can only be used in child bindings")}render(t){if(t===P||t==null)return this._t=void 0,this.it=t;if(t===Pe)return t;if(typeof t!="string")throw Error(this.constructor.directiveName+"() called with a non-string value");if(t===this.it)return this._t;this.it=t;const r=[t];return r.raw=r,this._t={_$litType$:this.constructor.resultType,strings:r,values:[]}}}on.directiveName="unsafeHTML",on.resultType=1;const El=Kr(on);function va(e,t){return e===P&&t===P?P:b`<span class="ml-1 flex flex-row items-center">${e}${t}</span>`}function ba(e,t){return b`<tr class="line"
    ><td class="line-number"></td><td class="line-marker"></td><td class="code flex"><span>${El(e)}</span>${t}</td></tr
  >`}const ya="M 0,5 C 0,-1.66 10,-1.66 10,5 10,7.76 7.76,10 5,10 2.24,10 0,7.76 0,5 Z",wa="M 0,0 C 0,0 10,0 10,0 10,0 5,10 5,10 5,10 0,0 0,0 Z",Tl="0.4 0 0.2 1",ka=(e,t,r)=>ee`<path stroke-opacity="${r}" class="transition-stroke-opacity stroke-gray-800" d="${t}">
    <animate values="${e};${t}" attributeName="d" dur="0.2s" begin="indefinite" calcMode="spline" keySplines="${Tl}" />
  </path>`,xa=ka(ya,wa,1),$a=ka(wa,ya,0);function _a(e,t,r){e?.querySelector(`[${t}="${encodeURIComponent(r)}"] path animate`)?.beginElement()}var Sa=Object.defineProperty,Pl=Object.getOwnPropertyDescriptor,Aa=e=>{throw TypeError(e)},zl=(e,t,r)=>t in e?Sa(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,We=(e,t,r,n)=>{for(var i=n>1?void 0:n?Pl(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&Sa(t,r,i),i},jt=(e,t,r)=>zl(e,typeof t!="symbol"?t+"":t,r),ln=(e,t,r)=>t.has(e)||Aa("Cannot "+r),cn=(e,t,r)=>(ln(e,t,"read from private field"),t.get(e)),un=(e,t,r)=>t.has(e)?Aa("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Fl=(e,t,r,n)=>(ln(e,t,"write to private field"),t.set(e,r),r),Ca=(e,t,r)=>(ln(e,t,"access private method"),r),Lt,dn,ur,hn;const pn="diff-old",Ma="diff-new";let Ce=class extends Fe{constructor(){super(),un(this,ur),un(this,Lt),un(this,dn,e=>{e.key==="Escape"&&this.selectedMutant&&this.toggleMutant(this.selectedMutant)}),jt(this,"filtersChanged",e=>{this.selectedMutantStates=e.detail.concat(["Pending"])}),jt(this,"codeClicked",e=>{if(e.stopPropagation(),e.target instanceof Element){let t=e.target;const r=[];for(;t instanceof Element;t=t.parentElement){const i=t.getAttribute("mutant-id"),a=this.mutants.find(({id:s})=>s.toString()===i);a&&r.push(a)}const n=(this.selectedMutant?r.indexOf(this.selectedMutant):-1)+1;r[n]?(this.toggleMutant(r[n]),Ta()):this.selectedMutant&&(this.toggleMutant(this.selectedMutant),Ta())}}),jt(this,"nextMutant",()=>{const e=this.selectedMutant?(this.mutants.indexOf(this.selectedMutant)+1)%this.mutants.length:0;this.mutants[e]&&this.toggleMutant(this.mutants[e])}),jt(this,"previousMutant",()=>{const e=this.selectedMutant?(this.mutants.indexOf(this.selectedMutant)+this.mutants.length-1)%this.mutants.length:this.mutants.length-1;this.mutants[e]&&this.toggleMutant(this.mutants[e])}),this.filters=[],this.selectedMutantStates=[],this.lines=[],this.mutants=[],Fl(this,Lt,new AbortController)}connectedCallback(){super.connectedCallback(),window.addEventListener("keydown",cn(this,dn),{signal:cn(this,Lt).signal})}disconnectedCallback(){cn(this,Lt).abort(),super.disconnectedCallback()}render(){const e=new Map;for(const r of this.mutants){let n=e.get(r.location.start.line);n||(n=[],e.set(r.location.start.line,n)),n.push(r)}const t=r=>this.renderMutantDots([...e.entries()].filter(([n])=>n>r).flatMap(([,n])=>n));return b`
      <mte-state-filter
        allow-toggle-all
        .filters="${this.filters}"
        @filters-changed="${this.filtersChanged}"
        @next=${this.nextMutant}
        @previous=${this.previousMutant}
      ></mte-state-filter>
      <pre
        @click="${this.codeClicked}"
        id="report-code-block"
        class="line-numbers ${this.selectedMutantStates.map(r=>`mte-selected-${r}`).join(" ")} flex rounded-md py-4"
      >
        <code class="flex language-${this.model.language}">
          <table>${Ve(this.lines,(r,n)=>{const i=n+1,a=this.renderMutantDots(e.get(i)),s=this.lines.length===i?t(i):P;return ba(r,va(a,s))})}</table>
          </code>
          </pre>
    `}renderMutantDots(e){return e?.length?e.map(t=>ee`<svg mutant-id="${t.id}" class="mutant-dot mx-0.5 cursor-pointer ${this.selectedMutant?.id===t.id?"selected":""} ${t.status}" height="10" width="12">
              <title>${Ea(t)}</title>
              ${this.selectedMutant?.id===t.id?xa:$a}
          </svg>`):P}toggleMutant(e){if(this.removeCurrentDiff(),Ca(this,ur,hn).call(this,e),this.selectedMutant===e){this.selectedMutant=void 0,this.dispatchEvent(Ae("mutant-selected",{selected:!1,mutant:e}));return}else this.selectedMutant&&Ca(this,ur,hn).call(this,this.selectedMutant);this.selectedMutant=e;const t=this.code.querySelectorAll("tr.line");for(let i=e.location.start.line-1;i<e.location.end.line;i++)t.item(i).classList.add(pn);const r=this.highlightedReplacementRows(e),n=t.item(e.location.end.line-1);n.insertAdjacentHTML("afterend",r),ha(n),this.dispatchEvent(Ae("mutant-selected",{selected:!0,mutant:e}))}removeCurrentDiff(){const e=this.code;e.querySelectorAll(`.${pn}`).forEach(n=>n.classList.remove(pn)),e.querySelectorAll(`.${Ma}`).forEach(n=>n.remove())}reactivate(){super.reactivate(),this.updateFileRepresentation()}update(e){e.has("model")&&this.model&&this.updateFileRepresentation(),(e.has("model")&&this.model||e.has("selectedMutantStates"))&&(this.mutants=this.model.mutants.filter(t=>this.selectedMutantStates.includes(t.status)).sort((t,r)=>cr(t.location.start,r.location.start)?1:-1),this.selectedMutant&&!this.mutants.includes(this.selectedMutant)&&e.has("selectedMutantStates")&&this.selectedMutantsHaveChanged(e.get("selectedMutantStates")??[])&&this.toggleMutant(this.selectedMutant)),super.update(e)}updateFileRepresentation(){this.filters=["Killed","Survived","NoCoverage","Ignored","Timeout","CompileError","RuntimeError"].filter(n=>this.model.mutants.some(i=>i.status===n)).map(n=>({enabled:[...this.selectedMutantStates,"Survived","NoCoverage","Timeout"].includes(n),count:this.model.mutants.filter(i=>i.status===n).length,status:n,label:b`${ca(n)} ${n}`,context:vl(n)}));const e=an(this.model.source,this.model.name),t=new Set,r=new Set(this.model.mutants);this.lines=sn(e,function*(n){for(const i of t)cr(n,i.location.end)&&(t.delete(i),yield{elementName:"span",id:i.id,isClosing:!0});for(const i of r)cr(n,i.location.start)&&(t.add(i),r.delete(i),yield{elementName:"span",id:i.id,attributes:{class:en(`mutant border-none ${i.status}`),title:en(Ea(i)),"mutant-id":en(i.id.toString())}})})}selectedMutantsHaveChanged(e){return e.length!==this.selectedMutantStates.length?!0:!e.every((t,r)=>this.selectedMutantStates[r]===t)}highlightedReplacementRows(e){const t=e.getMutatedLines().trimEnd(),r=e.getOriginalLines().trimEnd(),[n,i]=Cl(r,t),a=sn(an(t,this.model.name),function*({offset:l}){l===n?yield{elementName:"span",id:"diff-focus",attributes:{class:"diff-focus"}}:l===i&&(yield{elementName:"span",id:"diff-focus",isClosing:!0})}),s=`<tr class="${Ma}"><td class="empty-line-number"></td><td class="line-marker"></td><td class="code">`,o="</td></tr>";return a.map(l=>`${s}${l}${o}`).join("")}};Lt=new WeakMap,dn=new WeakMap,ur=new WeakSet,hn=function(e){_a(this.code,"mutant-id",e.id)},jt(Ce,"styles",[ra,ze,Te(Ml)]),We([he()],Ce.prototype,"filters",2),We([I({attribute:!1})],Ce.prototype,"model",2),We([he()],Ce.prototype,"selectedMutantStates",2),We([he()],Ce.prototype,"selectedMutant",2),We([he()],Ce.prototype,"lines",2),We([he()],Ce.prototype,"mutants",2),We([Xt("code")],Ce.prototype,"code",2),Ce=We([se("mte-file")],Ce);function Ea(e){return`${e.mutatorName} ${e.status}`}function Ta(){window.getSelection()?.removeAllRanges()}var dr={exports:{}},Ol=dr.exports,Pa;function Il(){return Pa||(Pa=1,function(e){((t,r)=>{e.exports?e.exports=r():t.fuzzysort=r()})(Ol,t=>{var r=(f,c)=>{if(!f||!c)return j;var w=C(f);xe(c)||(c=y(c));var A=w.bitflags;return(A&c._bitflags)!==A?j:H(w,c)},n=(f,c,w)=>{if(!f)return w?.all?z(c,w):G;var A=C(f),M=A.bitflags,S=A.containsSpace,k=v(w?.threshold||0),F=w?.limit||je,T=0,R=0,L=c.length;function ge(lt){T<F?(At.add(lt),++T):(++R,lt._score>At.peek()._score&&At.replaceTop(lt))}if(w?.key)for(var oe=w.key,U=0;U<L;++U){var de=c[U],V=Be(de,oe);if(V&&(xe(V)||(V=y(V)),(M&V._bitflags)===M)){var Y=H(A,V);Y!==j&&(Y._score<k||(Y.obj=de,ge(Y)))}}else if(w?.keys){var Ze=w.keys,$e=Ze.length;e:for(var U=0;U<L;++U){var de=c[U];{for(var le=0,N=0;N<$e;++N){var oe=Ze[N],V=Be(de,oe);if(!V){Ee[N]=ce;continue}xe(V)||(V=y(V)),Ee[N]=V,le|=V._bitflags}if((M&le)!==M)continue}if(S)for(let W=0;W<A.spaceSearches.length;W++)q[W]=B;for(var N=0;N<$e;++N){if(V=Ee[N],V===ce){ke[N]=ce;continue}if(ke[N]=H(A,V,!1,S),ke[N]===j){ke[N]=ce;continue}if(S)for(let J=0;J<A.spaceSearches.length;J++){if(ie[J]>-1e3&&q[J]>B){var ae=(q[J]+ie[J])/4;ae>q[J]&&(q[J]=ae)}ie[J]>q[J]&&(q[J]=ie[J])}}if(S){for(let W=0;W<A.spaceSearches.length;W++)if(q[W]===B)continue e}else{var D=!1;for(let W=0;W<$e;W++)if(ke[W]._score!==B){D=!0;break}if(!D)continue}var be=new l($e);for(let W=0;W<$e;W++)be[W]=ke[W];if(S){var te=0;for(let W=0;W<A.spaceSearches.length;W++)te+=q[W]}else{var te=B;for(let J=0;J<$e;J++){var Y=be[J];if(Y._score>-1e3&&te>B){var ae=(te+Y._score)/4;ae>te&&(te=ae)}Y._score>te&&(te=Y._score)}}if(be.obj=de,be._score=te,w?.scoreFn){if(te=w.scoreFn(be),!te)continue;te=v(te),be._score=te}te<k||ge(be)}}else for(var U=0;U<L;++U){var V=c[U];if(V&&(xe(V)||(V=y(V)),(M&V._bitflags)===M)){var Y=H(A,V);Y!==j&&(Y._score<k||ge(Y))}}if(T===0)return G;for(var Ge=new Array(T),U=T-1;U>=0;--U)Ge[U]=At.poll();return Ge.total=T+R,Ge},i=(f,c="<b>",w="</b>")=>{for(var A=typeof c=="function"?c:void 0,M=f.target,S=M.length,k=f.indexes,F="",T=0,R=0,L=!1,ge=[],oe=0;oe<S;++oe){var U=M[oe];if(k[R]===oe){if(++R,L||(L=!0,A?(ge.push(F),F=""):F+=c),R===k.length){A?(F+=U,ge.push(A(F,T++)),F="",ge.push(M.substr(oe+1))):F+=U+w+M.substr(oe+1);break}}else L&&(L=!1,A?(ge.push(A(F,T++)),F=""):F+=w);F+=U}return A?ge:F},a=f=>{typeof f=="number"?f=""+f:typeof f!="string"&&(f="");var c=h(f);return u(f,{_targetLower:c._lower,_targetLowerCodes:c.lowerCodes,_bitflags:c.bitflags})},s=()=>{g.clear(),_.clear()};class o{get indexes(){return this._indexes.slice(0,this._indexes.len).sort((c,w)=>c-w)}set indexes(c){return this._indexes=c}highlight(c,w){return i(this,c,w)}get score(){return x(this._score)}set score(c){this._score=v(c)}}class l extends Array{get score(){return x(this._score)}set score(c){this._score=v(c)}}var u=(f,c)=>{const w=new o;return w.target=f,w.obj=c.obj??j,w._score=c._score??B,w._indexes=c._indexes??[],w._targetLower=c._targetLower??"",w._targetLowerCodes=c._targetLowerCodes??j,w._nextBeginningIndexes=c._nextBeginningIndexes??j,w._bitflags=c._bitflags??0,w},x=f=>f===B?0:f>1?f:Math.E**(((-f+1)**.04307-1)*-2),v=f=>f===0?B:f>1?f:1-Math.pow(Math.log(f)/-2+1,1/.04307),$=f=>{typeof f=="number"?f=""+f:typeof f!="string"&&(f=""),f=f.trim();var c=h(f),w=[];if(c.containsSpace){var A=f.split(/\s+/);A=[...new Set(A)];for(var M=0;M<A.length;M++)if(A[M]!==""){var S=h(A[M]);w.push({lowerCodes:S.lowerCodes,_lower:A[M].toLowerCase(),containsSpace:!1})}}return{lowerCodes:c.lowerCodes,_lower:c._lower,containsSpace:c.containsSpace,bitflags:c.bitflags,spaceSearches:w}},y=f=>{if(f.length>999)return a(f);var c=g.get(f);return c!==void 0||(c=a(f),g.set(f,c)),c},C=f=>{if(f.length>999)return $(f);var c=_.get(f);return c!==void 0||(c=$(f),_.set(f,c)),c},z=(f,c)=>{var w=[];w.total=f.length;var A=c?.limit||je;if(c?.key)for(var M=0;M<f.length;M++){var S=f[M],k=Be(S,c.key);if(k!=j){xe(k)||(k=y(k));var F=u(k.target,{_score:k._score,obj:S});if(w.push(F),w.length>=A)return w}}else if(c?.keys)for(var M=0;M<f.length;M++){for(var S=f[M],T=new l(c.keys.length),R=c.keys.length-1;R>=0;--R){var k=Be(S,c.keys[R]);if(!k){T[R]=ce;continue}xe(k)||(k=y(k)),k._score=B,k._indexes.len=0,T[R]=k}if(T.obj=S,T._score=B,w.push(T),w.length>=A)return w}else for(var M=0;M<f.length;M++){var k=f[M];if(k!=j&&(xe(k)||(k=y(k)),k._score=B,k._indexes.len=0,w.push(k),w.length>=A))return w}return w},H=(f,c,w=!1,A=!1)=>{if(w===!1&&f.containsSpace)return X(f,c,A);for(var M=f._lower,S=f.lowerCodes,k=S[0],F=c._targetLowerCodes,T=S.length,R=F.length,U=0,L=0,ge=0;;){var oe=k===F[L];if(oe){if(O[ge++]=L,++U,U===T)break;k=S[U]}if(++L,L>=R)return j}var U=0,de=!1,V=0,Y=c._nextBeginningIndexes;Y===j&&(Y=c._nextBeginningIndexes=d(c.target)),L=O[0]===0?0:Y[O[0]-1];var Ze=0;if(L!==R)for(;;)if(L>=R){if(U<=0||(++Ze,Ze>200))break;--U;var $e=E[--V];L=Y[$e]}else{var oe=S[U]===F[L];if(oe){if(E[V++]=L,++U,U===T){de=!0;break}++L}else L=Y[L]}var le=T<=1?-1:c._targetLower.indexOf(M,O[0]),N=!!~le,ae=N?le===0||c._nextBeginningIndexes[le-1]===le:!1;if(N&&!ae){for(var D=0;D<Y.length;D=Y[D])if(!(D<=le)){for(var be=0;be<T&&S[be]===c._targetLowerCodes[D+be];be++);if(be===T){le=D,ae=!0;break}}}var te=W=>{for(var J=0,bs=0,Le=1;Le<T;++Le)W[Le]-W[Le-1]!==1&&(J-=W[Le],++bs);var Fc=W[T-1]-W[0]-(T-1);if(J-=(12+Fc)*bs,W[0]!==0&&(J-=W[0]*W[0]*.2),!de)J*=1e3;else{for(var jn=1,Le=Y[0];Le<R;Le=Y[Le])++jn;jn>24&&(J*=(jn-24)*10)}return J-=(R-T)/2,N&&(J/=1+T*T*1),ae&&(J/=1+T*T*1),J-=(R-T)/2,J};if(de)if(ae){for(var D=0;D<T;++D)O[D]=le+D;var Ge=O,lt=te(O)}else var Ge=E,lt=te(E);else{if(N)for(var D=0;D<T;++D)O[D]=le+D;var Ge=O,lt=te(Ge)}c._score=lt;for(var D=0;D<T;++D)c._indexes[D]=Ge[D];c._indexes.len=T;const Sr=new o;return Sr.target=c.target,Sr._score=c._score,Sr._indexes=c._indexes,Sr},X=(f,c,w)=>{for(var A=new Set,M=0,S=j,k=0,F=f.spaceSearches,T=F.length,R=0,L=()=>{for(let ae=R-1;ae>=0;ae--)c._nextBeginningIndexes[Z[ae*2+0]]=Z[ae*2+1]},ge=!1,N=0;N<T;++N){ie[N]=B;var oe=F[N];if(S=H(oe,c),w){if(S===j)continue;ge=!0}else if(S===j)return L(),j;var U=N===T-1;if(!U){var de=S._indexes,V=!0;for(let D=0;D<de.len-1;D++)if(de[D+1]-de[D]!==1){V=!1;break}if(V){var Y=de[de.len-1]+1,Ze=c._nextBeginningIndexes[Y-1];for(let D=Y-1;D>=0&&Ze===c._nextBeginningIndexes[D];D--)c._nextBeginningIndexes[D]=Y,Z[R*2+0]=D,Z[R*2+1]=Ze,R++}}M+=S._score/T,ie[N]=S._score/T,S._indexes[0]<k&&(M-=(k-S._indexes[0])*2),k=S._indexes[0];for(var $e=0;$e<S._indexes.len;++$e)A.add(S._indexes[$e])}if(w&&!ge)return j;L();var le=H(f,c,!0);if(le!==j&&le._score>M){if(w)for(var N=0;N<T;++N)ie[N]=le._score/T;return le}w&&(S=c),S._score=M;var N=0;for(let ae of A)S._indexes[N++]=ae;return S._indexes.len=N,S},p=f=>f.replace(new RegExp("\\p{Script=Latin}+","gu"),c=>c.normalize("NFD")).replace(/[\u0300-\u036f]/g,""),h=f=>{f=p(f);for(var c=f.length,w=f.toLowerCase(),A=[],M=0,S=!1,k=0;k<c;++k){var F=A[k]=w.charCodeAt(k);if(F===32){S=!0;continue}var T=F>=97&&F<=122?F-97:F>=48&&F<=57?26:F<=127?30:31;M|=1<<T}return{lowerCodes:A,bitflags:M,containsSpace:S,_lower:w}},m=f=>{for(var c=f.length,w=[],A=0,M=!1,S=!1,k=0;k<c;++k){var F=f.charCodeAt(k),T=F>=65&&F<=90,R=T||F>=97&&F<=122||F>=48&&F<=57,L=T&&!M||!S||!R;M=T,S=R,L&&(w[A++]=k)}return w},d=f=>{f=p(f);for(var c=f.length,w=m(f),A=[],M=w[0],S=0,k=0;k<c;++k)M>k?A[k]=M:(M=w[++S],A[k]=M===void 0?c:M);return A},g=new Map,_=new Map,O=[],E=[],Z=[],q=[],ie=[],Ee=[],ke=[],Be=(f,c)=>{var w=f[c];if(w!==void 0)return w;if(typeof c=="function")return c(f);var A=c;Array.isArray(c)||(A=c.split("."));for(var M=A.length,S=-1;f&&++S<M;)f=f[A[S]];return f},xe=f=>typeof f=="object"&&typeof f._bitflags=="number",je=1/0,B=-je,G=[];G.total=0;var j=null,ce=a(""),ue=f=>{var c=[],w=0,A={},M=S=>{for(var k=0,F=c[k],T=1;T<w;){var R=T+1;k=T,R<w&&c[R]._score<c[T]._score&&(k=R),c[k-1>>1]=c[k],T=1+(k<<1)}for(var L=k-1>>1;k>0&&F._score<c[L]._score;L=(k=L)-1>>1)c[k]=c[L];c[k]=F};return A.add=S=>{var k=w;c[w++]=S;for(var F=k-1>>1;k>0&&S._score<c[F]._score;F=(k=F)-1>>1)c[k]=c[F];c[k]=S},A.poll=S=>{if(w!==0){var k=c[0];return c[0]=c[--w],M(),k}},A.peek=S=>{if(w!==0)return c[0]},A.replaceTop=S=>{c[0]=S,M()},A},At=ue();return{single:r,go:n,prepare:a,cleanup:s}})}(dr)),dr.exports}var Rl=Il();const fn=al(Rl);/**
 * @license
 * Copyright 2020 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const{I:Dl}=Ds,za=()=>document.createComment(""),Nt=(e,t,r)=>{const n=e._$AA.parentNode,i=t===void 0?e._$AB:t._$AA;if(r===void 0){const a=n.insertBefore(za(),i),s=n.insertBefore(za(),i);r=new Dl(a,s,e,e.options)}else{const a=r._$AB.nextSibling,s=r._$AM,o=s!==e;if(o){let l;r._$AQ?.(e),r._$AM=e,r._$AP!==void 0&&(l=e._$AU)!==s._$AU&&r._$AP(l)}if(a!==i||o){let l=r._$AA;for(;l!==a;){const u=l.nextSibling;n.insertBefore(l,i),l=u}}}return r},nt=(e,t,r=e)=>(e._$AI(t,r),e),Bl={},jl=(e,t=Bl)=>e._$AH=t,Ll=e=>e._$AH,gn=e=>{e._$AP?.(!1,!0);let t=e._$AA;const r=e._$AB.nextSibling;for(;t!==r;){const n=t.nextSibling;t.remove(),t=n}};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Fa=(e,t,r)=>{const n=new Map;for(let i=t;i<=r;i++)n.set(e[i],i);return n},yt=Kr(class extends Zr{constructor(e){if(super(e),e.type!==Wr.CHILD)throw Error("repeat() can only be used in text expressions")}dt(e,t,r){let n;r===void 0?r=t:t!==void 0&&(n=t);const i=[],a=[];let s=0;for(const o of e)i[s]=n?n(o,s):s,a[s]=r(o,s),s++;return{values:a,keys:i}}render(e,t,r){return this.dt(e,t,r).values}update(e,[t,r,n]){const i=Ll(e),{values:a,keys:s}=this.dt(t,r,n);if(!Array.isArray(i))return this.ut=s,a;const o=this.ut??=[],l=[];let u,x,v=0,$=i.length-1,y=0,C=a.length-1;for(;v<=$&&y<=C;)if(i[v]===null)v++;else if(i[$]===null)$--;else if(o[v]===s[y])l[y]=nt(i[v],a[y]),v++,y++;else if(o[$]===s[C])l[C]=nt(i[$],a[C]),$--,C--;else if(o[v]===s[C])l[C]=nt(i[v],a[C]),Nt(e,l[C+1],i[v]),v++,C--;else if(o[$]===s[y])l[y]=nt(i[$],a[y]),Nt(e,i[v],i[$]),$--,y++;else if(u===void 0&&(u=Fa(s,y,C),x=Fa(o,v,$)),u.has(o[v]))if(u.has(o[$])){const z=x.get(s[y]),H=z!==void 0?i[z]:null;if(H===null){const X=Nt(e,i[v]);nt(X,a[y]),l[y]=X}else l[y]=nt(H,a[y]),Nt(e,i[v],H),i[z]=null;y++}else gn(i[$]),$--;else gn(i[v]),v++;for(;y<=C;){const z=Nt(e,l[C+1]);nt(z,a[y]),l[y++]=z}for(;v<=$;){const z=i[v++];z!==null&&gn(z)}return this.ut=s,jl(e,l),Pe}}),Nl=ee`
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
    <path fill-rule="evenodd" d="M2 4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4Zm4.78 1.97a.75.75 0 0 1 0 1.06L5.81 8l.97.97a.75.75 0 1 1-1.06 1.06l-1.5-1.5a.75.75 0 0 1 0-1.06l1.5-1.5a.75.75 0 0 1 1.06 0Zm2.44 1.06a.75.75 0 0 1 1.06-1.06l1.5 1.5a.75.75 0 0 1 0 1.06l-1.5 1.5a.75.75 0 1 1-1.06-1.06l.97-.97-.97-.97Z" clip-rule="evenodd" />
  </svg>
`,Oa=ee`
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
    />
  </svg>
`,Ul=ee`
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
    <path fill-rule="evenodd" d="M2 4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4Zm2.22 1.97a.75.75 0 0 0 0 1.06l.97.97-.97.97a.75.75 0 1 0 1.06 1.06l1.5-1.5a.75.75 0 0 0 0-1.06l-1.5-1.5a.75.75 0 0 0-1.06 0ZM8.75 8.5a.75.75 0 0 0 0 1.5h2.5a.75.75 0 0 0 0-1.5h-2.5Z" clip-rule="evenodd" />
  </svg>
`,Ia=e=>ee`<svg aria-hidden="true" class="${e} h-4 w-4" fill="white" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
  <path
    fill-rule="evenodd"
    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
    clip-rule="evenodd"
  ></path>
</svg>`,Hl=Ia("rotate-180"),ql=Ia();var Ra=Object.defineProperty,Vl=Object.getOwnPropertyDescriptor,Da=e=>{throw TypeError(e)},Wl=(e,t,r)=>t in e?Ra(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Ut=(e,t,r,n)=>{for(var i=n>1?void 0:n?Vl(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&Ra(t,r,i),i},Ba=(e,t,r)=>Wl(e,typeof t!="symbol"?t+"":t,r),mn=(e,t,r)=>t.has(e)||Da("Cannot "+r),_e=(e,t,r)=>(mn(e,t,"read from private field"),t.get(e)),Ie=(e,t,r)=>t.has(e)?Da("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),ja=(e,t,r,n)=>(mn(e,t,"write to private field"),t.set(e,r),r),we=(e,t,r)=>(mn(e,t,"access private method"),r),hr,wt,pr,fe,La,Na,Ua,vn,Ha,qa,Va,Wa,bn,yn,wn,kn,fr,xn;let kt=class extends qe{constructor(){super(),Ie(this,fe),Ie(this,hr,new AbortController),Ie(this,wt,[]),Ie(this,pr,""),Ba(this,"open",()=>{this.dialog.showModal()}),Ba(this,"close",()=>{this.dialog.close()}),Ie(this,vn,e=>{((e.ctrlKey||e.metaKey)&&e.key==="k"||!this.isOpen&&e.key==="/")&&_e(this,bn).call(this,e),this.isOpen&&(e.key==="ArrowUp"?we(this,fe,Va).call(this):e.key==="ArrowDown"&&we(this,fe,qa).call(this),(e.key==="ArrowUp"||e.key==="ArrowDown")&&this.updateComplete.then(()=>{we(this,fe,Ha).call(this)}),e.key==="Enter"&&we(this,fe,Wa).call(this))}),Ie(this,bn,(e=null)=>{e?.preventDefault(),e?.stopPropagation(),this.isOpen?this.close():this.open()}),Ie(this,yn,()=>{this.fileIndex=0,we(this,fe,fr).call(this,""),document.body.style.overflow=_e(this,pr)}),Ie(this,wn,()=>{document.body.style.overflow="hidden"}),Ie(this,kn,e=>{this.isOpen&&(we(this,fe,fr).call(this,e.target.value),this.fileIndex=0)}),this.fileIndex=0,this.filteredFiles=[]}get isOpen(){return this.dialog.open}connectedCallback(){super.connectedCallback(),ja(this,pr,document.body.style.overflow),window.addEventListener("keydown",_e(this,vn),{signal:_e(this,hr).signal})}disconnectedCallback(){super.disconnectedCallback(),fn.cleanup(),_e(this,hr).abort()}willUpdate(e){e.has("rootModel")&&(we(this,fe,Ua).call(this),we(this,fe,fr).call(this,""))}render(){return b`
      <dialog
        @click="${this.close}"
        @close="${_e(this,yn)}"
        @show="${_e(this,wn)}"
        aria-labelledby="file-picker-label"
        class="mx-auto my-4 max-w-[40rem] bg-transparent backdrop:bg-gray-950/50 backdrop:backdrop-blur-lg md:w-1/2"
      >
        <div
          @click="${e=>e.stopPropagation()}"
          class="flex h-fit max-h-[33rem] flex-col rounded-lg bg-gray-200/60 p-4 backdrop-blur-lg"
        >
          <div class="mb-3 flex items-center rounded-sm bg-gray-200/60 p-2 text-gray-800 shadow-lg">
            <div class="mx-2 flex items-center">${Oa}</div>
            <label id="file-picker-label" for="file-picker-input" class="sr-only">Search for a file</label>
            <input
              autocomplete="off"
              id="file-picker-input"
              @input="${_e(this,kn)}"
              type="search"
              style="box-shadow: none"
              class="mr-2 w-full border-0 border-transparent bg-transparent focus:shadow-none"
              placeholder="Search for a file (Ctrl-K)"
              aria-controls="files"
            />
          </div>
          ${we(this,fe,La).call(this)}
        </div>
      </dialog>
    `}};hr=new WeakMap,wt=new WeakMap,pr=new WeakMap,fe=new WeakSet,La=function(){return b`
      <ul id="files" tabindex="-1" class="flex snap-y flex-col gap-2 overflow-auto" role="listbox" aria-labelledby="file-picker-label">
        ${vt(this.filteredFiles.length===0,()=>b`<li class="text-gray-800">No files found</li>`)}
        ${yt(this.filteredFiles,e=>e.name,({name:e,file:t,template:r},n)=>{const i=we(this,fe,xn).call(this,t);return b`
              <li
                class="group snap-start rounded-sm bg-gray-200 text-gray-900 transition-shadow aria-selected:bg-primary-500 aria-selected:text-gray-50 aria-selected:shadow-lg"
                role="option"
                aria-selected="${n===this.fileIndex}"
              >
                <a
                  tabindex="${n===this.fileIndex?0:-1}"
                  @click="${this.close}"
                  class="flex h-full flex-wrap items-center p-2 outline-hidden"
                  @mousemove="${()=>this.fileIndex=n}"
                  href="${bt(i,e)}"
                >
                  <span class="inline-flex" title="File with ${i}s">${we(this,fe,Na).call(this,i)}</span>
                  <span class="ms-1">${t.result?.name}</span>
                  <span class="mx-2">•</span>
                  <span class="text-gray-400 group-aria-selected:text-gray-200">${r??e}</span>
                </a>
              </li>
            `})}
      </ul>
    `},Na=function(e){return e===me.mutant?Nl:Ul},Ua=function(){if(!this.rootModel)return;ja(this,wt,[]);const e=(t,r=null,n)=>{if(t){if(t.file&&t.name!==n){const i=r?`${r}/${t.name}`:t.name;_e(this,wt).push({name:i,file:t.file,prepared:fn.prepare(i)})}t.childResults.forEach(i=>{r!==n&&r&&t.name?e(i,`${r}/${t.name}`,n):(r===n||!r)&&t.name!==n?e(i,t.name,n):e(i,null,n)})}};e(this.rootModel.systemUnderTestMetrics,null,"All files"),e(this.rootModel.testMetrics,null,"All tests")},vn=new WeakMap,Ha=function(){this.renderRoot.querySelector('[aria-selected="true"] a')?.scrollIntoView({block:"nearest"})},qa=function(){if(this.fileIndex===this.filteredFiles.length-1){this.fileIndex=0;return}this.fileIndex=Math.min(this.filteredFiles.length-1,this.fileIndex+1)},Va=function(){if(this.fileIndex===0){this.fileIndex=this.filteredFiles.length-1;return}this.fileIndex=Math.max(0,this.fileIndex-1)},Wa=function(){if(this.filteredFiles.length===0)return;const e=this.filteredFiles[this.fileIndex];window.location.href=bt(we(this,fe,xn).call(this,e.file),e.name),this.close()},bn=new WeakMap,yn=new WeakMap,wn=new WeakMap,kn=new WeakMap,fr=function(e){e?this.filteredFiles=fn.go(e,_e(this,wt),{key:"prepared",threshold:.3,limit:500}).map(t=>({file:t.obj.file,name:t.obj.name,template:t.highlight(r=>b`<mark class="bg-inherit text-primary-500 group-aria-selected:text-primary-50 group-aria-selected:underline">${r}</mark>`)})):this.filteredFiles=_e(this,wt)},xn=function(e){return e instanceof li?me.test:me.mutant},Ut([I({attribute:!1})],kt.prototype,"rootModel",2),Ut([he()],kt.prototype,"filteredFiles",2),Ut([he()],kt.prototype,"fileIndex",2),Ut([Xt("dialog")],kt.prototype,"dialog",2),kt=Ut([se("mte-file-picker")],kt);var Kl=Object.defineProperty,Zl=Object.getOwnPropertyDescriptor,Ka=e=>{throw TypeError(e)},$n=(e,t,r,n)=>{for(var i=n>1?void 0:n?Zl(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&Kl(t,r,i),i},Gl=(e,t,r)=>t.has(e)||Ka("Cannot "+r),Yl=(e,t,r)=>t.has(e)?Ka("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),it=(e,t,r)=>(Gl(e,t,"access private method"),r),Re,Za,_n,Sn,Ga,Ya;let gr=class extends qe{constructor(){super(...arguments),Yl(this,Re)}get rootName(){switch(this.view){case me.mutant:return"All files";case me.test:return"All tests"}}render(){return b`<nav class="my-4 flex rounded-md border border-primary-600 bg-primary-100 p-3 text-gray-700" aria-label="Breadcrumb">
      <ol class="inline-flex items-center">
        ${this.path&&this.path.length>0?it(this,Re,Sn).call(this,this.rootName,[]):it(this,Re,_n).call(this,this.rootName)}
        ${it(this,Re,Za).call(this)}
      </ol>
      ${it(this,Re,Ga).call(this)}
    </nav> `}};Re=new WeakSet,Za=function(){if(this.path){const e=this.path;return yt(e,t=>t,(t,r)=>r===e.length-1?it(this,Re,_n).call(this,t):it(this,Re,Sn).call(this,t,e.slice(0,r+1)))}},_n=function(e){return b`<li aria-current="page">
      <span class="ml-1 text-sm font-medium text-gray-800 md:ml-2">${e}</span>
    </li> `},Sn=function(e,t){return b`<li class="after:text-gray-800 after:content-['/'] md:after:pl-1">
      <a
        href="${bt(this.view,...t)}"
        class="ml-1 text-sm font-medium text-primary-800 underline hover:text-gray-900 hover:underline md:ml-2"
        >${e}</a
      >
    </li>`},Ga=function(){return b`
      <button @click="${()=>it(this,Re,Ya).call(this)}" class="ml-auto" title="Open file picker (Ctrl-K)">${Oa}</button>
    `},Ya=function(){this.blur(),this.renderRoot.querySelector("button")?.blur(),this.dispatchEvent(Ae("mte-file-picker-open",void 0))},$n([I({type:Array,attribute:!1})],gr.prototype,"path",2),$n([I()],gr.prototype,"view",2),gr=$n([se("mte-breadcrumb")],gr);var Ja=Object.defineProperty,Jl=Object.getOwnPropertyDescriptor,Qa=e=>{throw TypeError(e)},Ql=(e,t,r)=>t in e?Ja(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Xa=(e,t,r,n)=>{for(var i=n>1?void 0:n?Jl(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&Ja(t,r,i),i},es=(e,t,r)=>Ql(e,typeof t!="symbol"?t+"":t,r),Xl=(e,t,r)=>t.has(e)||Qa("Cannot "+r),ec=(e,t,r)=>t.has(e)?Qa("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),ts=(e,t,r)=>(Xl(e,t,"access private method"),r),mr,An;let Cn=class extends Fe{constructor(){super(...arguments),ec(this,mr),es(this,"next",e=>{e.stopPropagation(),this.dispatchEvent(Ae("next",void 0,{bubbles:!0,composed:!0}))}),es(this,"previous",e=>{e.stopPropagation(),this.dispatchEvent(Ae("previous",void 0,{bubbles:!0,composed:!0}))})}updated(e){e.has("filters")&&this.dispatchFiltersChangedEvent()}checkboxChanged(e,t){e.enabled=t,this.dispatchFiltersChangedEvent()}dispatchFiltersChangedEvent(){this.dispatchEvent(Ae("filters-changed",this.filters.filter(({enabled:e})=>e).map(({status:e})=>e)))}render(){return b`
      <div class="sticky top-offset z-10 mb-1 flex flex-row gap-5 bg-white py-6 pt-7">
        <div class="flex items-center gap-2">
          ${ts(this,mr,An).call(this,this.previous,Hl,"Previous","Select previous mutant")}
          ${ts(this,mr,An).call(this,this.next,ql,"Next","Select next mutant")}
        </div>

        ${vt(this.filters?.length,yt(this.filters,e=>e.status,e=>b`
              <div class="flex items-center gap-2 last:mr-12" data-status="${e.status.toString()}">
                <input
                  ?checked="${e.enabled}"
                  id="filter-${e.status}"
                  aria-describedby="status-description"
                  type="checkbox"
                  .value="${e.status.toString()}"
                  @input="${t=>this.checkboxChanged(e,t.target.checked)}"
                  class="h-5 w-5 shrink-0 rounded-sm bg-gray-100 ring-offset-gray-200! transition-colors checked:bg-primary-600 focus:ring-2 focus:ring-primary-500 focus:outline-hidden"
                />

                <label
                  for="filter-${e.status}"
                  class="${this.bgForContext(e.context)} rounded-md px-2.5 py-0.5 text-sm font-medium hover:cursor-pointer"
                >
                  ${e.label} (${e.count})
                </label>
              </div>
            `))}
      </div>
    `}bgForContext(e){switch(e){case"success":return"bg-green-100 text-green-800";case"warning":return"bg-yellow-100 text-yellow-800";case"danger":return"bg-red-100 text-red-800";case"caution":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}}};mr=new WeakSet,An=function(e,t,r,n){return b`<button
      title="${r}"
      @click=${e}
      type="button"
      class="inline-flex items-center rounded-sm bg-primary-600 p-1 text-center text-white hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:outline-hidden"
      >${t}
      <span class="sr-only">${n}</span>
    </button>`},Xa([I({type:Array})],Cn.prototype,"filters",2),Cn=Xa([se("mte-state-filter")],Cn);const tc=':host{--theme-d:1.5em;--theme-s:1.2em;--theme-p:.15em;--theme-g:.06em;--theme-width:2.9em;--poly:polygon(44.1337% 12.9617%,50% 0%,55.8663% 12.9617%,59.7057% 13.7778%,63.4388% 14.9907%,67.0246% 16.5873%,79.3893% 9.54915%,76.5165% 23.4835%,79.143% 26.4005%,81.4502% 29.576%,83.4127% 32.9754%,97.5528% 34.5492%,87.0383% 44.1337%,87.4486% 48.0374%,87.4486% 51.9626%,87.0383% 55.8663%,97.5528% 65.4508%,83.4127% 67.0246%,81.4502% 70.424%,79.143% 73.5995%,76.5165% 76.5165%,79.3893% 90.4508%,67.0246% 83.4127%,63.4388% 85.0093%,59.7057% 86.2222%,55.8663% 87.0383%,50% 100%,44.1337% 87.0383%,40.2943% 86.2222%,36.5612% 85.0093%,32.9754% 83.4127%,20.6107% 90.4508%,23.4835% 76.5165%,20.857% 73.5995%,18.5499% 70.424%,16.5873% 67.0246%,2.44717% 65.4508%,12.9617% 55.8663%,12.5514% 51.9626%,12.5514% 48.0374%,12.9617% 44.1337%,2.44717% 34.5492%,16.5873% 32.9754%,18.5499% 29.576%,20.857% 26.4005%,23.4835% 23.4835%,20.6107% 9.54915%,32.9754% 16.5873%,36.5612% 14.9907%,40.2943% 13.7778%)}#darkTheme{position:absolute;right:100vw}#darkTheme+label{--i:0;--j:calc(1 - var(--i));grid-gap:var(--theme-p)var(--theme-g);padding:var(--theme-p);height:var(--theme-d);border-radius:calc(.5*var(--theme-s) + var(--theme-p));background:hsl(199,98%,calc(var(--j)*48%));color:#0000;-webkit-user-select:none;user-select:none;cursor:pointer;transition:all .3s;display:grid;overflow:hidden}#darkTheme+label:before,#darkTheme+label:after{width:var(--theme-s);height:var(--theme-s);content:"";transition:inherit}#darkTheme+label:before{transform-origin:20% 20%;transform:translate(calc(var(--i)*(100% + var(--theme-g))))scale(calc(1 - var(--i)*.8));-webkit-clip-path:var(--poly);clip-path:var(--poly);background:#ff0}#darkTheme+label:after{transform:translatey(calc(var(--i)*(-130% - var(--theme-p))));background:radial-gradient(circle at 19% 19%,#0000 41%,#fff 43%);border-radius:50%;grid-column:2}#darkTheme:checked+label{--i:1}.check-box-container{width:var(--theme-width)}';var rs=Object.defineProperty,rc=Object.getOwnPropertyDescriptor,nc=(e,t,r)=>t in e?rs(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ns=(e,t,r,n)=>{for(var i=n>1?void 0:n?rc(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&rs(t,r,i),i},ic=(e,t,r)=>nc(e,t+"",r);let vr=class extends qe{dispatchThemeChangedEvent=t=>{const r=t.target.checked;this.dispatchEvent(Ae("theme-switch",r?"dark":"light"))};render(){return b`
      <div class="check-box-container" @click="${t=>t.stopPropagation()}">
        <input type="checkbox" @click="${this.dispatchThemeChangedEvent}" ?checked="${this.theme==="dark"}" id="darkTheme" />
        <label for="darkTheme">Dark</label>
      </div>
    `}};ic(vr,"styles",[ze,Te(tc)]),ns([I()],vr.prototype,"theme",2),vr=ns([se("mte-theme-switch")],vr);const is=({hasDetail:e,mode:t},r)=>b`<mte-drawer
    class="fixed bottom-0 z-10 container rounded-t-3xl bg-gray-200/60 shadow-xl backdrop-blur-lg motion-safe:transition-[height,max-width] motion-safe:duration-200"
    ?has-detail=${e}
    mode="${t}"
  >
    ${r}
  </mte-drawer>`;var ac=Object.defineProperty,sc=Object.getOwnPropertyDescriptor,Mn=(e,t,r,n)=>{for(var i=n>1?void 0:n?sc(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&ac(t,r,i),i};const as=e=>`${e.name}${e.sourceFile&&e.location?` (${da(e)})`:""}`,ss=e=>b`<span class="whitespace-pre-wrap">${e}</span>`;let br=class extends Fe{constructor(){super(),this.mode="closed"}render(){return is({hasDetail:!!(this.mutant?.killedByTests?.length||this.mutant?.coveredByTests?.length||this.mutant?.statusReason),mode:this.mode},Bt(this.mutant,e=>b`
          <span class="align-middle text-lg" slot="header"
            >${ca(e.status)} ${e.mutatorName} ${e.status}
            (${e.location.start.line}:${e.location.start.column})</span
          >
          <span slot="summary">${this.renderSummary()}</span>
          <span slot="detail" class="block">${this.renderDetail()}</span>
        `))}renderSummary(){return na(b`${this.mutant?.killedByTests?.[0]?tt(b`${Q("🎯","killed")} Killed by:
            ${this.mutant.killedByTests?.[0].name}${this.mutant.killedByTests.length>1?`(and ${this.mutant.killedByTests.length-1} more)`:""}`):P}
      ${vt(this.mutant?.static,tt(b`${Q("🗿","static")} Static mutant`))}
      ${Bt(this.mutant?.coveredByTests,e=>tt(b`${Q("☂️","umbrella")} Covered by ${e.length}
          test${ua(e)}${this.mutant?.status==="Survived"?" (yet still survived)":""}`))}
      ${vt(this.mutant?.statusReason?.trim(),tt(b`${Q("🕵️","spy")} ${ss(this.mutant.statusReason)}`,`Reason for the ${this.mutant.status} status`))}
      ${Bt(this.mutant?.description,e=>tt(b`${Q("📖","book")} ${ss(e)}`))}`)}renderDetail(){return b`<ul class="mr-2 mb-6">
      ${Ve(this.mutant?.killedByTests,e=>ir("This mutant was killed by this test",b`${Q("🎯","killed")} ${as(e)}`))}
      ${Ve(this.mutant?.coveredByTests?.filter(e=>!this.mutant?.killedByTests?.includes(e)),e=>ir("This mutant was covered by this test",b`${Q("☂️","umbrella")} ${as(e)}`))}
    </ul>`}};Mn([I({attribute:!1})],br.prototype,"mutant",2),Mn([I({reflect:!0})],br.prototype,"mode",2),br=Mn([se("mte-drawer-mutant")],br);var oc=Object.defineProperty,lc=Object.getOwnPropertyDescriptor,xt=(e,t,r,n)=>{for(var i=n>1?void 0:n?lc(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&oc(t,r,i),i};let at=class extends Fe{constructor(){super(),this.drawerMode="closed"}handleClick=()=>{this.drawerMode="closed"};handleMutantSelected=e=>{this.selectedMutant=e.detail.mutant,this.drawerMode=e.detail.selected?"half":"closed"};updated(e){e.has("result")&&!this.result.file&&(this.drawerMode="closed")}render(){return b`
      <main class="pb-drawer-half-open" @click="${this.handleClick}">
        <mte-metrics-table .columns="${cc}" .currentPath="${this.path}" .thresholds="${this.thresholds}" .model="${this.result}">
        </mte-metrics-table>
        ${this.result.file?b`<mte-file @mutant-selected="${this.handleMutantSelected}" .model="${this.result.file}"></mte-file>`:P}
      </main>
      <mte-drawer-mutant mode="${this.drawerMode}" .mutant="${this.selectedMutant}"></mte-drawer-mutant>
    `}};xt([he()],at.prototype,"drawerMode",2),xt([I({attribute:!1})],at.prototype,"selectedMutant",2),xt([I({attribute:!1})],at.prototype,"result",2),xt([I({attribute:!1,reflect:!1})],at.prototype,"thresholds",2),xt([I({attribute:!1,reflect:!1})],at.prototype,"path",2),at=xt([se("mte-mutant-view")],at);const cc=[{key:"mutationScore",label:"Of total",tooltip:"The percentage of mutants that were detected. The higher, the better!",category:"percentage",group:"Mutation score"},{key:"mutationScoreBasedOnCoveredCode",label:"Of covered",tooltip:"Mutation score based on only the code covered by tests",category:"percentage",group:"Mutation score"},{key:"killed",label:"Killed",tooltip:"At least one test failed while these mutants were active. This is what you want!",category:"number"},{key:"survived",label:"Survived",tooltip:"All tests passed while these mutants were active. You're missing a test for them.",category:"number"},{key:"timeout",label:"Timeout",tooltip:"Running the tests while these mutants were active resulted in a timeout. For example, an infinite loop.",category:"number"},{key:"noCoverage",label:"No coverage",tooltip:"These mutants aren't covered by one of your tests and survived as a result.",category:"number"},{key:"ignored",label:"Ignored",tooltip:"These mutants weren't tested because they are ignored. Either by user action, or for another reason.",category:"number"},{key:"runtimeErrors",label:"Runtime errors",tooltip:"Running tests when these mutants are active resulted in an error (rather than a failed test). For example: an out of memory error.",category:"number"},{key:"compileErrors",label:"Compile errors",tooltip:"Mutants that caused a compile error.",category:"number"},{key:"totalDetected",label:"Detected",tooltip:"The number of mutants detected by your tests (killed + timeout).",category:"number",width:"large",isBold:!0},{key:"totalUndetected",label:"Undetected",tooltip:"The number of mutants that are not detected by your tests (survived + no coverage).",category:"number",width:"large",isBold:!0},{key:"totalMutants",label:"Total",tooltip:"All mutants (except runtimeErrors + compileErrors)",category:"number",width:"large",isBold:!0}];var uc=Object.defineProperty,dc=Object.getOwnPropertyDescriptor,Ht=(e,t,r,n)=>{for(var i=n>1?void 0:n?dc(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&uc(t,r,i),i};let $t=class extends Fe{constructor(){super(),this.drawerMode="closed"}handleClick=()=>{this.drawerMode="closed"};handleTestSelected=e=>{this.selectedTest=e.detail.test,this.drawerMode=e.detail.selected?"half":"closed"};updated(e){e.has("result")&&!this.result.file&&(this.drawerMode="closed")}render(){return b`
      <main class="pb-drawer-half-open" @click="${this.handleClick}">
        <mte-metrics-table .columns="${hc}" .currentPath="${this.path}" .model="${this.result}"> </mte-metrics-table>
        ${this.result.file?b`<mte-test-file @test-selected="${this.handleTestSelected}" .model="${this.result.file}"></mte-test-file>`:P}
      </main>
      <mte-drawer-test mode="${this.drawerMode}" .test="${this.selectedTest}"></mte-drawer-test>
    `}};Ht([he()],$t.prototype,"drawerMode",2),Ht([I({attribute:!1})],$t.prototype,"result",2),Ht([I({attribute:!1,reflect:!1})],$t.prototype,"path",2),Ht([I({attribute:!1})],$t.prototype,"selectedTest",2),$t=Ht([se("mte-test-view")],$t);const hc=[{key:"killing",label:"Killing",tooltip:"These tests killed at least one mutant",width:"normal",category:"number"},{key:"covering",label:"Covering",tooltip:"These tests are covering at least one mutant, but not killing any of them.",width:"normal",category:"number"},{key:"notCovering",label:"Not Covering",tooltip:"These tests were not covering a mutant (and thus not killing any of them).",width:"normal",category:"number"},{key:"total",label:"Total tests",width:"large",category:"number",isBold:!0}];var pc=Object.defineProperty,fc=Object.getOwnPropertyDescriptor,qt=(e,t,r,n)=>{for(var i=n>1?void 0:n?fc(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&pc(t,r,i),i};let _t=class extends Fe{constructor(){super(),this.currentPath=[],this.thresholds={high:80,low:60}}hasMultipleColspan=!1;willUpdate(e){e.has("columns")&&(this.hasMultipleColspan=this.columns.some(t=>t.category==="percentage"))}render(){return this.model?b`<div class="overflow-x-auto rounded-md border border-gray-200">
          <table class="w-full table-auto text-left text-sm">${this.renderTableHeadRow()}${this.renderTableBody(this.model)} </table>
        </div>`:P}renderTableHeadRow(){const e=this.columns.filter(r=>r.group!=="Mutation score"),t=this.columns.filter(r=>r.group==="Mutation score");return b`<thead class="border-b border-gray-200 text-center text-sm">
      <tr>
        <th rowspan="2" scope="col" class="px-4 py-4">
          <div class="flex items-center justify-around">
            <span>File / Directory</span
            ><a
              href="https://stryker-mutator.io/docs/mutation-testing-elements/mutant-states-and-metrics"
              target="_blank"
              class="info-icon float-right"
              title="What does this all mean?"
              >${Q("ℹ","info icon")}</a
            >
          </div>
        </th>
        ${t.length>0?b`<th colspan="4" class="px-2 even:bg-gray-100">Mutation Score</th>`:""}
        ${yt(e,r=>r.key,r=>this.renderTableHead(r))}
      </tr>
      <tr>
        ${yt(t,r=>r.key,r=>this.renderTableHead(r))}
      </tr>
    </thead>`}renderTableHead(e){const t=`tooltip-${e.key.toString()}`,r=e.tooltip?b`<mte-tooltip title="${e.tooltip}" id="${t}">${e.label}</mte-tooltip>`:b`<span id="${t}">${e.label}</span>`;return e.group?b` <th colspan="2" class="bg-gray-200 px-2"> ${r} </th>`:b`<th rowspan="2" class="w-24 px-2 even:bg-gray-100 2xl:w-28">
      <div class="inline-block">${r}</div>
    </th>`}renderTableBody(e){const t=()=>e.file?P:Ve(e.childResults,r=>{const n=[r.name];for(;!r.file&&r.childResults.length===1;)r=r.childResults[0],n.push(r.name);return this.renderRow(n.join("/"),r,...this.currentPath,...n)});return b`<tbody class="divide-y divide-gray-200">${this.renderRow(e.name,e)} ${t()}</tbody>`}renderRow(e,t,...r){return b`<tr title="${t.name}" class="group hover:bg-gray-200">
      <td class="font-semibold">
        <div class="flex items-center justify-start">
          <mte-file-icon file-name="${t.name}" ?file="${t.file}" class="mx-1"></mte-file-icon> ${r.length>0?b`<a class="mr-auto inline-block w-full py-4 pr-2 hover:text-primary-on hover:underline" href="${bt(...r)}"
                >${e}</a
              >`:b`<span class="py-4">${t.name}</span>`}
        </div>
      </td>
      ${Ve(this.columns,n=>this.renderCell(n,t.metrics))}
    </tr>`}renderCell(e,t){const r=t[e.key],n=this.hasMultipleColspan?"odd:bg-gray-100":"even:bg-gray-100";if(e.category==="percentage"){const i=!isNaN(r),a=this.determineBgColoringClass(r),s=this.determineTextColoringClass(r),o=r.toFixed(2),l=`width: ${r}%`;return b`<td class="bg-gray-100 px-4 py-4 group-hover:bg-gray-200!">
          ${i?b`<div class="h-3 w-full min-w-[24px] rounded-full bg-gray-300">
                <div
                  class="${a} h-3 rounded-full pl-1 transition-all"
                  role="progressbar"
                  aria-valuenow="${o}"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  aria-describedby="tooltip-mutationScore"
                  title="${e.label}"
                  style="${l}"
                ></div>
              </div>`:b` <span class="text-light-muted font-bold">N/A</span> `}
        </td>
        <td class="${s} ${n} w-12 pr-2 text-center font-bold group-hover:bg-gray-200!"
          >${i?b`<span class="transition-colors">${o}</span>`:P}</td
        >`}return b`<td
      class="${Gr({"font-bold":e.isBold??!1,[n]:!0})} py-4 text-center group-hover:bg-gray-200!"
      aria-describedby="${`tooltip-${e.key.toString()}`}"
      >${r}</td
    >`}determineBgColoringClass(e){return!isNaN(e)&&this.thresholds?e<this.thresholds.low?"bg-red-600 text-gray-200":e<this.thresholds.high?"bg-yellow-400":"bg-green-600 text-gray-200":"bg-blue-600"}determineTextColoringClass(e){return!isNaN(e)&&this.thresholds?e<this.thresholds.low?"text-red-700":e<this.thresholds.high?"text-yellow-600":"text-green-700":""}};qt([I({attribute:!1})],_t.prototype,"model",2),qt([I({attribute:!1})],_t.prototype,"currentPath",2),qt([I({type:Array})],_t.prototype,"columns",2),qt([I({attribute:!1})],_t.prototype,"thresholds",2),_t=qt([se("mte-metrics-table")],_t);const gc='#report-code-block{background:var(--prism-background);border:1px solid var(--prism-border);overflow:auto visible}.line-numbers{counter-reset:mte-line-number}.line .line-number{text-align:right;color:var(--mut-line-number);counter-increment:mte-line-number;padding:0 10px 0 15px}.line .line-number:before{content:counter(mte-line-number)}.line-marker:before{content:" ";padding:0 5px}.Killing{--mut-test-dot-color:var(--color-green-700)}.Covering{--mut-test-dot-color:var(--color-amber-400)}.NotCovering{--mut-test-dot-color:var(--color-orange-500)}svg.test-dot{fill:var(--mut-test-dot-color)}';var os=Object.defineProperty,mc=Object.getOwnPropertyDescriptor,ls=e=>{throw TypeError(e)},vc=(e,t,r)=>t in e?os(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,st=(e,t,r,n)=>{for(var i=n>1?void 0:n?mc(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&os(t,r,i),i},yr=(e,t,r)=>vc(e,typeof t!="symbol"?t+"":t,r),En=(e,t,r)=>t.has(e)||ls("Cannot "+r),Vt=(e,t,r)=>(En(e,t,"read from private field"),t.get(e)),wr=(e,t,r)=>t.has(e)?ls("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),bc=(e,t,r,n)=>(En(e,t,"write to private field"),t.set(e,r),r),cs=(e,t,r)=>(En(e,t,"access private method"),r),Wt,Tn,kr,xr,Pn;let De=class extends Fe{constructor(){super(),wr(this,xr),wr(this,Wt),wr(this,Tn,e=>{e.key==="Escape"&&Vt(this,kr).call(this)}),yr(this,"filtersChanged",e=>{this.enabledStates=e.detail,this.selectedTest&&!this.enabledStates.includes(this.selectedTest.status)&&this.toggleTest(this.selectedTest)}),yr(this,"nextTest",()=>{const e=this.selectedTest?(this.tests.findIndex(({id:t})=>t===this.selectedTest.id)+1)%this.tests.length:0;this.selectTest(this.tests[e])}),yr(this,"previousTest",()=>{const e=this.selectedTest?(this.tests.findIndex(({id:t})=>t===this.selectedTest.id)+this.tests.length-1)%this.tests.length:this.tests.length-1;this.selectTest(this.tests[e])}),wr(this,kr,()=>{this.selectedTest&&this.toggleTest(this.selectedTest)}),this.filters=[],this.lines=[],this.enabledStates=[],this.tests=[],bc(this,Wt,new AbortController)}connectedCallback(){super.connectedCallback(),window.addEventListener("keydown",Vt(this,Tn),{signal:Vt(this,Wt).signal})}disconnectedCallback(){Vt(this,Wt).abort(),super.disconnectedCallback()}toggleTest(e){cs(this,xr,Pn).call(this,e),this.selectedTest===e?(this.selectedTest=void 0,this.dispatchEvent(Ae("test-selected",{selected:!1,test:e}))):(this.selectedTest&&cs(this,xr,Pn).call(this,this.selectedTest),this.selectedTest=e,this.dispatchEvent(Ae("test-selected",{selected:!0,test:e})),ha(this.renderRoot.querySelector(`[test-id="${e.id}"]`)))}selectTest(e){e&&this.toggleTest(e)}render(){return b`
      <mte-state-filter
        @next=${this.nextTest}
        @previous=${this.previousTest}
        .filters="${this.filters}"
        @filters-changed="${this.filtersChanged}"
      ></mte-state-filter>
      ${this.renderTestList()} ${this.renderCode()}
    `}renderTestList(){const e=this.tests.filter(t=>!t.location);return e.length?b`<ul class="max-w-6xl">
        ${yt(e,t=>t.id,t=>b`<li class="my-3">
              <button
                class="w-full rounded-sm p-3 text-left hover:bg-gray-100 active:bg-gray-200"
                type="button"
                data-active="${this.selectedTest===t}"
                test-id="${t.id}"
                @click=${r=>{r.stopPropagation(),this.toggleTest(t)}}
                >${Xr(t.status)} ${t.name} [${t.status}]
              </button>
            </li>`)}
      </ul>`:P}renderCode(){if(this.model?.source){const e=new Map;for(const r of this.tests)if(r.location){let n=e.get(r.location.start.line);n||(n=[],e.set(r.location.start.line,n)),n.push(r)}const t=r=>this.renderTestDots([...e.entries()].filter(([n])=>n>r).flatMap(([,n])=>n));return b`<pre
        id="report-code-block"
        @click="${Vt(this,kr)}"
        class="line-numbers flex rounded-md p-1"
      ><code class="flex language-${nn(this.model.name)}">
      <table>
        ${Ve(this.lines,(r,n)=>{const i=n+1,a=this.renderTestDots(e.get(i)),s=this.lines.length===i?t(i):P;return ba(r,va(a,s))})}</table></code></pre>`}return P}renderTestDots(e){return e?.length?e.map(t=>ee`<svg
              test-id="${t.id}"
              class="test-dot mx-0.5 cursor-pointer ${this.selectedTest?.id===t.id?"selected":""} ${t.status}"
              @click=${r=>{r.stopPropagation(),this.toggleTest(t)}}
              height="10"
              width="12"
            >
              <title>${yc(t)}</title>
              ${this.selectedTest===t?xa:$a}
            </svg>`):P}reactivate(){super.reactivate(),this.updateFileRepresentation()}willUpdate(e){e.has("model")&&this.updateFileRepresentation(),(e.has("model")||e.has("enabledStates"))&&this.model&&(this.tests=this.model.tests.filter(t=>this.enabledStates.includes(t.status)).sort((t,r)=>t.location&&r.location?cr(t.location.start,r.location.start)?1:-1:this.model.tests.indexOf(t)-this.model.tests.indexOf(r)))}updateFileRepresentation(){if(!this.model)return;const e=this.model;this.filters=[ne.Killing,ne.Covering,ne.NotCovering].filter(t=>e.tests.some(r=>r.status===t)).map(t=>({enabled:!0,count:e.tests.filter(r=>r.status===t).length,status:t,label:b`${Xr(t)} ${t}`,context:bl(t)})),this.model.source&&(this.lines=sn(an(this.model.source,this.model.name)))}};Wt=new WeakMap,Tn=new WeakMap,kr=new WeakMap,xr=new WeakSet,Pn=function(e){_a(this.renderRoot,"test-id",e.id)},yr(De,"styles",[ra,ze,Te(gc)]),st([I({attribute:!1})],De.prototype,"model",2),st([he()],De.prototype,"filters",2),st([he()],De.prototype,"lines",2),st([he()],De.prototype,"enabledStates",2),st([he()],De.prototype,"selectedTest",2),st([he()],De.prototype,"tests",2),De=st([se("mte-test-file")],De);function yc(e){return`${e.name} (${e.status})`}var wc=Object.defineProperty,kc=Object.getOwnPropertyDescriptor,zn=(e,t,r,n)=>{for(var i=n>1?void 0:n?kc(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&wc(t,r,i),i};const Fn=e=>b`<code>${e.getMutatedLines()}</code> (${da(e)})`;let $r=class extends Fe{constructor(){super(),this.mode="closed"}render(){return is({hasDetail:!!(this.test?.killedMutants?.length||this.test?.coveredMutants?.length),mode:this.mode},Bt(this.test,e=>b`
          <span class="align-middle text-lg" slot="header"
            >${Xr(e.status)} ${e.name} [${e.status}]
            ${e.location?b`(${e.location.start.line}:${e.location.start.column})`:P}</span
          >
          <span slot="summary">${this.renderSummary()}</span>
          <span class="block" slot="detail">${this.renderDetail()}</span>
        `))}renderSummary(){return na(b`${this.test?.killedMutants?.[0]?tt(b`${Q("🎯","killed")} Killed:
            ${Fn(this.test.killedMutants?.[0])}${this.test.killedMutants.length>1?b` (and ${this.test.killedMutants.length-1} more)`:""}`):P}
      ${Bt(this.test?.coveredMutants,e=>tt(b`${Q("☂️","umbrella")} Covered ${e.length}
          mutant${ua(e)}${this.test?.status===ne.Covering?" (yet didn't kill any of them)":""}`))}`)}renderDetail(){return b`<ul class="mr-2 mb-6">
      ${Ve(this.test?.killedMutants,e=>ir("This test killed this mutant",b`${Q("🎯","killed")} ${Fn(e)}`))}
      ${Ve(this.test?.coveredMutants?.filter(e=>!this.test?.killedMutants?.includes(e)),e=>ir("This test covered this mutant",b`${Q("☂️","umbrella")} ${Fn(e)}`))}
    </ul>`}};zn([I({attribute:!1})],$r.prototype,"test",2),zn([I({reflect:!0})],$r.prototype,"mode",2),$r=zn([se("mte-drawer-test")],$r);const xc="svg.cs{fill:var(--mut-file-csharp-color)}svg.html{fill:var(--mut-file-html-color)}svg.java{fill:var(--mut-file-java-color)}svg.javascript{fill:var(--mut-file-js-color)}svg.scala{fill:var(--mut-file-scala-color)}svg.typescript{fill:var(--mut-file-ts-color)}svg.php{fill:var(--mut-file-php-color)}svg.vue{fill:var(--mut-file-vue-color)}svg.octicon{fill:var(--mut-octicon-icon-color)}svg.javascript.test,svg.typescript.test{fill:var(--mut-file-js-test-color)}svg.gherkin{fill:var(--mut-file-gherkin-color)}svg.svelte{fill:var(--mut-file-svelte-color)}svg.rust{fill:var(--mut-file-rust-color)}svg{vertical-align:middle;width:20px}";var us=Object.defineProperty,$c=Object.getOwnPropertyDescriptor,_c=(e,t,r)=>t in e?us(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,On=(e,t,r,n)=>{for(var i=n>1?void 0:n?$c(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&us(t,r,i),i},Sc=(e,t,r)=>_c(e,t+"",r);let Kt=class extends qe{get language(){return nn(this.fileName)}get isTestFile(){const e=this.fileName.substr(0,this.fileName.lastIndexOf(".")).toLowerCase();return e.endsWith("spec")||e.endsWith("test")}get cssClass(){return Gr({[this.language?.toString()??"unknown"]:this.file,test:this.isTestFile})}render(){if(!this.file)return ee`<svg aria-label="directory" class="octicon octicon-file-directory" viewBox="0 0 14 16" version="1.1" width="14" height="16" role="img"><path fill-rule="evenodd" d="M 13,2 H 7 V 1 C 7,0.34 6.69,0 6,0 H 1 C 0.45,0 0,0.45 0,1 v 10 c 0,0.55 0.45,1 1,1 h 12 c 0.55,0 1,-0.45 1,-1 V 3 C 14,2.45 13.55,2 13,2 Z M 6,2 H 1 V 1 h 5 z" id="path2" /></svg>`;if(!this.language)return ee`<svg aria-label="file" class="octicon octicon-file" viewBox="0 0 12 16" version="1.1" width="12" height="16" role="img"><path fill-rule="evenodd" d="M6 5H2V4h4v1zM2 8h7V7H2v1zm0 2h7V9H2v1zm0 2h7v-1H2v1zm10-7.5V14c0 .55-.45 1-1 1H1c-.55 0-1-.45-1-1V2c0-.55.45-1 1-1h7.5L12 4.5zM11 5L8 2H1v12h10V5z"></path></svg>`;switch(this.language){case ye.csharp:return ee`<svg class="${this.cssClass}" aria-label="cs" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><g><path d="M7.1 15.9c0-1.3.2-2.4.6-3.4.4-1 .9-1.8 1.6-2.5.7-.7 1.5-1.2 2.4-1.6s1.9-.5 2.9-.5 1.9.2 2.7.6c.8.4 1.5.9 2 1.4l-2.2 2.5c-.4-.3-.7-.6-1.1-.7-.4-.1-.8-.3-1.4-.3-.5 0-.9.1-1.3.3-.4.2-.8.5-1.1.9s-.5.8-.7 1.4c-.2.6-.3 1.2-.3 1.9 0 1.5.3 2.6 1 3.3.7.8 1.5 1.2 2.6 1.2.5 0 1-.1 1.4-.3.4-.2.8-.5 1.1-.9l2.2 2.5c-.7.8-1.4 1.3-2.2 1.7-.8.4-1.7.6-2.7.6s-2-.2-2.9-.5-1.7-.8-2.4-1.5-1.1-1.7-1.5-2.7c-.5-.9-.7-2.1-.7-3.4z"/><path d="M21.8 17.1h-1l-.4 2.4h-1.2l.4-2.4h-1.2V16h1.5l.2-1.6h-1.3v-1.1h1.5l.4-2.4h1.2l-.4 2.4h1l.4-2.4h1.2l-.4 2.4H25v1.1h-1.6l-.2 1.6h1.3v1.1h-1.6l-.4 2.4h-1.2c0 .1.5-2.4.5-2.4zm-.8-1h1l.2-1.6h-1l-.2 1.6z"/></g></svg>`;case ye.html:return ee`<svg class="${this.cssClass}" aria-label="html" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M8 15l6-5.6V12l-4.5 4 4.5 4v2.6L8 17v-2zm16 2.1l-6 5.6V20l4.6-4-4.6-4V9.3l6 5.6v2.2z"/></svg>`;case ye.java:return ee`<svg class="${this.cssClass}" aria-label="java" xmlns="http://www.w3.org/2000/svg" viewBox="-4 -4 20 20"><path class="cls-1" d="M6 0a6 6 0 1 0 6 6 6 6 0 0 0-6-6zm2.14 6.8a2.16 2.16 0 0 1-2.29 2.41 2.5 2.5 0 0 1-2-.87l.73-.92a1.52 1.52 0 0 0 1.23.59c.66 0 1.06-.42 1.06-1.32V2.8h1.26z"/></svg>`;case ye.javascript:return ee`<svg class="${this.cssClass}" aria-label="js" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path  d="M11.4 10h2.7v7.6c0 3.4-1.6 4.6-4.3 4.6-.6 0-1.5-.1-2-.3l.3-2.2c.******* 1.4.3 1.1 0 1.9-.5 1.9-2.4V10zm5.1 9.2c.7.4 1.9.8 3 .8 1.3 0 1.9-.5 1.9-1.3s-.6-1.2-2-1.7c-2-.7-3.3-1.8-3.3-3.6 0-2.1 1.7-3.6 4.6-3.6 1.4 0 2.4.3 3.1.6l-.6 2.2c-.5-.2-1.3-.6-2.5-.6s-1.8.5-1.8 1.2c0 .8.7 1.1 2.2 1.7 2.1.8 3.1 1.9 3.1 3.6 0 2-1.6 3.7-4.9 3.7-1.4 0-2.7-.4-3.4-.7l.6-2.3z"/></svg>`;case ye.typescript:return ee`<svg class="${this.cssClass}" aria-label="ts" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M15.6 11.8h-3.4V22H9.7V11.8H6.3V10h9.2v1.8zm7.7 7.1c0-.5-.2-.8-.5-1.1-.3-.3-.9-.5-1.7-.8-1.4-.4-2.5-.9-3.3-1.5-.7-.6-1.1-1.3-1.1-2.3 0-1 .4-1.8 1.3-2.4.8-.6 1.9-.9 3.2-.9 1.3 0 2.4.4 3.2 ******* 1.2 1.6 1.2 2.6h-2.3c0-.6-.2-1-.6-1.4-.4-.3-.9-.5-1.6-.5-.6 0-1.1.1-1.5.4-.4.3-.5.7-.5 1.1 0 .******* 1 .4.3 1 .5 2 .8 1.3.4 2.3.9 3 ******* 1 1.4 1 2.4s-.4 1.9-1.2 2.4c-.8.6-1.9.9-3.2.9-1.3 0-2.5-.3-3.4-1s-1.5-1.6-1.4-2.9h2.4c0 .7.2 1.2.7 ******* 1.1.5 1.8.5s1.2-.1 1.5-.4c.2-.3.4-.7.4-1.1z"/></svg>`;case ye.scala:return ee`<svg class="${this.cssClass}" aria-label="scala" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M21.6 7v4.2c-.1.1-.1.2-.2.2-.3.3-.7.5-1.1.6-.9.3-1.9.5-2.8.7-1.6.3-3.1.5-4.7.7-.8.1-1.6.2-2.4.4V9.6c.1-.1.2-.1.4-.1 1.2-.2 2.5-.4 3.8-.5 1.9-.3 3.8-.5 5.6-1.1.5-.2 1.1-.4 1.4-.9zm0 5.6v4.2l-.2.2c-.5.4-1.1.6-1.6.8-.8.2-1.6.4-2.4.5-1 .2-1.9.3-2.9.5-1.4.2-2.7.3-4.1.6v-4.2c.1-.1.2-.1.3-.1 1.7-.2 3.4-.5 5.1-.7 1.4-.2 2.9-.5 4.3-.9.6-.2 1.1-.4 1.5-.9zM10.5 25h-.1v-4.2c.1-.1.2-.1.3-.1 1.2-.2 2.3-.3 3.5-.5 2-.3 3.9-.5 5.8-1.1.6-.2 1.2-.4 1.6-.9v4.2c-.1.2-.3.3-.5.5-.6.3-1.2.5-1.9.7-1.2.3-2.5.5-3.7.7-1.3.2-2.6.4-3.9.5-.4 0-.7.1-1.1.2z"/></svg>`;case ye.php:return ee`<svg class="${this.cssClass}" aria-label="php" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M12.7 19.7c-.1-.6-.4-1.1-1-1.3-.2-.1-.5-.3-.7-.4-.3-.1-.6-.2-.8-.3-.2-.1-.4 0-.6.2-.1.2 0 .4.1.5.1.2.2.3.4.5.2.3.4.5.7.8.2.3.4.5.3.9-.1.7-.4 1.4-.9 1.9-.1.1-.2.1-.2.1-.3 0-.7-.2-.9-.4-.3-.3-.2-.6.1-.8.1 0 .2-.1.2-.2.2-.2.3-.4.2-.7-.1-.1-.1-.2-.2-.3-.4-.4-.9-.8-1.4-1.2-1.3-1-1.9-2.2-2-3.6-.1-1.6.3-3.1 1.1-4.5.3-.5.7-1 1.3-1.3.4-.2.8-.3 1.2-.4 1.1-.3 2.3-.5 3.5-.3 1 .2 1.8.7 2.1 1.7.2.7.3 1.3.2 2-.1 1.4-1.2 2.6-2.5 3-.6.2-.9.1-1.2-.4-.2-.3-.5-.7-.7-1.1V14c0-.1-.1-.1-.1-.2.1.6.2 1.2.5 1.7.2.3.4.5.8.5 1.3.1 2.3-.3 3.1-1.3.8-1.1 1-2.4.8-3.8 0-.3-.1-.5-.2-.8 0-.2 0-.3.2-.4.1 0 .2 0 .2-.1 1-.2 2.1-.3 3.1-.2 1.2.1 2.3.4 3.3 1.1 1.6 1 2.6 2.5 3.1 4.3.1.3.1.5.1.8 0 .2-.1.2-.3.1-.2-.1-.3-.3-.4-.4-.1-.1-.2-.3-.3-.4-.1-.1-.2-.1-.2 0s-.1.2-.1.3c-.3 1-.7 1.9-1.4 2.6-.1.1-.2.3-.2.4 0 .4-.1.8 0 1.2.1.8.2 1.7.3 2.5.1.5-.1.7-.5.9-.3.1-.6.2-1 .2h-1.6c0-.6 0-1.2-.5-1.5.1-.4.2-.8.3-1.3.1-.4 0-.7-.2-1-.2-.3-.5-.3-.8-.2-.8.5-1.6.5-2.5.2-.4-.1-.7-.1-.9.3-.2.4-.3.8-.3 1.2 0 .5.1 1.1.2 1.6 0 .3 0 .4-.3.5-.7.2-1.4.2-2 .1h-.1c0-.6 0-1.2-.7-1.5.4-.4.4-1.1.3-1.7zm-4.1-2.3c.1-.1.2-.2.2-.4.1-.3-.2-.8-.5-.9-.2-.1-.3 0-.4.1-.3.3-.5.6-.8.9 0 .1-.1.1-.1.2-.1.2 0 .4.2.4.1 0 .3 0 .4.1.4 0 .7-.1 1-.4zm0-3.3c0-.2-.2-.4-.4-.4s-.5.2-.4.5c0 .2.2.4.5.4.1-.1.3-.3.3-.5z"/></svg>`;case ye.vue:return ee`<svg class="${this.cssClass}" aria-label="vue" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 1000"><path d="M600 495.9l159.1-275.4h-84.4L600 349.7l-74.6-129.2h-84.5z"/><path d="M793.7 220.5L600 555.9 406.3 220.5H277l323 559 323-559z"/></svg>`;case ye.gherkin:return ee`<svg class="${this.cssClass}" aria-label="gherkin" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M16.129,2a12.348,12.348,0,0,0-2.35,24.465V30c7.371-1.114,13.9-6.982,14.384-14.684a12.8,12.8,0,0,0-5.9-11.667c-.223-.132-.449-.262-.682-.377s-.481-.231-.729-.33c-.079-.033-.156-.063-.235-.094-.216-.08-.435-.17-.658-.236A12.188,12.188,0,0,0,16.129,2Z" style="fill:var(--mut-file-gherkin-color)"/><path d="M18.68,6.563a1.345,1.345,0,0,0-1.178.472,5.493,5.493,0,0,0-.518.9,2.9,2.9,0,0,0,.377,3.023A3.317,3.317,0,0,0,19.763,9,2.388,2.388,0,0,0,20,8,1.411,1.411,0,0,0,18.68,6.563Zm-5.488.071A1.441,1.441,0,0,0,11.85,8,2.388,2.388,0,0,0,12.085,9a3.427,3.427,0,0,0,2.473,1.96,3.141,3.141,0,0,0-.212-3.85,1.322,1.322,0,0,0-1.154-.472Zm-3.7,3.637a1.3,1.3,0,0,0-.73,2.338,5.663,5.663,0,0,0,.895.543,3.386,3.386,0,0,0,3.179-.307,3.492,3.492,0,0,0-2.049-2.338,2.69,2.69,0,0,0-1.06-.236,1.369,1.369,0,0,0-.236,0Zm11.611,4.582a3.44,3.44,0,0,0-1.955.567A3.492,3.492,0,0,0,21.2,17.758a2.69,2.69,0,0,0,1.06.236,1.329,1.329,0,0,0,.966-2.362,5.47,5.47,0,0,0-.895-.52,3.247,3.247,0,0,0-1.225-.26Zm-10.292.071a3.247,3.247,0,0,0-1.225.26,2.575,2.575,0,0,0-.895.543A1.34,1.34,0,0,0,9.73,18.065a2.426,2.426,0,0,0,1.06-.236,3.185,3.185,0,0,0,1.955-2.338,3.366,3.366,0,0,0-1.931-.567Zm3.815,2.314a3.317,3.317,0,0,0-2.4,1.96,2.286,2.286,0,0,0-.236.968,1.4,1.4,0,0,0,2.426.992,5.492,5.492,0,0,0,.518-.9,3.109,3.109,0,0,0-.306-3.023Zm2.8.071a3.141,3.141,0,0,0,.212,3.85,1.47,1.47,0,0,0,2.5-.9,2.388,2.388,0,0,0-.236-.992,3.427,3.427,0,0,0-2.473-1.96Z" style="fill:#fff"/></svg>`;case ye.svelte:return ee`<svg class="${this.cssClass}" aria-label="svelte" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M10.617 10.473L14.809 7.8c2.387-1.52 5.688-.812 7.359 1.58a5.123 5.123 0 01.876 3.876 4.821 4.821 0 01-.72 1.798c.524.998.7 2.142.5 3.251a4.808 4.808 0 01-1.963 3.081l-.21.14-4.192 2.672c-2.386 1.52-5.688.812-7.36-1.58a5.125 5.125 0 01-.875-3.876c.116-.642.36-1.253.72-1.798a5.065 5.065 0 01-.5-3.251 4.81 4.81 0 011.962-3.081l.21-.14L14.81 7.8l-4.192 2.672zm9.825.008a3.33 3.33 0 00-3.573-1.324c-.226.06-.444.146-.65.256l-.202.118-4.192 2.671a2.891 2.891 0 00-1.306 1.937 3.081 3.081 0 00.526 2.33 3.33 3.33 0 003.574 1.326c.226-.06.444-.147.65-.256l.201-.118 1.6-1.02a.923.923 0 01.257-.113c.407-.105.837.054 1.077.4a.931.931 0 01.158.702.873.873 0 01-.295.512l-.099.072-4.192 2.671a.923.923 0 01-.257.113 1.003 1.003 0 01-1.076-.4.94.94 0 01-.171-.49l.002-.132.014-.156-.156-.047a5.407 5.407 0 01-1.387-.645l-.252-.174-.215-.158-.08.24a2.923 2.923 0 00-.1.392 3.082 3.082 0 00.527 2.33 3.33 3.33 0 003.38 1.37l.194-.045c.226-.06.444-.146.65-.256l.202-.118 4.192-2.671a2.892 2.892 0 001.306-1.937 3.081 3.081 0 00-.526-2.331 3.33 3.33 0 00-3.574-1.325 3.05 3.05 0 00-.65.257l-.201.117-1.6 1.02a.927.927 0 01-.257.113 1.003 1.003 0 01-1.077-.4.93.93 0 01-.158-.702.871.871 0 01.295-.512l.098-.072 4.192-2.671a.923.923 0 01.258-.113c.407-.106.836.053 1.076.399a.942.942 0 01.171.49l-.002.133-.014.156.155.047c.492.148.959.365 1.388.645l.252.175.215.157.079-.24c.042-.129.076-.26.1-.392a3.082 3.082 0 00-.526-2.33z"/></svg>`;case ye.rust:return ee`<svg class="${this.cssClass}" aria-label="rust" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M21.7 8.4V9l.1.1h.1c.3-.1.6-.1.9-.2.2-.1.4.1.3.3-.1.3-.1.6-.2.9v.1l.1.1c0 .1.1.1.2.1h.9c.2 0 .3.1.3.3v.2c-.1.3-.3.6-.4.8v.1s.1.1.1.2h.1c.3.1.6.1.9.2.2 0 .3.3.2.5-.2.3-.4.5-.5.7v.2c0 .1.1.1.2.2.3.1.5.2.8.3.2.1.3.3.1.5-.2.2-.4.4-.7.6v.3s.1.1.2.1c.2.1.4.3.7.4.2.1.2.4 0 .5-.3.2-.5.3-.8.5v.1c0 .2 0 .2.1.3.2.2.4.4.6.5.2.2.1.4-.1.5-.3.1-.6.2-.8.3 0 0-.1 0-.1.1-.1.1 0 .2 0 .3.2.2.3.4.5.7.1.1.1.3-.1.4-.1 0-.1 0-.2.1-.3 0-.5.1-.8.1h-.1c0 .1-.1.1-.1.2s0 .1.1.2c.1.2.2.5.3.7.1.1 0 .3-.1.4h-1.2c-.1.1-.1.2-.1.3.1.3.1.5.2.8.1.2-.1.4-.4.4-.3-.1-.6-.1-.9-.2H22l-.1.1s-.1.1 0 .1v.9c0 .2-.1.3-.3.3h-.2c-.3-.1-.5-.2-.8-.4h-.1c-.1 0-.2.1-.2.2 0 .3-.1.5-.1.8 0 .2-.3.3-.5.2-.2-.2-.5-.4-.7-.5h-.1c-.1 0-.2.1-.2.2-.1.3-.2.5-.3.8-.1.2-.2.2-.3.2-.1 0-.1-.1-.1-.1-.2-.2-.4-.4-.6-.7h-.2c-.1 0-.2.1-.2.2-.1.2-.3.5-.4.7-.1.2-.4.2-.5 0-.2-.3-.3-.5-.5-.8h-.2c-.1 0-.1 0-.2.1l-.6.6c-.1.1-.2.1-.4.1-.1 0-.1-.1-.1-.2l-.3-.9s0-.1-.1-.1h-.3c-.2.2-.4.3-.7.5-.4-.2-.7-.3-.7-.5-.1-.3-.1-.6-.1-.9 0 0 0-.1-.1-.1s-.1-.1-.2-.1-.1 0-.2.1c-.2.1-.5.2-.7.3-.2.1-.4 0-.4-.2V23l-.1-.1h-.1c-.3.1-.6.1-.9.2-.2.1-.4-.1-.3-.3.1-.3.1-.6.2-.9v-.1l-.1-.1H8c-.2 0-.3-.1-.3-.3v-.2c.1-.3.3-.6.4-.8v-.1s0-.1-.1-.1c0-.1-.1-.1-.1-.1-.3 0-.6-.1-.9-.1-.2 0-.3-.3-.2-.5.2-.2.4-.5.5-.7v-.1c0-.1 0-.1-.1-.2 0 0-.1-.1-.2-.1-.2-.1-.5-.2-.7-.3-.2-.1-.3-.3-.1-.5.3-.1.5-.4.8-.6v-.2c0-.1 0-.2-.1-.2-.2-.1-.5-.3-.7-.4-.2-.1-.2-.4 0-.5.3-.2.5-.3.8-.5V15l-.1-.1-.6-.6c-.1-.1-.1-.3 0-.4 0 0 .1 0 .1-.1l.9-.3v-.1c.1-.1 0-.2 0-.3-.2-.2-.3-.4-.5-.6-.1-.2 0-.5.2-.5.3-.1.6-.1.9-.2H8c0-.1.1-.1.1-.2s0-.1-.1-.2c-.1-.2-.2-.5-.3-.7-.1-.2 0-.4.2-.4H9s0-.1.1-.1v-.1c-.1-.3-.2-.6-.2-.9-.1-.2.1-.4.3-.3.3 0 .6.1.9.2h.1l.1-.1s.1-.1 0-.1V8c0-.2.1-.3.3-.3h.2c.3.1.6.3.8.4h.1s.1 0 .1-.1c.1 0 .1-.1.1-.1 0-.3.1-.6.1-.9 0-.2.3-.3.5-.2.2.2.5.4.7.5h.1c.1 0 .1 0 .2-.1 0 0 0-.1.1-.2.1-.2.2-.5.3-.7.1-.2.2-.2.4-.2l.1.1c.1.3.4.5.6.8h.1c.1 0 .2-.1.2-.1.1-.2.3-.5.4-.7.2-.2.4-.2.5-.1l.1.1c.2.3.3.5.5.8h.3c.1 0 .1-.1.1-.1.2-.2.4-.4.5-.6.1-.1.3-.1.4 0v.1c.1.3.2.6.3.8l.1.1h.2c.3-.1.6-.3.8-.5.2-.1.5 0 .5.2 0 .3.1.6.1.9 0 0 0 .1.1.1h.1c.1.1.2.1.2 0 .2-.1.5-.2.8-.3.2-.1.4 0 .4.3v.4zm-11.1 2.7h7.6c.3 0 .6 0 .9.1.6.2 1.1.5 1.4.9.3.3.5.7.5 1.2 0 .4-.1.8-.3 1.2-.2.3-.5.6-.8.8-.1.1-.2.2-.3.2.1.1.2.1.3.2.2.2.5.4.6.7.2.3.3.7.3 1 0 .1.1.2.2.3.2.2.5.2.8.2.2 0 .4-.1.5-.2.2-.2.2-.4.3-.6v-.5c0-.1 0-.1.1-.1h.7v-1.3c-.3-.1-.6-.3-.9-.4-.1-.1-.3-.1-.4-.2-.3-.1-.4-.4-.3-.8.2-.5.4-1 .7-1.5v-.1c-.4-.6-.8-1.2-1.4-1.7-1-.9-2.2-1.5-3.6-1.8h-.1c-.3.3-.6.6-1 .9-.2.2-.6.2-.8 0l-.9-.9h-.1c-.4.1-.7.2-1 .3-1.1.4-2 1-2.8 1.8-.1.1-.2.2-.2.3zm11.3 9.2h-3c-.2 0-.3 0-.4-.1-.4-.2-.6-.6-.7-1-.1-.4-.2-.7-.2-1.1 0-.2-.1-.4-.2-.6-.2-.5-.6-.8-1.1-.8h-1.8V18h1.8c.1 0 .1 0 .1.1v2c0 .1 0 .1-.1.1h-6c.2.3.4.5.6.7h.1c.4-.1.8-.2 1.2-.2.3-.1.6.1.7.4.1.******* 1.3v.1c.8.3 1.6.6 2.4.6.7.1 1.4 0 2.1-.1.5-.1 1-.3 1.5-.5v-.1c.1-.4.2-.9.3-1.3.1-.3.3-.5.7-.4l1.2.3h.1c0-.2.2-.5.4-.7zm-11.9-7l.3.6c0 .1.1.2 0 .3 0 .2-.2.3-.3.4-.4.2-.8.4-1.2.5 0 0-.1 0-.1.1v.5c0 .7.1 1.4.3 2.2 0 0 0 .1.1.1h2.1v-4.7H10zm4.3 1.4c.1 0 .1 0 0 0h2.3c.2 0 .4 0 .6-.1.1-.1.2-.1.3-.3.1-.2.1-.5-.1-.7-.2-.2-.5-.3-.7-.3h-2.5c.1.5.1.9.1 1.4zm-6-1c0 .*******.6s.6-.3.6-.6-.3-.6-.6-.6-.6.3-.6.6zM21 22.1c0-.3-.3-.6-.6-.6s-.6.3-.*******.6.6.6-.2.6-.6zm-9.4-.6c-.3 0-.6.3-.6.6s.*******.6-.3.6-.6-.3-.6-.6-.6zm5-13.1c0-.3-.2-.6-.6-.6-.3 0-.6.2-.6.6 0 .*******.6.3 0 .5-.3.6-.6zm6.5 6c.3 0 .6-.3.6-.6s-.3-.6-.6-.6-.6.3-.*******.6.6z"/></svg>`}}};Sc(Kt,"styles",[Te(xc)]),On([I({attribute:"file-name"})],Kt.prototype,"fileName",2),On([I({type:Boolean})],Kt.prototype,"file",2),Kt=On([se("mte-file-icon")],Kt);var Ac=Object.defineProperty,Cc=Object.getOwnPropertyDescriptor,ds=(e,t,r,n)=>{for(var i=n>1?void 0:n?Cc(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&Ac(t,r,i),i};let In=class extends qe{render(){return b`<span class="cursor-help underline decoration-dotted" title="${this.title}"><slot></slot></span>`}};ds([I({attribute:!0})],In.prototype,"title",2),In=ds([se("mte-tooltip")],In);/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */class Mc{constructor(t,{target:r,config:n,callback:i,skipInitial:a}){this.t=new Set,this.o=!1,this.i=!1,this.h=t,r!==null&&this.t.add(r??t),this.o=a??this.o,this.callback=i,window.IntersectionObserver?(this.u=new IntersectionObserver(s=>{const o=this.i;this.i=!1,this.o&&o||(this.handleChanges(s),this.h.requestUpdate())},n),t.addController(this)):console.warn("IntersectionController error: browser does not support IntersectionObserver.")}handleChanges(t){this.value=this.callback?.(t,this.u)}hostConnected(){for(const t of this.t)this.observe(t)}hostDisconnected(){this.disconnect()}async hostUpdated(){const t=this.u.takeRecords();t.length&&this.handleChanges(t)}observe(t){this.t.add(t),this.u.observe(t),this.i=!0}unobserve(t){this.t.delete(t),this.u.unobserve(t)}disconnect(){this.u.disconnect()}}var Ec=Object.defineProperty,Tc=Object.getOwnPropertyDescriptor,hs=e=>{throw TypeError(e)},St=(e,t,r,n)=>{for(var i=n>1?void 0:n?Tc(t,r):t,a=e.length-1,s;a>=0;a--)(s=e[a])&&(i=(n?s(t,r,i):s(i))||i);return n&&i&&Ec(t,r,i),i},Rn=(e,t,r)=>t.has(e)||hs("Cannot "+r),Pc=(e,t,r)=>(Rn(e,t,"read from private field"),r?r.call(e):t.get(e)),ps=(e,t,r)=>t.has(e)?hs("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),zc=(e,t,r,n)=>(Rn(e,t,"write to private field"),t.set(e,r),r),Ke=(e,t,r)=>(Rn(e,t,"access private method"),r),_r,Me,fs,gs,Dn,Bn,ms,vs;let ot=class extends qe{constructor(){super(),ps(this,Me),ps(this,_r),this.detected=0,this.noCoverage=0,this.pending=0,this.survived=0,this.total=0,zc(this,_r,new Mc(this,{callback:([e])=>!e.isIntersecting}))}render(){return b`
      ${Ke(this,Me,fs).call(this)}
      <div class="my-4 rounded-md border border-gray-200 bg-white transition-all">
        <div class="parts flex h-8 w-full overflow-hidden rounded-sm bg-gray-200">${Ke(this,Me,gs).call(this)}</div>
      </div>
    `}};return _r=new WeakMap,Me=new WeakSet,fs=function(){return b`<div
      class="${Pc(this,_r).value?"opacity-100":"opacity-0"} pointer-events-none fixed top-offset left-0 z-20 flex w-full justify-center transition-all"
    >
      <div class="container w-full bg-white py-2">
        <div class="flex h-2 overflow-hidden rounded-sm bg-gray-200">${Ke(this,Me,Bn).call(this).map(e=>Ke(this,Me,Dn).call(this,e,!0))}</div>
      </div>
    </div>`},gs=function(){return Ke(this,Me,Bn).call(this).map(e=>Ke(this,Me,Dn).call(this,e,!1))},Dn=function(e,t){return b`<div
      title="${t?P:e.tooltip}"
      style="width: ${Ke(this,Me,vs).call(this,e.amount)}%"
      class="${Ke(this,Me,ms).call(this,e.type)} ${e.amount===0?"opacity-0":"opacity-100"} relative flex items-center overflow-hidden"
      >${t?P:b`<span class="ms-3 font-bold text-gray-800">${e.amount}</span>`}
    </div>`},Bn=function(){return[{type:"detected",amount:this.detected,tooltip:`killed + timeout (${this.detected})`},{type:"survived",amount:this.survived,tooltip:`survived (${this.survived})`},{type:"no coverage",amount:this.noCoverage,tooltip:`no coverage (${this.noCoverage})`},{type:"pending",amount:this.pending,tooltip:"pending"}]},ms=function(e){switch(e){case"detected":return"bg-green-600";case"survived":return"bg-red-600";case"no coverage":return"bg-yellow-600";default:return"bg-gray-200"}},vs=function(e){return this.total!==0?100*e/this.total:0},St([I({type:Number})],ot.prototype,"detected",2),St([I({type:Number,attribute:"no-coverage"})],ot.prototype,"noCoverage",2),St([I({type:Number})],ot.prototype,"pending",2),St([I({type:Number})],ot.prototype,"survived",2),St([I({type:Number})],ot.prototype,"total",2),ot=St([se("mte-result-status-bar")],ot),Object.defineProperty(re,Symbol.toStringTag,{value:"Module"}),re}({});

    </script>
  </head>
  <body>
    <svg style="width: 80px; position:fixed; right:10px; bottom:10px; z-index:10" class="stryker-image" viewBox="0 0 1458 1458" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="2"><path fill="none" d="M0 0h1458v1458H0z"/><clipPath id="a"><path d="M0 0h1458v1458H0z"/></clipPath><g clip-path="url(#a)"><path d="M1458 729c0 402.655-326.345 729-729 729S0 1131.655 0 729C0 326.445 326.345 0 729 0s729 326.345 729 729" fill="#e74c3c" fill-rule="nonzero"/><path d="M778.349 1456.15L576.6 1254.401l233-105 85-78.668v-64.332l-257-257-44-187-50-208 251.806-82.793L1076.6 389.401l380.14 379.15c-19.681 367.728-311.914 663.049-678.391 687.599z" fill-opacity=".3"/><path d="M753.4 329.503c41.79 0 74.579 7.83 97.925 25.444 23.571 18.015 41.69 43.956 55.167 77.097l11.662 28.679 165.733-58.183-14.137-32.13c-26.688-60.655-64.896-108.61-114.191-144.011-49.329-35.423-117.458-54.302-204.859-54.302-50.78 0-95.646 7.376-134.767 21.542-40.093 14.671-74.09 34.79-102.239 60.259-28.84 26.207-50.646 57.06-65.496 92.701-14.718 35.052-22.101 72.538-22.101 112.401 0 72.536 20.667 133.294 61.165 182.704 38.624 47.255 98.346 88.037 179.861 121.291 42.257 17.475 78.715 33.125 109.227 46.994 27.193 12.361 49.294 26.124 66.157 41.751 15.309 14.186 26.497 30.584 33.63 49.258 7.721 20.214 11.16 45.69 11.16 76.402 0 28.021-4.251 51.787-13.591 71.219-8.832 18.374-20.171 33.178-34.523 44.219-14.787 11.374-31.193 19.591-49.393 24.466-19.68 5.359-39.14 7.993-58.69 7.993-29.359 0-54.387-3.407-75.182-10.747-20.112-7.013-37.144-16.144-51.259-27.486-13.618-11.009-24.971-23.766-33.744-38.279-9.64-15.8-17.272-31.924-23.032-48.408l-10.965-31.376-161.669 60.585 10.734 30.124c10.191 28.601 24.197 56.228 42.059 82.748 18.208 27.144 41.322 51.369 69.525 72.745 27.695 21.075 60.904 38.218 99.481 51.041 37.777 12.664 82.004 19.159 132.552 19.159 49.998 0 95.818-8.321 137.611-24.622 42.228-16.471 78.436-38.992 108.835-67.291 30.719-28.597 54.631-62.103 71.834-100.642 17.263-38.56 25.923-79.392 25.923-122.248 0-54.339-8.368-100.37-24.208-138.32-16.29-38.759-38.252-71.661-65.948-98.797-26.965-26.418-58.269-48.835-93.858-67.175-33.655-17.241-69.196-33.11-106.593-47.533-35.934-13.429-65.822-26.601-89.948-39.525-22.153-11.868-40.009-24.21-53.547-37.309-11.429-11.13-19.83-23.678-24.718-37.664-5.413-15.49-7.98-33.423-7.98-53.577 0-40.883 11.293-71.522 37.086-90.539 28.443-20.825 64.985-30.658 109.311-30.658z" fill="#f1c40f" fill-rule="nonzero"/><path d="M720 0h18v113h-18zM1458 738v-18h-113v18h113zM720 1345h18v113h-18zM113 738v-18H0v18h113z"/></g></svg>
    <mutation-test-report-app titlePostfix="Stryker">
      Your browser doesn't support <a href="https://caniuse.com/#search=custom%20elements">custom elements</a>.
      Please use a latest version of an evergreen browser (Firefox, Chrome, Safari, Opera, Edge, etc).
    </mutation-test-report-app>
    <script>
      const app = document.querySelector('mutation-test-report-app');
      app.report = {"files":{"server/util/constants.js":{"language":"javascript","mutants":[{"id":"0","mutatorName":"ObjectLiteral","replacement":"{}","statusReason":"expected 500 to equal 423","status":"Killed","static":true,"testsCompleted":4,"killedBy":["3"],"coveredBy":[],"location":{"end":{"column":2,"line":109},"start":{"column":18,"line":1}}},{"id":"1","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":23,"line":4},"start":{"column":16,"line":4}}},{"id":"2","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":47,"line":5},"start":{"column":27,"line":5}}},{"id":"3","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":10},"start":{"column":22,"line":6}}},{"id":"4","mutatorName":"ArrayDeclaration","replacement":"[]","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":63,"line":9},"start":{"column":23,"line":9}}},{"id":"5","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":9},"start":{"column":24,"line":9}}},{"id":"6","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":49,"line":9},"start":{"column":37,"line":9}}},{"id":"7","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":62,"line":9},"start":{"column":51,"line":9}}},{"id":"8","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":15},"start":{"column":25,"line":11}}},{"id":"9","mutatorName":"ArrayDeclaration","replacement":"[]","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":82,"line":14},"start":{"column":23,"line":14}}},{"id":"10","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":14},"start":{"column":24,"line":14}}},{"id":"11","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":49,"line":14},"start":{"column":37,"line":14}}},{"id":"12","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":62,"line":14},"start":{"column":51,"line":14}}},{"id":"13","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":81,"line":14},"start":{"column":64,"line":14}}},{"id":"14","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":29},"start":{"column":12,"line":16}}},{"id":"15","mutatorName":"Regex","replacement":"/[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"16","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[a-z]{2,6}/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"17","mutatorName":"Regex","replacement":"/^[^A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"18","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[A-Za-z0-9_-])@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"19","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"20","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[^A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"21","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]\\.([a-z]{1,6}\\.)?[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"22","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[^A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"23","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"24","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]\\.)?[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"25","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([^a-z]{1,6}\\.)?[a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"26","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[a-z]$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"27","mutatorName":"Regex","replacement":"/^[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[^a-z]{2,6}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":94,"line":17},"start":{"column":16,"line":17}}},{"id":"28","mutatorName":"Regex","replacement":"/[a-zA-Z0-9,'~._^ -]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":44,"line":18},"start":{"column":20,"line":18}}},{"id":"29","mutatorName":"Regex","replacement":"/^[a-zA-Z0-9,'~._^ -]*/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":44,"line":18},"start":{"column":20,"line":18}}},{"id":"30","mutatorName":"Regex","replacement":"/^[a-zA-Z0-9,'~._^ -]$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":44,"line":18},"start":{"column":20,"line":18}}},{"id":"31","mutatorName":"Regex","replacement":"/^[^a-zA-Z0-9,'~._^ -]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":44,"line":18},"start":{"column":20,"line":18}}},{"id":"32","mutatorName":"Regex","replacement":"/[a-zA-Z0-9,'~._^ -]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":42,"line":19},"start":{"column":18,"line":19}}},{"id":"33","mutatorName":"Regex","replacement":"/^[a-zA-Z0-9,'~._^ -]*/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":42,"line":19},"start":{"column":18,"line":19}}},{"id":"34","mutatorName":"Regex","replacement":"/^[a-zA-Z0-9,'~._^ -]$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":42,"line":19},"start":{"column":18,"line":19}}},{"id":"35","mutatorName":"Regex","replacement":"/^[^a-zA-Z0-9,'~._^ -]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":42,"line":19},"start":{"column":18,"line":19}}},{"id":"36","mutatorName":"Regex","replacement":"/[a-zA-Z']*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":20},"start":{"column":21,"line":20}}},{"id":"37","mutatorName":"Regex","replacement":"/^[a-zA-Z']*/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":20},"start":{"column":21,"line":20}}},{"id":"38","mutatorName":"Regex","replacement":"/^[a-zA-Z']$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":20},"start":{"column":21,"line":20}}},{"id":"39","mutatorName":"Regex","replacement":"/^[^a-zA-Z']*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":20},"start":{"column":21,"line":20}}},{"id":"40","mutatorName":"Regex","replacement":"/[ A-Za-z0-9_@./#&+-]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":54,"line":21},"start":{"column":29,"line":21}}},{"id":"41","mutatorName":"Regex","replacement":"/^[ A-Za-z0-9_@./#&+-]*/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":54,"line":21},"start":{"column":29,"line":21}}},{"id":"42","mutatorName":"Regex","replacement":"/^[ A-Za-z0-9_@./#&+-]$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":54,"line":21},"start":{"column":29,"line":21}}},{"id":"43","mutatorName":"Regex","replacement":"/^[^ A-Za-z0-9_@./#&+-]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":54,"line":21},"start":{"column":29,"line":21}}},{"id":"44","mutatorName":"Regex","replacement":"/[ A-Za-z_@./#&+-]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":65,"line":22},"start":{"column":43,"line":22}}},{"id":"45","mutatorName":"Regex","replacement":"/^[ A-Za-z_@./#&+-]*/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":65,"line":22},"start":{"column":43,"line":22}}},{"id":"46","mutatorName":"Regex","replacement":"/^[ A-Za-z_@./#&+-]$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":65,"line":22},"start":{"column":43,"line":22}}},{"id":"47","mutatorName":"Regex","replacement":"/^[^ A-Za-z_@./#&+-]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":65,"line":22},"start":{"column":43,"line":22}}},{"id":"48","mutatorName":"Regex","replacement":"/[^<"+"> ?//\\\\]+$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":38,"line":23},"start":{"column":22,"line":23}}},{"id":"49","mutatorName":"Regex","replacement":"/^[^<"+"> ?//\\\\]+/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":38,"line":23},"start":{"column":22,"line":23}}},{"id":"50","mutatorName":"Regex","replacement":"/^[^<"+"> ?//\\\\]$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":38,"line":23},"start":{"column":22,"line":23}}},{"id":"51","mutatorName":"Regex","replacement":"/^[<"+"> ?//\\\\]+$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":38,"line":23},"start":{"column":22,"line":23}}},{"id":"52","mutatorName":"Regex","replacement":"/[\\w@ ]+$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":24},"start":{"column":24,"line":24}}},{"id":"53","mutatorName":"Regex","replacement":"/^[\\w@ ]+/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":24},"start":{"column":24,"line":24}}},{"id":"54","mutatorName":"Regex","replacement":"/^[\\w@ ]$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":24},"start":{"column":24,"line":24}}},{"id":"55","mutatorName":"Regex","replacement":"/^[^\\w@ ]+$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":24},"start":{"column":24,"line":24}}},{"id":"56","mutatorName":"Regex","replacement":"/^[\\W@ ]+$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":35,"line":24},"start":{"column":24,"line":24}}},{"id":"57","mutatorName":"Regex","replacement":"/[ A-Za-z0-9_@./#&+-]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":48,"line":25},"start":{"column":23,"line":25}}},{"id":"58","mutatorName":"Regex","replacement":"/^[ A-Za-z0-9_@./#&+-]*/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":48,"line":25},"start":{"column":23,"line":25}}},{"id":"59","mutatorName":"Regex","replacement":"/^[ A-Za-z0-9_@./#&+-]$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":48,"line":25},"start":{"column":23,"line":25}}},{"id":"60","mutatorName":"Regex","replacement":"/^[^ A-Za-z0-9_@./#&+-]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":48,"line":25},"start":{"column":23,"line":25}}},{"id":"61","mutatorName":"Regex","replacement":"/[ A-Za-z0-9_@./#&+-]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":43,"line":26},"start":{"column":18,"line":26}}},{"id":"62","mutatorName":"Regex","replacement":"/^[ A-Za-z0-9_@./#&+-]*/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":43,"line":26},"start":{"column":18,"line":26}}},{"id":"63","mutatorName":"Regex","replacement":"/^[ A-Za-z0-9_@./#&+-]$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":43,"line":26},"start":{"column":18,"line":26}}},{"id":"64","mutatorName":"Regex","replacement":"/^[^ A-Za-z0-9_@./#&+-]*$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":43,"line":26},"start":{"column":18,"line":26}}},{"id":"65","mutatorName":"Regex","replacement":"/(http(s)?:\\/\\/www\\.)[-a-zA-Z0-9@:%.+~#=]{2,256}\\.[a-z]{2,4}\\b([-a-zA-Z0-9@:%+.~#?&//=]*)/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":105,"line":27},"start":{"column":14,"line":27}}},{"id":"66","mutatorName":"Regex","replacement":"/(http(s):\\/\\/www\\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\\.[a-z]{2,4}\\b([-a-zA-Z0-9@:%+.~#?&//=]*)/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":105,"line":27},"start":{"column":14,"line":27}}},{"id":"67","mutatorName":"Regex","replacement":"/(http(s)?:\\/\\/www\\.)?[-a-zA-Z0-9@:%.+~#=]\\.[a-z]{2,4}\\b([-a-zA-Z0-9@:%+.~#?&//=]*)/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":105,"line":27},"start":{"column":14,"line":27}}},{"id":"68","mutatorName":"Regex","replacement":"/(http(s)?:\\/\\/www\\.)?[^-a-zA-Z0-9@:%.+~#=]{2,256}\\.[a-z]{2,4}\\b([-a-zA-Z0-9@:%+.~#?&//=]*)/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":105,"line":27},"start":{"column":14,"line":27}}},{"id":"69","mutatorName":"Regex","replacement":"/(http(s)?:\\/\\/www\\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\\.[a-z]\\b([-a-zA-Z0-9@:%+.~#?&//=]*)/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":105,"line":27},"start":{"column":14,"line":27}}},{"id":"70","mutatorName":"Regex","replacement":"/(http(s)?:\\/\\/www\\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\\.[^a-z]{2,4}\\b([-a-zA-Z0-9@:%+.~#?&//=]*)/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":105,"line":27},"start":{"column":14,"line":27}}},{"id":"71","mutatorName":"Regex","replacement":"/(http(s)?:\\/\\/www\\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\\.[a-z]{2,4}\\b([-a-zA-Z0-9@:%+.~#?&//=])/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":105,"line":27},"start":{"column":14,"line":27}}},{"id":"72","mutatorName":"Regex","replacement":"/(http(s)?:\\/\\/www\\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\\.[a-z]{2,4}\\b([^-a-zA-Z0-9@:%+.~#?&//=]*)/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":105,"line":27},"start":{"column":14,"line":27}}},{"id":"73","mutatorName":"Regex","replacement":"/([+]\\d{1,2})?\\d{10}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":28},"start":{"column":17,"line":28}}},{"id":"74","mutatorName":"Regex","replacement":"/^([+]\\d{1,2})?\\d{10}/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":28},"start":{"column":17,"line":28}}},{"id":"75","mutatorName":"Regex","replacement":"/^([+]\\d{1,2})\\d{10}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":28},"start":{"column":17,"line":28}}},{"id":"76","mutatorName":"Regex","replacement":"/^([^+]\\d{1,2})?\\d{10}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":28},"start":{"column":17,"line":28}}},{"id":"77","mutatorName":"Regex","replacement":"/^([+]\\d)?\\d{10}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":28},"start":{"column":17,"line":28}}},{"id":"78","mutatorName":"Regex","replacement":"/^([+]\\D{1,2})?\\d{10}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":28},"start":{"column":17,"line":28}}},{"id":"79","mutatorName":"Regex","replacement":"/^([+]\\d{1,2})?\\d$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":28},"start":{"column":17,"line":28}}},{"id":"80","mutatorName":"Regex","replacement":"/^([+]\\d{1,2})?\\D{10}$/","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":28},"start":{"column":17,"line":28}}},{"id":"81","mutatorName":"ObjectLiteral","replacement":"{}","statusReason":"expected 423 to equal 400","status":"Killed","static":true,"testsCompleted":5,"killedBy":["4"],"coveredBy":[],"location":{"end":{"column":6,"line":36},"start":{"column":13,"line":31}}},{"id":"82","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":29,"line":32},"start":{"column":19,"line":32}}},{"id":"83","mutatorName":"StringLiteral","replacement":"\"\"","statusReason":"expected 423 to equal 400","status":"Killed","static":true,"testsCompleted":5,"killedBy":["4"],"coveredBy":[],"location":{"end":{"column":25,"line":33},"start":{"column":17,"line":33}}},{"id":"84","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":31,"line":34},"start":{"column":20,"line":34}}},{"id":"85","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":27,"line":35},"start":{"column":18,"line":35}}},{"id":"86","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":42},"start":{"column":18,"line":37}}},{"id":"87","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":27,"line":38},"start":{"column":18,"line":38}}},{"id":"88","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":23,"line":39},"start":{"column":16,"line":39}}},{"id":"89","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":19,"line":40},"start":{"column":14,"line":40}}},{"id":"90","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":33,"line":41},"start":{"column":21,"line":41}}},{"id":"91","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":45},"start":{"column":18,"line":43}}},{"id":"92","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":23,"line":44},"start":{"column":16,"line":44}}},{"id":"93","mutatorName":"ObjectLiteral","replacement":"{}","statusReason":"expected post to be called once but was called 0 times","status":"Killed","static":true,"testsCompleted":41,"killedBy":["37"],"coveredBy":[],"location":{"end":{"column":6,"line":55},"start":{"column":14,"line":46}}},{"id":"94","mutatorName":"StringLiteral","replacement":"\"\"","statusReason":"expected post to be called once but was called 0 times","status":"Killed","static":true,"testsCompleted":41,"killedBy":["37"],"coveredBy":[],"location":{"end":{"column":36,"line":47},"start":{"column":23,"line":47}}},{"id":"95","mutatorName":"StringLiteral","replacement":"\"\"","statusReason":"expected post to be called once but was called 0 times","status":"Killed","static":true,"testsCompleted":42,"killedBy":["38"],"coveredBy":[],"location":{"end":{"column":36,"line":48},"start":{"column":23,"line":48}}},{"id":"96","mutatorName":"StringLiteral","replacement":"\"\"","statusReason":"expected post to be called once but was called 0 times","status":"Killed","static":true,"testsCompleted":43,"killedBy":["39"],"coveredBy":[],"location":{"end":{"column":46,"line":49},"start":{"column":28,"line":49}}},{"id":"97","mutatorName":"StringLiteral","replacement":"\"\"","statusReason":"expected post to be called once but was called 0 times","status":"Killed","static":true,"testsCompleted":46,"killedBy":["42"],"coveredBy":[],"location":{"end":{"column":43,"line":50},"start":{"column":24,"line":50}}},{"id":"98","mutatorName":"StringLiteral","replacement":"\"\"","statusReason":"expected post to be called once but was called 0 times","status":"Killed","static":true,"testsCompleted":45,"killedBy":["41"],"coveredBy":[],"location":{"end":{"column":36,"line":51},"start":{"column":23,"line":51}}},{"id":"99","mutatorName":"StringLiteral","replacement":"\"\"","statusReason":"expected post to be called once but was called 0 times","status":"Killed","static":true,"testsCompleted":44,"killedBy":["40"],"coveredBy":[],"location":{"end":{"column":44,"line":52},"start":{"column":27,"line":52}}},{"id":"100","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":37,"line":53},"start":{"column":23,"line":53}}},{"id":"101","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":54,"line":54},"start":{"column":32,"line":54}}},{"id":"102","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":41,"line":56},"start":{"column":23,"line":56}}},{"id":"103","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":57},"start":{"column":20,"line":57}}},{"id":"104","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":51,"line":58},"start":{"column":15,"line":58}}},{"id":"105","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":62},"start":{"column":11,"line":59}}},{"id":"106","mutatorName":"StringLiteral","replacement":"\"\"","statusReason":"expected 400 to equal 200","status":"Killed","static":true,"testsCompleted":7,"killedBy":["6"],"coveredBy":[],"location":{"end":{"column":42,"line":64},"start":{"column":27,"line":64}}},{"id":"107","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":70},"start":{"column":21,"line":65}}},{"id":"108","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":27,"line":66},"start":{"column":18,"line":66}}},{"id":"109","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":29,"line":67},"start":{"column":19,"line":67}}},{"id":"110","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":31,"line":68},"start":{"column":20,"line":68}}},{"id":"111","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":47,"line":69},"start":{"column":28,"line":69}}},{"id":"112","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":40,"line":71},"start":{"column":33,"line":71}}},{"id":"113","mutatorName":"ObjectLiteral","replacement":"{}","statusReason":"expected 403 to equal 200","status":"Killed","static":true,"testsCompleted":36,"killedBy":["32"],"coveredBy":[],"location":{"end":{"column":6,"line":75},"start":{"column":15,"line":72}}},{"id":"114","mutatorName":"StringLiteral","replacement":"\"\"","statusReason":"expected 403 to equal 200","status":"Killed","static":true,"testsCompleted":36,"killedBy":["32"],"coveredBy":[],"location":{"end":{"column":35,"line":73},"start":{"column":22,"line":73}}},{"id":"115","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":23,"line":74},"start":{"column":16,"line":74}}},{"id":"116","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":79},"start":{"column":25,"line":76}}},{"id":"117","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":38,"line":77},"start":{"column":24,"line":77}}},{"id":"118","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":48,"line":78},"start":{"column":29,"line":78}}},{"id":"119","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":23,"line":80},"start":{"column":15,"line":80}}},{"id":"120","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":54,"line":81},"start":{"column":25,"line":81}}},{"id":"121","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":86},"start":{"column":31,"line":82}}},{"id":"122","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":23,"line":83},"start":{"column":16,"line":83}}},{"id":"123","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":37,"line":84},"start":{"column":23,"line":84}}},{"id":"124","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":49,"line":85},"start":{"column":29,"line":85}}},{"id":"125","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":90},"start":{"column":32,"line":87}}},{"id":"126","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":25,"line":88},"start":{"column":17,"line":88}}},{"id":"127","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":27,"line":89},"start":{"column":18,"line":89}}},{"id":"128","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":39,"line":92},"start":{"column":26,"line":92}}},{"id":"129","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":61,"line":93},"start":{"column":37,"line":93}}},{"id":"130","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":57,"line":94},"start":{"column":35,"line":94}}},{"id":"131","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":41,"line":95},"start":{"column":27,"line":95}}},{"id":"132","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":63,"line":96},"start":{"column":38,"line":96}}},{"id":"133","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":59,"line":97},"start":{"column":36,"line":97}}},{"id":"134","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":43,"line":98},"start":{"column":28,"line":98}}},{"id":"135","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":43,"line":99},"start":{"column":28,"line":99}}},{"id":"136","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":53,"line":100},"start":{"column":33,"line":100}}},{"id":"137","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":41,"line":101},"start":{"column":27,"line":101}}},{"id":"138","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":57,"line":102},"start":{"column":35,"line":102}}},{"id":"139","mutatorName":"StringLiteral","replacement":"\"\"","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":45,"line":103},"start":{"column":26,"line":103}}},{"id":"140","mutatorName":"ObjectLiteral","replacement":"{}","status":"Survived","static":true,"testsCompleted":60,"coveredBy":[],"location":{"end":{"column":6,"line":108},"start":{"column":36,"line":104}}}],"source":"module.exports = {\r\n    // For AES, this is always 16\r\n    IV_LENGTH: 16,\r\n    LOG_LEVEL: 'debug',\r\n    POST_EXPIRATION_DAYS: 'PostExpirationDays',\r\n    PROFILE_PICTURE: {\r\n        MIN_SIZE: 5120,\r\n        MAX_SIZE: 5242880,\r\n        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png']\r\n    },\r\n    USER_DOCUMENT_FILE: {\r\n        MIN_SIZE: 10240,\r\n        MAX_SIZE: 5242880,\r\n        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf']\r\n    },\r\n    REGEX: {\r\n        EMAIL: /^[A-Za-z0-9](\\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\\.([a-z]{1,6}\\.)?[a-z]{2,6}$/,\r\n        FIRSTNAME: /^[a-zA-Z0-9,'~._^ -]*$/,\r\n        SURNAME: /^[a-zA-Z0-9,'~._^ -]*$/,\r\n        ALPHA_ONLY: /^[a-zA-Z']*$/,\r\n        ALPHA_SPECIAL_CHAR: /^[ A-Za-z0-9_@./#&+-]*$/,\r\n        ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: /^[ A-Za-z_@./#&+-]*$/,\r\n        FULL_ACCESS: /^[^<"+"> ?//\\\\]+$/,\r\n        ALPHA_NUMARIC: /^[\\w@ ]+$/,\r\n        templateName: /^[ A-Za-z0-9_@./#&+-]*$/,\r\n        SUBJECT: /^[ A-Za-z0-9_@./#&+-]*$/,\r\n        URL: /(http(s)?:\\/\\/www\\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\\.[a-z]{2,4}\\b([-a-zA-Z0-9@:%+.~#?&//=]*)/,\r\n        MOBILE: /^([+]\\d{1,2})?\\d{10}$/\r\n    },\r\n    OTPLENGTH: 6,\r\n    STATUS: {\r\n        INACTIVE: 'inactive',\r\n        ACTIVE: 'active',\r\n        SUSPENDED: 'suspended',\r\n        FREEZED: 'freezed'\r\n    },\r\n    ENVIRONMENT: {\r\n        TESTING: 'testing',\r\n        LOCAL: 'local',\r\n        DEV: 'dev',\r\n        PRODUCTION: 'production'\r\n    },\r\n    EVENT_TYPES: {\r\n        EVENT: 'event'\r\n    },\r\n    TRIGGER: {\r\n        REQUESTED_BY: 'requestedBy',\r\n        EVENT_SIGNUP: 'eventSignup',\r\n        FUNDRAISER_SIGNUP: 'fundraiserSignup',\r\n        EVENT_UPDATED: 'eventSignupUpdate',\r\n        POST_CREATED: 'postCreated',\r\n        BOOSTER_DONATION: 'boosterDonation',\r\n        CONVERSATION: 'conversation',\r\n        PERSONAL_CONVERSATION: 'personalConversation'\r\n    },\r\n    DEVELOPERS_EMAIL: '<EMAIL>',\r\n    CONTACT_EMAIL: '<EMAIL>',\r\n    SES_HOST: 'email-smtp.eu-west-1.amazonaws.com',\r\n    ROLE: {\r\n        USER: 1,\r\n        ADMIN: 4\r\n    },\r\n    DEFAULT_PAGE_SIZE: 15,\r\n    AWS_S3_PUBLIC_BUCKET: 'vaalee-assets',\r\n    PAYMENT_STATUS: {\r\n        PENDING: 'pending',\r\n        APPROVED: 'approved',\r\n        CANCELLED: 'cancelled',\r\n        PAYMENT_INITIATED: 'payment-initiated'\r\n    },\r\n    DEFAULT_NOTIFICATION_TITLE: 'Alert',\r\n    ORG_ROLE: {\r\n        SUPER_ADMIN: 'super admin',\r\n        ADMIN: 'admin'\r\n    },\r\n    NOTIFICATION_ROUTE: {\r\n        EVENT_DETAILS: 'eventDetails',\r\n        FUNDRAISER_DETAILS: 'fundraiserDetails'\r\n    },\r\n    APP_NAME: 'Vaalee',\r\n    CONTACT_US_SUBJECT: 'New Contact Form Submission',\r\n    GROUP_CONVERSATION_TYPES: {\r\n        EVENT: 'event',\r\n        ORGANIZATION: 'organization',\r\n        ORGANIZATION_ADMIN: 'organization_admin'\r\n    },\r\n    GROUP_CONVERSATION_STATUS: {\r\n        ACTIVE: 'active',\r\n        EXPIRED: 'expired'\r\n    },\r\n    GROUP_CONVERSATION_EXPIRATION_DAYS: 2,\r\n    KEY_FOR_USER_EVENTS: 'user-events',\r\n    KEY_FOR_USER_REGISTERED_EVENTS: 'user-registered-events',\r\n    KEY_FOR_USER_CALENDAR_EVENTS: 'user-calendar-events',\r\n    KEY_FOR_CHILD_EVENTS: 'child-events',\r\n    KEY_FOR_CHILD_REGISTERED_EVENTS: 'child-registered-events',\r\n    KEY_FOR_CHILD_CALENDAR_EVENTS: 'child-calendar-events',\r\n    KEY_FOR_CHILD_DETAILS: 'child-details',\r\n    KEY_FOR_EVENT_DETAILS: 'event-details',\r\n    KEY_FOR_FUNDRAISER_DETAILS: 'fundraiser-details',\r\n    KEY_FOR_POST_DETAILS: 'post-details',\r\n    KEY_FOR_ORGANIZATION_DETAILS: 'organization-details',\r\n    FEED_VERSION_PREFIX: 'FeedVersionPrefix',\r\n    DB_BATCH_OPERATION_CHUNK_SIZE: {\r\n        BATCH_PUT: 25,\r\n        BATCH_DELETE: 25,\r\n        BATCH_GET: 100\r\n    }\r\n};\r\n"}},"schemaVersion":"1.0","thresholds":{"high":80,"low":60,"break":null},"testFiles":{"test/alltests.js":{"tests":[{"id":"0","name":"Data seeding Check server root url"},{"id":"1","name":"Notification List As a user I should handle error if token is not passed"},{"id":"2","name":"Notification List As a user I should validate the that i am registered to app and correct token is passed"},{"id":"3","name":"Notification List As a user I should validate the that i am registered to app and is verified"},{"id":"4","name":"Notification List As a user I should handle error in getting notification list"},{"id":"5","name":"Notification List As a user I should get notification list"},{"id":"6","name":"Notification List As a user I should get notification list with photo url"},{"id":"7","name":"Notification List As a user I should get 2nd page of notification list"},{"id":"8","name":"Create Notification should return error if title is not provided"},{"id":"9","name":"Create Notification should return error if description is not provided"},{"id":"10","name":"Create Notification should return error if associatedChildId is not provided"},{"id":"11","name":"Create Notification As a user I should handle error if token is not passed"},{"id":"12","name":"Create Notification As a user I should validate the that i am registered to app and correct token is passed"},{"id":"13","name":"Create Notification As a user I should validate the that i am registered to app and is verified"},{"id":"14","name":"Create Notification As a user I should validate that the child is associated with user"},{"id":"15","name":"Create Notification As a user I should be able to create notification"},{"id":"16","name":"Update notification read status As a user I should handle error if token is not passed"},{"id":"17","name":"Update notification read status As a user I should validate the that i am registered to app and correct token is passed"},{"id":"18","name":"Update notification read status As a user I should validate the that i am registered to app and is verified"},{"id":"19","name":"Update notification read status As a user I should validate if the notification is not found"},{"id":"20","name":"Update notification read status As a user I should validate if the notification is not associated with user"},{"id":"21","name":"Update notification read status As a user I should validate if the notification is already read"},{"id":"22","name":"Update notification read status As a user I should be able to update read status of a notification"},{"id":"23","name":"Send Notification should return error if description is not passed"},{"id":"24","name":"Send Notification should return error if organizationId is not passed"},{"id":"25","name":"Send Notification should return error if children is not passed"},{"id":"26","name":"Send Notification should return error if eventId is not passed"},{"id":"27","name":"Send Notification As a user I should handle error if token is not passed"},{"id":"28","name":"Send Notification As a user I should validate the that i am registered to app and correct token is passed"},{"id":"29","name":"Send Notification As a user I should validate the that i am registered to app and is verified"},{"id":"30","name":"Send Notification As a user I should get error if I am not associated with passed organization"},{"id":"31","name":"Send Notification As a user I should get error if I have editor role in passed organization"},{"id":"32","name":"Send Notification As a user I should be able to send notification to all children associated with passed org"},{"id":"33","name":"Send Notification As a user I should be able to send notification to all participants of the event passed"},{"id":"34","name":"Send Notification As a user I should be able to send notification to all the children passed"},{"id":"35","name":"Send Notification via topic As a user I should be able to send notification to the topic"},{"id":"36","name":"Send Notification via topic As a user I should get error when sending notification to the topic"},{"id":"37","name":"SqsService should pull event from queue and process notification for requestedBy notifications"},{"id":"38","name":"SqsService should pull event from queue and process notification for eventSignup notifications"},{"id":"39","name":"SqsService should pull event from queue and process notification for fundraiser signup notifications"},{"id":"40","name":"SqsService should pull event from queue and process notification for fundraiser signup booster donation notifications"},{"id":"41","name":"SqsService should pull event from queue and process notification for postCreation notifications"},{"id":"42","name":"SqsService should pull event from queue and process notification for event update notifications"},{"id":"43","name":"SqsService should handle error when jwtClient authorize fails"},{"id":"44","name":"PushNotificationService should send notification for 24hour trigger for single day event"},{"id":"45","name":"PushNotificationService should send notification for 24hour trigger for multiday event"},{"id":"46","name":"PushNotificationService should send notification for 72hour trigger for single day event"},{"id":"47","name":"PushNotificationService should send notification for 72hour trigger for multiday event"},{"id":"48","name":"PushNotificationService should handle errors correctly"},{"id":"49","name":"UpdateFeedsService should remove past feeds from the child feeds"},{"id":"50","name":"UpdateFeedsService should remove past posts from the child feeds"},{"id":"51","name":"UpdateFeedsService should skip if no past events in the feeds"},{"id":"52","name":"UpdateFeedsService should handle errors"},{"id":"53","name":"ConversationService should update conversation groups"},{"id":"54","name":"ConversationService should not update conversation groups if no expired groups"},{"id":"55","name":"ConversationService should handle error when updating conversation groups"},{"id":"56","name":"Stop server in end Server should stop manually to get code coverage"}],"source":"const dotenv = require('dotenv');\r\nconst env = process.env.NODE_ENV || 'testing';\r\ndotenv.config({ path: process.env.PWD + '/' + env + '.env' });\r\nglobal.logger = require('../server/util/logger');\r\nconst chai = require('chai');\r\nconst chaiHttp = require('chai-http');\r\nconst app = require('../index');\r\nchai.use(chaiHttp);\r\nconst request = require('supertest');\r\nrequest(app);\r\n\r\n// Start testing\r\nrequire('./init.test');\r\nrequire('../server/services/notification/test/notification.test');\r\nrequire('../server/sqs/test/sqs.test');\r\nrequire('../server/pushNotification/test/pushNotification.test');\r\nrequire('../server/updateFeeds/test/updateChildFeeds.test');\r\nrequire('../server/conversation/test/conversationService.test');\r\n\r\ndescribe('Stop server in end', () => {\r\n    it('Server should stop manually to get code coverage', done => {\r\n        app.close();\r\n        done();\r\n    });\r\n});\r\n"}},"projectRoot":"D:\\myproject\\vaalee-backend\\notification","config":{"$schema":"./node_modules/@stryker-mutator/core/schema/stryker-schema.json","_comment":"This config was generated using 'stryker init'. Please take a look at: https://stryker-mutator.io/docs/stryker-js/configuration/ for more information.","packageManager":"npm","reporters":["html","clear-text","progress"],"testRunner":"mocha","testRunner_comment":"Take a look at https://stryker-mutator.io/docs/stryker-js/mocha-runner for information about the mocha plugin.","coverageAnalysis":"perTest","mutate":["server/util/constants.js"],"testRunnerNodeArgs":["--max-old-space-size=4096"],"mochaOptions":{"spec":["test/**/*.js"]},"timeoutMS":60000,"timeoutFactor":2,"maxConcurrentTestRunners":1,"disableTypeChecks":"{src,lib}/**/*.{js,ts,jsx,tsx,html,vue,svelte}","ignorePatterns":["coverage/**/*","node_modules/**/*",".stryker-tmp/**/*"],"allowConsoleColors":true,"checkers":[],"checkerNodeArgs":[],"commandRunner":{"command":"npm test"},"clearTextReporter":{"allowColor":true,"allowEmojis":false,"logTests":true,"maxTestsToLog":3,"reportTests":true,"reportMutants":true,"reportScoreTable":true,"skipFull":false},"dashboard":{"baseUrl":"https://dashboard.stryker-mutator.io/api/reports","reportType":"full"},"dryRunOnly":false,"eventReporter":{"baseDir":"reports/mutation/events"},"ignoreStatic":false,"incremental":false,"incrementalFile":"reports/stryker-incremental.json","force":false,"fileLogLevel":"off","inPlace":false,"logLevel":"info","maxTestRunnerReuse":0,"mutator":{"plugins":null,"excludedMutations":[]},"plugins":["@stryker-mutator/*"],"appendPlugins":[],"htmlReporter":{"fileName":"reports/mutation/mutation.html"},"jsonReporter":{"fileName":"reports/mutation/mutation.json"},"symlinkNodeModules":true,"tempDirName":".stryker-tmp","cleanTempDir":true,"thresholds":{"high":80,"low":60,"break":null},"dryRunTimeoutMinutes":5,"tsconfigFile":"tsconfig.json","warnings":true,"disableBail":false,"allowEmpty":false,"ignorers":[],"concurrency":1},"framework":{"name":"StrykerJS","version":"9.0.1","branding":{"homepageUrl":"https://stryker-mutator.io","imageUrl":"data:image/svg+xml;utf8,%3Csvg viewBox='0 0 1458 1458' xmlns='http://www.w3.org/2000/svg' fill-rule='evenodd' clip-rule='evenodd' stroke-linejoin='round' stroke-miterlimit='2'%3E%3Cpath fill='none' d='M0 0h1458v1458H0z'/%3E%3CclipPath id='a'%3E%3Cpath d='M0 0h1458v1458H0z'/%3E%3C/clipPath%3E%3Cg clip-path='url(%23a)'%3E%3Cpath d='M1458 729c0 402.655-326.345 729-729 729S0 1131.655 0 729C0 326.445 326.345 0 729 0s729 326.345 729 729' fill='%23e74c3c' fill-rule='nonzero'/%3E%3Cpath d='M778.349 1456.15L576.6 1254.401l233-105 85-78.668v-64.332l-257-257-44-187-50-208 251.806-82.793L1076.6 389.401l380.14 379.15c-19.681 367.728-311.914 663.049-678.391 687.599z' fill-opacity='.3'/%3E%3Cpath d='M753.4 329.503c41.79 0 74.579 7.83 97.925 25.444 23.571 18.015 41.69 43.956 55.167 77.097l11.662 28.679 165.733-58.183-14.137-32.13c-26.688-60.655-64.896-108.61-114.191-144.011-49.329-35.423-117.458-54.302-204.859-54.302-50.78 0-95.646 7.376-134.767 21.542-40.093 14.671-74.09 34.79-102.239 60.259-28.84 26.207-50.646 57.06-65.496 92.701-14.718 35.052-22.101 72.538-22.101 112.401 0 72.536 20.667 133.294 61.165 182.704 38.624 47.255 98.346 88.037 179.861 121.291 42.257 17.475 78.715 33.125 109.227 46.994 27.193 12.361 49.294 26.124 66.157 41.751 15.309 14.186 26.497 30.584 33.63 49.258 7.721 20.214 11.16 45.69 11.16 76.402 0 28.021-4.251 51.787-13.591 71.219-8.832 18.374-20.171 33.178-34.523 44.219-14.787 11.374-31.193 19.591-49.393 24.466-19.68 5.359-39.14 7.993-58.69 7.993-29.359 0-54.387-3.407-75.182-10.747-20.112-7.013-37.144-16.144-51.259-27.486-13.618-11.009-24.971-23.766-33.744-38.279-9.64-15.8-17.272-31.924-23.032-48.408l-10.965-31.376-161.669 60.585 10.734 30.124c10.191 28.601 24.197 56.228 42.059 82.748 18.208 27.144 41.322 51.369 69.525 72.745 27.695 21.075 60.904 38.218 99.481 51.041 37.777 12.664 82.004 19.159 132.552 19.159 49.998 0 95.818-8.321 137.611-24.622 42.228-16.471 78.436-38.992 108.835-67.291 30.719-28.597 54.631-62.103 71.834-100.642 17.263-38.56 25.923-79.392 25.923-122.248 0-54.339-8.368-100.37-24.208-138.32-16.29-38.759-38.252-71.661-65.948-98.797-26.965-26.418-58.269-48.835-93.858-67.175-33.655-17.241-69.196-33.11-106.593-47.533-35.934-13.429-65.822-26.601-89.948-39.525-22.153-11.868-40.009-24.21-53.547-37.309-11.429-11.13-19.83-23.678-24.718-37.664-5.413-15.49-7.98-33.423-7.98-53.577 0-40.883 11.293-71.522 37.086-90.539 28.443-20.825 64.985-30.658 109.311-30.658z' fill='%23f1c40f' fill-rule='nonzero'/%3E%3Cpath d='M720 0h18v113h-18zM1458 738v-18h-113v18h113zM720 1345h18v113h-18zM113 738v-18H0v18h113z'/%3E%3C/g%3E%3C/svg%3E"},"dependencies":{"@stryker-mutator/mocha-runner":"9.0.1","mocha":"10.8.2","typescript":"5.8.3"}}};
      function updateTheme() {
        document.body.style.backgroundColor = app.themeBackgroundColor;
      }
      app.addEventListener('theme-changed', updateTheme);
      updateTheme();
    </script>
  </body>
  </html>