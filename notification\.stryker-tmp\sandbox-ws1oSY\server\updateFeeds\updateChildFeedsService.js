/* eslint-disable max-len */
// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const MOMENT = require('moment');
const Redis = require('ioredis');
const stringify = require('json-stringify-safe');
const redis = new Redis(process.env.REDIS_PORT, process.env.REDIS_HOST);
const CONSTANTS = require('../util/constants');
const {
  Client
} = require('@opensearch-project/opensearch');
const ConstantModel = require('../models/constant.model');
const Utils = require('../util/utilFunctions');
let client;
if (stryMutAct_9fa48("1482") ? process.env.NODE_ENV === 'testing' : stryMutAct_9fa48("1481") ? false : stryMutAct_9fa48("1480") ? true : (stryCov_9fa48("1480", "1481", "1482"), process.env.NODE_ENV !== (stryMutAct_9fa48("1483") ? "" : (stryCov_9fa48("1483"), 'testing')))) {
  if (stryMutAct_9fa48("1484")) {
    {}
  } else {
    stryCov_9fa48("1484");
    client = new Client(stryMutAct_9fa48("1485") ? {} : (stryCov_9fa48("1485"), {
      node: process.env.OPENSEARCH_ENDPOINT,
      auth: stryMutAct_9fa48("1486") ? {} : (stryCov_9fa48("1486"), {
        username: process.env.OPENSEARCH_USERNAME,
        password: process.env.OPENSEARCH_PASSWORD
      }),
      ssl: stryMutAct_9fa48("1487") ? {} : (stryCov_9fa48("1487"), {
        rejectUnauthorized: stryMutAct_9fa48("1488") ? true : (stryCov_9fa48("1488"), false)
      })
    }));
  }
}
class UpdateFeedsService {
  static async updateChildFeeds() {
    if (stryMutAct_9fa48("1489")) {
      {}
    } else {
      stryCov_9fa48("1489");
      try {
        if (stryMutAct_9fa48("1490")) {
          {}
        } else {
          stryCov_9fa48("1490");
          const versionPrefixFromDb = await ConstantModel.get(CONSTANTS.FEED_VERSION_PREFIX);
          const versionPrefix = stryMutAct_9fa48("1491") ? versionPrefixFromDb?.value && '' : (stryCov_9fa48("1491"), (stryMutAct_9fa48("1492") ? versionPrefixFromDb.value : (stryCov_9fa48("1492"), versionPrefixFromDb?.value)) ?? (stryMutAct_9fa48("1493") ? "Stryker was here!" : (stryCov_9fa48("1493"), '')));
          const expirationDays = await this.getPostExpirationDays();
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1494") ? `` : (stryCov_9fa48("1494"), `Expiration days for posts: ${expirationDays}`));
          const initialMemoryUsage = await this.getMemoryUsage();
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1495") ? `` : (stryCov_9fa48("1495"), `Initial Redis memory usage: ${initialMemoryUsage}`));
          const eventDetailsKey = Utils.getEventDetailsKey(stryMutAct_9fa48("1496") ? {} : (stryCov_9fa48("1496"), {
            versionPrefix,
            shouldGenerateKeyPrefix: stryMutAct_9fa48("1497") ? false : (stryCov_9fa48("1497"), true)
          }));
          CONSOLE_LOGGER.time(stryMutAct_9fa48("1498") ? `` : (stryCov_9fa48("1498"), `get: ${eventDetailsKey}:*`));
          const eventHashMapsOfRedis = await this.getEventHashMapsOfRedis(stryMutAct_9fa48("1499") ? `` : (stryCov_9fa48("1499"), `${eventDetailsKey}:*`));
          CONSOLE_LOGGER.timeEnd(stryMutAct_9fa48("1500") ? `` : (stryCov_9fa48("1500"), `get: ${eventDetailsKey}:*`));
          const fundraiserDetailsKey = Utils.getFundraiserDetailsKey(stryMutAct_9fa48("1501") ? {} : (stryCov_9fa48("1501"), {
            versionPrefix,
            shouldGenerateKeyPrefix: stryMutAct_9fa48("1502") ? false : (stryCov_9fa48("1502"), true)
          }));
          CONSOLE_LOGGER.time(stryMutAct_9fa48("1503") ? `` : (stryCov_9fa48("1503"), `get: ${fundraiserDetailsKey}:*`));
          const fundraiserHashMapOfRedis = await this.getEventHashMapsOfRedis(stryMutAct_9fa48("1504") ? `` : (stryCov_9fa48("1504"), `${fundraiserDetailsKey}:*`));
          CONSOLE_LOGGER.timeEnd(stryMutAct_9fa48("1505") ? `` : (stryCov_9fa48("1505"), `get: ${fundraiserDetailsKey}:*`));
          const postDetailsKey = Utils.getPostDetailsKey(stryMutAct_9fa48("1506") ? {} : (stryCov_9fa48("1506"), {
            versionPrefix,
            shouldGenerateKeyPrefix: stryMutAct_9fa48("1507") ? false : (stryCov_9fa48("1507"), true)
          }));
          CONSOLE_LOGGER.time(stryMutAct_9fa48("1508") ? `` : (stryCov_9fa48("1508"), `get: ${postDetailsKey}:*`));
          const postHashMapOfRedis = await this.getEventHashMapsOfRedis(stryMutAct_9fa48("1509") ? `` : (stryCov_9fa48("1509"), `${postDetailsKey}:*`));
          CONSOLE_LOGGER.timeEnd(stryMutAct_9fa48("1510") ? `` : (stryCov_9fa48("1510"), `get: ${postDetailsKey}:*`));
          const childEventsKey = Utils.getChildKey(stryMutAct_9fa48("1511") ? {} : (stryCov_9fa48("1511"), {
            versionPrefix,
            shouldGenerateKeyPrefix: stryMutAct_9fa48("1512") ? false : (stryCov_9fa48("1512"), true)
          }));
          CONSOLE_LOGGER.time(stryMutAct_9fa48("1513") ? `` : (stryCov_9fa48("1513"), `get: ${childEventsKey}:*`));
          const childEventsOfRedis = await this.getEventHashMapsOfRedis(stryMutAct_9fa48("1514") ? `` : (stryCov_9fa48("1514"), `${childEventsKey}:*`));
          CONSOLE_LOGGER.timeEnd(stryMutAct_9fa48("1515") ? `` : (stryCov_9fa48("1515"), `get: ${childEventsKey}:*`));
          const userEventsKey = Utils.getUserKey(stryMutAct_9fa48("1516") ? {} : (stryCov_9fa48("1516"), {
            versionPrefix,
            shouldGenerateKeyPrefix: stryMutAct_9fa48("1517") ? false : (stryCov_9fa48("1517"), true)
          }));
          CONSOLE_LOGGER.time(stryMutAct_9fa48("1518") ? `` : (stryCov_9fa48("1518"), `get: ${userEventsKey}:*`));
          const userEventsOfRedis = await this.getEventHashMapsOfRedis(stryMutAct_9fa48("1519") ? `` : (stryCov_9fa48("1519"), `${userEventsKey}:*`));
          CONSOLE_LOGGER.timeEnd(stryMutAct_9fa48("1520") ? `` : (stryCov_9fa48("1520"), `get: ${userEventsKey}:*`));
          const combinedKeysForFeedsReferences = stryMutAct_9fa48("1521") ? [] : (stryCov_9fa48("1521"), [...childEventsOfRedis, ...userEventsOfRedis]);
          const eventMap = new Set();
          const currentTime = MOMENT().utc();
          const pipeline = redis.pipeline();
          const allHashMapsOfRedis = stryMutAct_9fa48("1522") ? [] : (stryCov_9fa48("1522"), [...eventHashMapsOfRedis, ...fundraiserHashMapOfRedis, ...postHashMapOfRedis]);
          CONSOLE_LOGGER.time(stryMutAct_9fa48("1523") ? "" : (stryCov_9fa48("1523"), 'creating eventMap'));
          for (const key of allHashMapsOfRedis) {
            if (stryMutAct_9fa48("1524")) {
              {}
            } else {
              stryCov_9fa48("1524");
              const eventData = await redis.hget(key, stryMutAct_9fa48("1525") ? "" : (stryCov_9fa48("1525"), 'details'));
              const event = JSON.parse(eventData);
              if (stryMutAct_9fa48("1528") ? MOMENT(event.endDateTime).isValid() && MOMENT(event.endDateTime).isBefore(currentTime) || event.eventType === CONSTANTS.EVENT_TYPES.EVENT : stryMutAct_9fa48("1527") ? false : stryMutAct_9fa48("1526") ? true : (stryCov_9fa48("1526", "1527", "1528"), (stryMutAct_9fa48("1530") ? MOMENT(event.endDateTime).isValid() || MOMENT(event.endDateTime).isBefore(currentTime) : stryMutAct_9fa48("1529") ? true : (stryCov_9fa48("1529", "1530"), MOMENT(event.endDateTime).isValid() && MOMENT(event.endDateTime).isBefore(currentTime))) && (stryMutAct_9fa48("1532") ? event.eventType !== CONSTANTS.EVENT_TYPES.EVENT : stryMutAct_9fa48("1531") ? true : (stryCov_9fa48("1531", "1532"), event.eventType === CONSTANTS.EVENT_TYPES.EVENT)))) {
                if (stryMutAct_9fa48("1533")) {
                  {}
                } else {
                  stryCov_9fa48("1533");
                  eventMap.add(key.split(stryMutAct_9fa48("1534") ? "" : (stryCov_9fa48("1534"), ':')).pop());
                }
              }
              if (stryMutAct_9fa48("1537") ? MOMENT(event.endDate).isValid() && MOMENT(event.endDate).isBefore(currentTime) || event.isFundraiser === true : stryMutAct_9fa48("1536") ? false : stryMutAct_9fa48("1535") ? true : (stryCov_9fa48("1535", "1536", "1537"), (stryMutAct_9fa48("1539") ? MOMENT(event.endDate).isValid() || MOMENT(event.endDate).isBefore(currentTime) : stryMutAct_9fa48("1538") ? true : (stryCov_9fa48("1538", "1539"), MOMENT(event.endDate).isValid() && MOMENT(event.endDate).isBefore(currentTime))) && (stryMutAct_9fa48("1541") ? event.isFundraiser !== true : stryMutAct_9fa48("1540") ? true : (stryCov_9fa48("1540", "1541"), event.isFundraiser === (stryMutAct_9fa48("1542") ? false : (stryCov_9fa48("1542"), true)))))) {
                if (stryMutAct_9fa48("1543")) {
                  {}
                } else {
                  stryCov_9fa48("1543");
                  eventMap.add(key.split(stryMutAct_9fa48("1544") ? "" : (stryCov_9fa48("1544"), ':')).pop());
                }
              }
              if (stryMutAct_9fa48("1547") ? MOMENT(event.publishedDate).isValid() && MOMENT(event.publishedDate).add(expirationDays, 'days').isBefore(currentTime) || event.isPost === true : stryMutAct_9fa48("1546") ? false : stryMutAct_9fa48("1545") ? true : (stryCov_9fa48("1545", "1546", "1547"), (stryMutAct_9fa48("1549") ? MOMENT(event.publishedDate).isValid() || MOMENT(event.publishedDate).add(expirationDays, 'days').isBefore(currentTime) : stryMutAct_9fa48("1548") ? true : (stryCov_9fa48("1548", "1549"), MOMENT(event.publishedDate).isValid() && MOMENT(event.publishedDate).add(expirationDays, stryMutAct_9fa48("1550") ? "" : (stryCov_9fa48("1550"), 'days')).isBefore(currentTime))) && (stryMutAct_9fa48("1552") ? event.isPost !== true : stryMutAct_9fa48("1551") ? true : (stryCov_9fa48("1551", "1552"), event.isPost === (stryMutAct_9fa48("1553") ? false : (stryCov_9fa48("1553"), true)))))) {
                if (stryMutAct_9fa48("1554")) {
                  {}
                } else {
                  stryCov_9fa48("1554");
                  eventMap.add(key.split(stryMutAct_9fa48("1555") ? "" : (stryCov_9fa48("1555"), ':')).pop());
                }
              }
            }
          }
          CONSOLE_LOGGER.timeEnd(stryMutAct_9fa48("1556") ? "" : (stryCov_9fa48("1556"), 'creating eventMap'));
          CONSOLE_LOGGER.time(stryMutAct_9fa48("1557") ? "" : (stryCov_9fa48("1557"), 'Remove event from the child feeds'));
          for (const key of combinedKeysForFeedsReferences) {
            if (stryMutAct_9fa48("1558")) {
              {}
            } else {
              stryCov_9fa48("1558");
              const isChildFeed = key.includes(stryMutAct_9fa48("1559") ? "" : (stryCov_9fa48("1559"), 'child-events'));
              const feedsReferences = await redis.zrevrangebyscore(key, stryMutAct_9fa48("1560") ? "" : (stryCov_9fa48("1560"), '+inf'), stryMutAct_9fa48("1561") ? "" : (stryCov_9fa48("1561"), '-inf'));
              for (const feedReference of feedsReferences) {
                if (stryMutAct_9fa48("1562")) {
                  {}
                } else {
                  stryCov_9fa48("1562");
                  const event = JSON.parse(feedReference);
                  if (stryMutAct_9fa48("1564") ? false : stryMutAct_9fa48("1563") ? true : (stryCov_9fa48("1563", "1564"), eventMap.has(event.postId))) {
                    if (stryMutAct_9fa48("1565")) {
                      {}
                    } else {
                      stryCov_9fa48("1565");
                      pipeline.zrem(key, feedReference);
                      CONSOLE_LOGGER.info(stryMutAct_9fa48("1566") ? `` : (stryCov_9fa48("1566"), `Post Feed removed from key: ${key}`), event);
                      if (stryMutAct_9fa48("1568") ? false : stryMutAct_9fa48("1567") ? true : (stryCov_9fa48("1567", "1568"), isChildFeed)) {
                        if (stryMutAct_9fa48("1569")) {
                          {}
                        } else {
                          stryCov_9fa48("1569");
                          try {
                            if (stryMutAct_9fa48("1570")) {
                              {}
                            } else {
                              stryCov_9fa48("1570");
                              await client.update(stryMutAct_9fa48("1571") ? {} : (stryCov_9fa48("1571"), {
                                index: stryMutAct_9fa48("1572") ? "" : (stryCov_9fa48("1572"), 'children'),
                                id: event.childId,
                                body: stryMutAct_9fa48("1573") ? {} : (stryCov_9fa48("1573"), {
                                  script: stryMutAct_9fa48("1574") ? {} : (stryCov_9fa48("1574"), {
                                    source: stryMutAct_9fa48("1575") ? "" : (stryCov_9fa48("1575"), 'ctx._source.childPosts.removeIf(item -> item == params.tag)'),
                                    lang: stryMutAct_9fa48("1576") ? "" : (stryCov_9fa48("1576"), 'painless'),
                                    params: stryMutAct_9fa48("1577") ? {} : (stryCov_9fa48("1577"), {
                                      tag: event.postId
                                    })
                                  })
                                })
                              }));
                            }
                          } catch (error) {
                            if (stryMutAct_9fa48("1578")) {
                              {}
                            } else {
                              stryCov_9fa48("1578");
                              CONSOLE_LOGGER.error(stryMutAct_9fa48("1579") ? `` : (stryCov_9fa48("1579"), `Error updating childPosts for child: ${event.childId}`), error);
                            }
                          }
                        }
                      }
                    }
                  } else if (stryMutAct_9fa48("1581") ? false : stryMutAct_9fa48("1580") ? true : (stryCov_9fa48("1580", "1581"), eventMap.has(event.eventId))) {
                    if (stryMutAct_9fa48("1582")) {
                      {}
                    } else {
                      stryCov_9fa48("1582");
                      pipeline.zrem(key, feedReference);
                      CONSOLE_LOGGER.info(stryMutAct_9fa48("1583") ? `` : (stryCov_9fa48("1583"), `Feed removed from key: ${key}`), event);
                      if (stryMutAct_9fa48("1585") ? false : stryMutAct_9fa48("1584") ? true : (stryCov_9fa48("1584", "1585"), isChildFeed)) {
                        if (stryMutAct_9fa48("1586")) {
                          {}
                        } else {
                          stryCov_9fa48("1586");
                          try {
                            if (stryMutAct_9fa48("1587")) {
                              {}
                            } else {
                              stryCov_9fa48("1587");
                              await client.update(stryMutAct_9fa48("1588") ? {} : (stryCov_9fa48("1588"), {
                                index: stryMutAct_9fa48("1589") ? "" : (stryCov_9fa48("1589"), 'children'),
                                id: event.childId,
                                body: stryMutAct_9fa48("1590") ? {} : (stryCov_9fa48("1590"), {
                                  script: stryMutAct_9fa48("1591") ? {} : (stryCov_9fa48("1591"), {
                                    source: stryMutAct_9fa48("1592") ? "" : (stryCov_9fa48("1592"), 'ctx._source.childFeeds.removeIf(item -> item == params.tag)'),
                                    lang: stryMutAct_9fa48("1593") ? "" : (stryCov_9fa48("1593"), 'painless'),
                                    params: stryMutAct_9fa48("1594") ? {} : (stryCov_9fa48("1594"), {
                                      tag: event.eventId
                                    })
                                  })
                                })
                              }));
                            }
                          } catch (error) {
                            if (stryMutAct_9fa48("1595")) {
                              {}
                            } else {
                              stryCov_9fa48("1595");
                              CONSOLE_LOGGER.error(stryMutAct_9fa48("1596") ? `` : (stryCov_9fa48("1596"), `Error updating childFeeds for child: ${event.childId}`), error);
                            }
                          }
                        }
                      }
                    }
                  } else if (stryMutAct_9fa48("1598") ? false : stryMutAct_9fa48("1597") ? true : (stryCov_9fa48("1597", "1598"), eventMap.has(event.fundraiserId))) {
                    if (stryMutAct_9fa48("1599")) {
                      {}
                    } else {
                      stryCov_9fa48("1599");
                      pipeline.zrem(key, feedReference);
                      CONSOLE_LOGGER.info(stryMutAct_9fa48("1600") ? `` : (stryCov_9fa48("1600"), `Fundraiser feed removed from key: ${key}`), event);
                      if (stryMutAct_9fa48("1602") ? false : stryMutAct_9fa48("1601") ? true : (stryCov_9fa48("1601", "1602"), isChildFeed)) {
                        if (stryMutAct_9fa48("1603")) {
                          {}
                        } else {
                          stryCov_9fa48("1603");
                          try {
                            if (stryMutAct_9fa48("1604")) {
                              {}
                            } else {
                              stryCov_9fa48("1604");
                              await client.update(stryMutAct_9fa48("1605") ? {} : (stryCov_9fa48("1605"), {
                                index: stryMutAct_9fa48("1606") ? "" : (stryCov_9fa48("1606"), 'children'),
                                id: event.childId,
                                body: stryMutAct_9fa48("1607") ? {} : (stryCov_9fa48("1607"), {
                                  script: stryMutAct_9fa48("1608") ? {} : (stryCov_9fa48("1608"), {
                                    source: stryMutAct_9fa48("1609") ? "" : (stryCov_9fa48("1609"), 'ctx._source.childFundraiserFeeds.removeIf(item -> item == params.tag)'),
                                    lang: stryMutAct_9fa48("1610") ? "" : (stryCov_9fa48("1610"), 'painless'),
                                    params: stryMutAct_9fa48("1611") ? {} : (stryCov_9fa48("1611"), {
                                      tag: event.fundraiserId
                                    })
                                  })
                                })
                              }));
                            }
                          } catch (error) {
                            if (stryMutAct_9fa48("1612")) {
                              {}
                            } else {
                              stryCov_9fa48("1612");
                              CONSOLE_LOGGER.error(stryMutAct_9fa48("1613") ? `` : (stryCov_9fa48("1613"), `Error updating childFundraiserFeeds for child: ${event.childId}`), error);
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          await pipeline.exec();
          CONSOLE_LOGGER.timeEnd(stryMutAct_9fa48("1614") ? "" : (stryCov_9fa48("1614"), 'Remove event from the child feeds'));
          const finalMemoryUsage = await this.getMemoryUsage();
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1615") ? `` : (stryCov_9fa48("1615"), `Final Redis memory usage: ${finalMemoryUsage}`));
        }
      } catch (error) {
        if (stryMutAct_9fa48("1616")) {
          {}
        } else {
          stryCov_9fa48("1616");
          CONSOLE_LOGGER.error(stringify(error), stryMutAct_9fa48("1617") ? "" : (stryCov_9fa48("1617"), 'Error updating child feeds'));
        }
      }
    }
  }
  static async getEventHashMapsOfRedis(key) {
    if (stryMutAct_9fa48("1618")) {
      {}
    } else {
      stryCov_9fa48("1618");
      return await redis.keys(key);
    }
  }
  static async getMemoryUsage() {
    if (stryMutAct_9fa48("1619")) {
      {}
    } else {
      stryCov_9fa48("1619");
      return await redis.info(stryMutAct_9fa48("1620") ? "" : (stryCov_9fa48("1620"), 'memory'));
    }
  }
  static async getPostExpirationDays() {
    if (stryMutAct_9fa48("1621")) {
      {}
    } else {
      stryCov_9fa48("1621");
      try {
        if (stryMutAct_9fa48("1622")) {
          {}
        } else {
          stryCov_9fa48("1622");
          const postExpirationConstant = await ConstantModel.get(CONSTANTS.POST_EXPIRATION_DAYS);
          const expirationDays = parseInt(stryMutAct_9fa48("1623") ? postExpirationConstant.value : (stryCov_9fa48("1623"), postExpirationConstant?.value), 10);
          if (stryMutAct_9fa48("1626") ? isNaN(expirationDays) && expirationDays <= 0 : stryMutAct_9fa48("1625") ? false : stryMutAct_9fa48("1624") ? true : (stryCov_9fa48("1624", "1625", "1626"), isNaN(expirationDays) || (stryMutAct_9fa48("1629") ? expirationDays > 0 : stryMutAct_9fa48("1628") ? expirationDays < 0 : stryMutAct_9fa48("1627") ? false : (stryCov_9fa48("1627", "1628", "1629"), expirationDays <= 0)))) {
            if (stryMutAct_9fa48("1630")) {
              {}
            } else {
              stryCov_9fa48("1630");
              throw new Error(stryMutAct_9fa48("1631") ? "" : (stryCov_9fa48("1631"), 'Invalid expiration days value'));
            }
          }
          return expirationDays;
        }
      } catch (error) {
        if (stryMutAct_9fa48("1632")) {
          {}
        } else {
          stryCov_9fa48("1632");
          CONSOLE_LOGGER.info(stryMutAct_9fa48("1633") ? "" : (stryCov_9fa48("1633"), 'Error retrieving post expiration days constant:'), error.message);
          return 15;
        }
      }
    }
  }
}
module.exports = UpdateFeedsService;