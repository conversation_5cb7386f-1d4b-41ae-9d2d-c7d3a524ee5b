// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const jwt = require('jsonwebtoken');
const Utils = require('../util/utilFunctions');
const User = require('../models/user.model');
const HTTPStatus = require('../util/http-status');

/**
 * @desc This function is being used to authenticate each private request
 * <AUTHOR>
 * @since 19/10/2023
 * @param {Object} req Request req.headers RequestBody req.headers.accessToken accessToken
 * @param {Object} res Response
 * @param {function} next exceptionHandler Calls exceptionHandler
 */
const checkUser = (me, res, next) => {
  if (stryMutAct_9fa48("60")) {
    {}
  } else {
    stryCov_9fa48("60");
    User.get(stryMutAct_9fa48("61") ? {} : (stryCov_9fa48("61"), {
      id: me.sub,
      email: me.email
    })).then(userObj => {
      if (stryMutAct_9fa48("62")) {
        {}
      } else {
        stryCov_9fa48("62");
        const responseObject = Utils.errorResponse();
        if (stryMutAct_9fa48("65") ? false : stryMutAct_9fa48("64") ? true : stryMutAct_9fa48("63") ? userObj : (stryCov_9fa48("63", "64", "65"), !userObj)) {
          if (stryMutAct_9fa48("66")) {
            {}
          } else {
            stryCov_9fa48("66");
            responseObject.message = res.__(stryMutAct_9fa48("67") ? "" : (stryCov_9fa48("67"), 'ACCESS_DENIED'));
            res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
            return;
          }
        } else if (stryMutAct_9fa48("70") ? userObj.status !== CONSTANTS.STATUS.ACTIVE && !userObj.isVerified : stryMutAct_9fa48("69") ? false : stryMutAct_9fa48("68") ? true : (stryCov_9fa48("68", "69", "70"), (stryMutAct_9fa48("72") ? userObj.status === CONSTANTS.STATUS.ACTIVE : stryMutAct_9fa48("71") ? false : (stryCov_9fa48("71", "72"), userObj.status !== CONSTANTS.STATUS.ACTIVE)) || (stryMutAct_9fa48("73") ? userObj.isVerified : (stryCov_9fa48("73"), !userObj.isVerified)))) {
          if (stryMutAct_9fa48("74")) {
            {}
          } else {
            stryCov_9fa48("74");
            responseObject.data = stryMutAct_9fa48("75") ? {} : (stryCov_9fa48("75"), {
              status: userObj.status,
              message: res.__(stryMutAct_9fa48("76") ? "" : (stryCov_9fa48("76"), 'DEACTIVATE_ACCOUNT_BY_ADMIN'))
            });
            res.status(HTTPStatus.ACCOUNT_SUSPENDED).send(responseObject);
            return;
          }
        } else {
          // Do nothing...
        }
        res.locals.user = userObj;
        next();
      }
    }).catch(next);
  }
};
module.exports = function (req, res, next) {
  if (stryMutAct_9fa48("77")) {
    {}
  } else {
    stryCov_9fa48("77");
    try {
      if (stryMutAct_9fa48("78")) {
        {}
      } else {
        stryCov_9fa48("78");
        const token = req.headers.authorization.split(stryMutAct_9fa48("79") ? "" : (stryCov_9fa48("79"), ' '))[1];
        const decodedJwt = jwt.decode(token, stryMutAct_9fa48("80") ? {} : (stryCov_9fa48("80"), {
          complete: stryMutAct_9fa48("81") ? false : (stryCov_9fa48("81"), true)
        }));
        const decodedJwtPayload = decodedJwt.payload;
        checkUser(decodedJwtPayload, res, next);
      }
    } catch (err) {
      if (stryMutAct_9fa48("82")) {
        {}
      } else {
        stryCov_9fa48("82");
        const responseObject = Utils.errorResponse();
        responseObject.message = res.__(stryMutAct_9fa48("83") ? "" : (stryCov_9fa48("83"), 'ACCESS_DENIED'));
        res.status(HTTPStatus.UNAUTHORIZED).send(responseObject);
      }
    }
  }
};