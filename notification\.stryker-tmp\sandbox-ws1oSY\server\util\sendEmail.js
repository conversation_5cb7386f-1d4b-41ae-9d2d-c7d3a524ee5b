// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const fs = require('fs');
const {
  promisify
} = require('util');
const Constants = require('./constants');
const readFileAsync = promisify(fs.readFile);
const {
  SESClient,
  SendEmailCommand
} = require('@aws-sdk/client-ses');
const ses = new SESClient(stryMutAct_9fa48("1915") ? {} : (stryCov_9fa48("1915"), {
  region: process.env.SES_REGION
}));
class EmailService {
  /**
   * @desc This function is used to send email without attachments
   * <AUTHOR>
   * @param {Array} email array of email addresses
   * @param {String} subject subject of the email
   * @param {String} template location of the email template
   * @param {Object} templateVariables dynamic variables included in the email template
   * @since 26/09/2023
   */
  static async prepareAndSendEmail(email, subject, template, templateVariables) {
    if (stryMutAct_9fa48("1916")) {
      {}
    } else {
      stryCov_9fa48("1916");
      if (stryMutAct_9fa48("1919") ? process.env.NODE_ENV === 'testing' : stryMutAct_9fa48("1918") ? false : stryMutAct_9fa48("1917") ? true : (stryCov_9fa48("1917", "1918", "1919"), process.env.NODE_ENV !== (stryMutAct_9fa48("1920") ? "" : (stryCov_9fa48("1920"), 'testing')))) {
        if (stryMutAct_9fa48("1921")) {
          {}
        } else {
          stryCov_9fa48("1921");
          try {
            if (stryMutAct_9fa48("1922")) {
              {}
            } else {
              stryCov_9fa48("1922");
              let htmlMessage = await readFileAsync(template, stryMutAct_9fa48("1923") ? "" : (stryCov_9fa48("1923"), 'utf8'));
              templateVariables.year = MOMENT().year();
              for (const [key, value] of Object.entries(templateVariables)) {
                if (stryMutAct_9fa48("1924")) {
                  {}
                } else {
                  stryCov_9fa48("1924");
                  htmlMessage = htmlMessage.replace(new RegExp(stryMutAct_9fa48("1925") ? `` : (stryCov_9fa48("1925"), `##${stryMutAct_9fa48("1926") ? key.toLowerCase() : (stryCov_9fa48("1926"), key.toUpperCase())}`), stryMutAct_9fa48("1927") ? "" : (stryCov_9fa48("1927"), 'g')), value);
                }
              }
              const command = new SendEmailCommand(stryMutAct_9fa48("1928") ? {} : (stryCov_9fa48("1928"), {
                Destination: stryMutAct_9fa48("1929") ? {} : (stryCov_9fa48("1929"), {
                  ToAddresses: email
                }),
                Message: stryMutAct_9fa48("1930") ? {} : (stryCov_9fa48("1930"), {
                  Body: stryMutAct_9fa48("1931") ? {} : (stryCov_9fa48("1931"), {
                    Html: stryMutAct_9fa48("1932") ? {} : (stryCov_9fa48("1932"), {
                      Charset: stryMutAct_9fa48("1933") ? "" : (stryCov_9fa48("1933"), 'UTF-8'),
                      Data: htmlMessage
                    })
                  }),
                  Subject: stryMutAct_9fa48("1934") ? {} : (stryCov_9fa48("1934"), {
                    Charset: stryMutAct_9fa48("1935") ? "" : (stryCov_9fa48("1935"), 'UTF-8'),
                    Data: subject
                  })
                }),
                ReturnPath: Constants.DEVELOPERS_EMAIL,
                Source: Constants.DEVELOPERS_EMAIL
              }));
              await ses.send(command);
            }
          } catch (error) {
            if (stryMutAct_9fa48("1936")) {
              {}
            } else {
              stryCov_9fa48("1936");
              CONSOLE_LOGGER.info(stryMutAct_9fa48("1937") ? "" : (stryCov_9fa48("1937"), 'Error while sending email to '), email, JSON.stringify(error));
            }
          }
        }
      }
    }
  }
}
module.exports = EmailService;