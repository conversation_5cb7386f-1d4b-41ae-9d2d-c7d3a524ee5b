// @ts-nocheck
const sinon = require('sinon');
const SqsService = require('../sqsService');
const axios = require('axios');
const assert = require('assert');
const { google } = require('googleapis');
const { SQSClient } = require('@aws-sdk/client-sqs');
const NotificationModel = require('../../models/notification.model');
const ChildModel = require('../../models/child.model');

describe('SqsService', () => {
    let sandbox;
    before(() => {
        sandbox = sinon.createSandbox();
        process.env.NOTIFICATION_PRIVATE_KEY = 'testkey';
    });
    after(() => {
        sandbox.restore();
        sinon.restore();
    });

    it('should pull event from queue and process notification for requestedBy notifications', async () => {
        const event = {
            Records: [
                {
                    eventSource: 'aws:sqs',
                    body: JSON.stringify({
                        trigger: 'requestedBy',
                        childId: 'child1',
                        title: 'title',
                        message: 'message',
                        topicKey: 'topicKey',
                        eventSignUp: {
                            parentId: 'parentId',
                            eventId: 'eventId'
                        }
                    }),
                    receiptHandle: 'receiptHandle'
                }
            ]
        };
        const context = {};
        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const sendStub = sandbox.stub(SQSClient.prototype, 'send');
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();
        const childGetStub = sinon.stub(ChildModel, 'get').resolves({ guardians: ['guardian1'] });
        await SqsService.pullEventFromQueue(event, context);
        sinon.assert.calledOnce(axiosPostStub);
        childGetStub.restore();
        pushInNotificationStub.restore();
        jwtClientAuthorizeStub.restore();
        axiosPostStub.restore();
        sendStub.restore();
    });

    it('should pull event from queue and process notification for eventSignup notifications', async () => {
        const event = {
            Records: [
                {
                    eventSource: 'aws:sqs',
                    body: JSON.stringify({
                        trigger: 'eventSignup',
                        childId: 'child1',
                        title: 'title',
                        message: 'message',
                        topicKey: 'topicKey',
                        eventSignUp: {
                            parentId: 'parentId',
                            eventId: 'eventId'
                        },
                        eventDetails: {},
                        childDetails: {},
                        score: 0
                    }),
                    receiptHandle: 'receiptHandle'
                }
            ]
        };
        const context = {};

        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const sendStub = sandbox.stub(SQSClient.prototype, 'send');
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();
        const childGetStub = sinon.stub(ChildModel, 'get').resolves({ guardians: ['guardian1'] });
        await SqsService.pullEventFromQueue(event, context);
        childGetStub.restore();
        sinon.assert.calledOnce(axiosPostStub);
        jwtClientAuthorizeStub.restore();
        axiosPostStub.restore();
        sendStub.restore();
        pushInNotificationStub.restore();
    });

    it('should pull event from queue and process notification for fundraiser signup notifications', async () => {
        const event = {
            Records: [
                {
                    eventSource: 'aws:sqs',
                    body: JSON.stringify({
                        trigger: 'fundraiserSignup',
                        childId: 'child1',
                        title: 'title',
                        message: 'message',
                        topicKey: 'topicKey',
                        eventSignUp: {
                            parentId: 'parentId',
                            eventId: 'eventId'
                        },
                        eventDetails: {},
                        childDetails: {},
                        score: 0
                    }),
                    receiptHandle: 'receiptHandle'
                }
            ]
        };
        const context = {};

        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const sendStub = sandbox.stub(SQSClient.prototype, 'send');
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();
        const childGetStub = sinon.stub(ChildModel, 'get').resolves({ firstName: 'child1', lastName: 'test', guardians: ['guardian1'] });
        await SqsService.pullEventFromQueue(event, context);
        childGetStub.restore();
        sinon.assert.calledOnce(axiosPostStub);
        jwtClientAuthorizeStub.restore();
        axiosPostStub.restore();
        sendStub.restore();
        pushInNotificationStub.restore();
    });

    it('should pull event from queue and process notification for fundraiser signup booster donation notifications', async () => {
        const event = {
            Records: [
                {
                    eventSource: 'aws:sqs',
                    body: JSON.stringify({
                        topicKey: 'topicKey',
                        trigger: 'boosterDonation',
                        childId: 'child1',
                        title: 'title',
                        message: 'message',
                        fundraiserSignupId: 'fundraiserSignupId',
                        amount: 100
                    })
                }
            ]
        };
        const context = {};

        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const sendStub = sandbox.stub(SQSClient.prototype, 'send');
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();
        const childGetStub = sinon.stub(ChildModel, 'get').resolves({ guardians: ['guardian1'] });
        await SqsService.pullEventFromQueue(event, context);
        childGetStub.restore();
        sinon.assert.calledOnce(axiosPostStub);
        jwtClientAuthorizeStub.restore();
        axiosPostStub.restore();
        sendStub.restore();
        pushInNotificationStub.restore();
    });

    it('should pull event from queue and process notification for postCreation notifications', async () => {
        const event = {
            Records: [
                {
                    eventSource: 'aws:sqs',
                    body: JSON.stringify({
                        topicKey: 'topicKey',
                        trigger: 'postCreated',
                        childId: 'child1',
                        title: 'title',
                        message: 'message',
                        isPost: true,
                        postDetails: {
                            id: 'postId',
                            publishedDate: 'publishedDate',
                            title: 'title',
                            subTitle: 'subTitle',
                            content: 'content'
                        },
                        childDetails: {
                            id: 'childId'
                        },
                        score: 0
                    }),
                    receiptHandle: 'receiptHandle'
                }
            ]
        };
        const context = {};

        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const sendStub = sandbox.stub(SQSClient.prototype, 'send');
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();
        const childGetStub = sinon.stub(ChildModel, 'get').resolves(
            {
                guardians: ['guardian1'],
                firstName: 'firstName',
                lastName: 'lastName',
                photoURL: 'photoURL',
                associatedColor: 'associatedColor'
            });
        await SqsService.pullEventFromQueue(event, context);
        childGetStub.restore();
        sinon.assert.calledOnce(axiosPostStub);
        jwtClientAuthorizeStub.restore();
        axiosPostStub.restore();
        sendStub.restore();
        pushInNotificationStub.restore();
    });

    it('should pull event from queue and process notification for event update notifications', async () => {
        const event = {
            Records: [
                {
                    eventSource: 'aws:sqs',
                    body: JSON.stringify({
                        trigger: 'eventSignupUpdate',
                        childId: 'child1',
                        title: 'title',
                        message: 'message',
                        topicKey: 'topicKey',
                        eventSignUp: {
                            parentId: 'parentId',
                            eventId: 'eventId'
                        },
                        eventDetails: {},
                        childDetails: {},
                        score: 0
                    }),
                    receiptHandle: 'receiptHandle'
                }
            ]
        };
        const context = {};

        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(null, { access_token: 'fake_token' });
        });
        const sendStub = sandbox.stub(SQSClient.prototype, 'send');
        const pushInNotificationStub = sinon.stub(NotificationModel, 'create').resolves();
        const childGetStub = sinon.stub(ChildModel, 'get').resolves({ guardians: ['guardian1'] });
        await SqsService.pullEventFromQueue(event, context);
        childGetStub.restore();
        sinon.assert.calledOnce(axiosPostStub);
        jwtClientAuthorizeStub.restore();
        axiosPostStub.restore();
        sendStub.restore();
        pushInNotificationStub.restore();
    });

    it('should handle error when jwtClient authorize fails', async () => {
        const event = {
            Records: [
                {
                    eventSource: 'aws:sqs',
                    body: JSON.stringify({
                        trigger: 'eventSignup',
                        childId: 'child1',
                        title: 'title',
                        message: 'message',
                        topicKey: 'topicKey',
                        eventSignUp: {},
                        eventDetails: {},
                        childDetails: {},
                        score: 0
                    }),
                    receiptHandle: 'receiptHandle'
                }
            ]
        };
        const context = {};

        const axiosPostStub = sinon.stub(axios, 'post').resolves({ data: {} });
        const jwtClientAuthorizeStub = sinon.stub(google.auth.JWT.prototype, 'authorize').callsFake((callback) => {
            callback(new Error('Authorization failed'));
        });
        sinon.stub(ChildModel, 'get').resolves({ guardians: ['guardian1'] });

        try {
            await SqsService.pullEventFromQueue(event, context);
        } catch (error) {
            assert.equal(error.message, 'Authorization failed');
        }

        sinon.assert.notCalled(axiosPostStub);
        axiosPostStub.restore();
        ChildModel.get.restore();
        jwtClientAuthorizeStub.restore();
    });
});
