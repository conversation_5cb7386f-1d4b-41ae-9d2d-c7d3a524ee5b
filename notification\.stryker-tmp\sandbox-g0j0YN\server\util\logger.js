/**
 * DEBUG < INFO <  ERROR
 * @name logger
 */function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
class Logger {
  /**
   * @desc This function is being used to get debug logs
   * <AUTHOR>
   * @since 01/03/2021
   */
  static debug() {
    if (stryMutAct_9fa48("1860")) {
      {}
    } else {
      stryCov_9fa48("1860");
      console.log.apply(null, arguments);
    }
  }

  /**
   * @desc This function is being used to get info logs
   * <AUTHOR>
   * @since 01/03/2021
   */
  static info() {
    if (stryMutAct_9fa48("1861")) {
      {}
    } else {
      stryCov_9fa48("1861");
      if (stryMutAct_9fa48("1863") ? false : stryMutAct_9fa48("1862") ? true : (stryCov_9fa48("1862", "1863"), arguments.length)) {
        if (stryMutAct_9fa48("1864")) {
          {}
        } else {
          stryCov_9fa48("1864");
          console.info.apply(MOMENT()._d, arguments);
        }
      }
    }
  }

  /**
   * @desc This function is being used to error logs
   * <AUTHOR>
   * @since 01/03/2021
   */
  static error() {
    if (stryMutAct_9fa48("1865")) {
      {}
    } else {
      stryCov_9fa48("1865");
      console.error.apply(null, arguments);
    }
  }

  /**
   * @desc This function is being used to error logs
   * <AUTHOR>
   * @since 12/01/2024
   */
  static time() {
    if (stryMutAct_9fa48("1866")) {
      {}
    } else {
      stryCov_9fa48("1866");
      console.time.apply(null, arguments);
    }
  }

  /**
   * @desc This function is being used to error logs
   * <AUTHOR>
   * @since 12/01/2024
   */
  static timeEnd() {
    if (stryMutAct_9fa48("1867")) {
      {}
    } else {
      stryCov_9fa48("1867");
      console.timeEnd.apply(null, arguments);
    }
  }
}
module.exports = Logger;