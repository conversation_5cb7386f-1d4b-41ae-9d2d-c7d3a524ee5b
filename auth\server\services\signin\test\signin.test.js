const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
const TestCase = require('./testcaseSignin');
const sinon = require('sinon');
const Cognito = require('../../../util/cognito');
const User = require('../../../models/user.model');
const Child = require('../../../models/child.model');
const Organization = require('../../../models/organization.model');
chai.use(chaiHttp);
const Crypt = require('../../../util/crypt');
const jwt = require('jsonwebtoken');
const SigninService = require('../signInService');
const PendingParentInvite = require('../../../models/pendingPartnerInvite.model');
const Utils = require('../../../util/utilFunctions');

// eslint-disable-next-line max-len
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************.5r_dLXJ-VkWcspcZjbEahERROcsSUuyCO5ac5GKSV_o';

Utils.addCommonReqTokenForHMac(request);
describe('Signin Account', () => {
    try {
        TestCase.signinAccount.forEach((data) => {
            it(data.it, (done) => {
                request(process.env.BASE_URL)
                    .post('/auth/signin')
                    .send(data.options)
                    .end((err, res) => {
                        expect(res.body.status).to.be.status;
                        assert.equal(res.statusCode, 400);
                        done();
                    });
            });
        });

        it('As a user, I should validate if email is not registered', (done) => {
            const loginUser = {
                'email': '<EMAIL>',
                'password': '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267d',
                'fcmToken': 'test-fcm-token'
            };

            const scanStub = sinon.stub();
            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const saveStub = sinon.stub().resolves();

            scanStub.returns({ eq: eqStub });
            eqStub.returns({ exec: execStub });
            execStub.resolves({ count: 0 });
            sinon.replace(User, 'scan', scanStub);
            sinon.replace(User.prototype, 'save', saveStub);

            sinon.stub(Cognito, 'login').returns();

            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, 0);
                    assert.equal(res.statusCode, 401);
                    sinon.restore();
                    done();
                });
        });

        it('As a user, I should validate if valid password but user is not active', (done) => {
            const loginUser = {
                'email': '<EMAIL>',
                'password': '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267d',
                'fcmToken': 'test-fcm-token'
            };

            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const saveStub = sinon.stub().resolves();

            eqStub.returns({ exec: execStub });
            execStub.resolves({ count: 1, toJSON: () => { return [
                { status: 'inactive', isVerified: 0, isDeleted: 0, id: '**********' }]; } });
            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        not: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ status: 'inactive', isVerified: 0, isDeleted: 0, id: '**********' }])
                            })
                        })
                    })
                })
            });

            sinon.replace(User.prototype, 'save', saveStub);

            sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: token, RefreshToken: 'refresh-token' } });

            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, 0);
                    assert.equal(res.statusCode, 423);
                    sinon.restore();
                    done();
                });
        });

        it('As a user, I should validate if user exists in db', (done) => {
            const loginUser = {
                'email': '<EMAIL>',
                'password': '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267d',
                'fcmToken': 'test-fcm-token'
            };

            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const saveStub = sinon.stub().resolves();

            eqStub.returns({ exec: execStub });
            execStub.resolves({ count: 1, toJSON: () => { return [
                { status: 'inactive', isVerified: 0, isDeleted: 0, id: '**********' }]; } });
            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        not: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([])
                            })
                        })
                    })
                })
            });
            sinon.replace(User.prototype, 'save', saveStub);

            sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: token, RefreshToken: 'refresh-token' } });

            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, 0);
                    assert.equal(res.statusCode, 401);
                    sinon.restore();
                    done();
                });
        });

        it('As a user, I should validate and login with correct credentials', (done) => {
            const loginUser = {
                'email': '<EMAIL>',
                'password': '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267d',
                'fcmToken': 'test-fcm-token'
            };

            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const saveStub = sinon.stub().resolves();

            eqStub.returns({ exec: execStub });
            execStub.resolves({ count: 1, toJSON: () => { return [
                { status: 'active', isVerified: 1, isDeleted: 0, id: '**********' }]; } });
            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        not: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ status: 'active', isVerified: 1, isDeleted: 0, id: '**********' }])
                            })
                        })
                    })
                })
            });
            sinon.replace(User.prototype, 'save', saveStub);

            sinon.stub(User, 'update').returns({
                status: 'active', isVerified: 1, isDeleted: 0, id: '**********',
                firstName: 'firstName',
                lastName: 'lastName',
                email: 'email',
                countryCode: 'countryCode',
                phoneNumber: 'phoneNumber',
                role: 1,
                children: [],
                associatedOrganizations: [],
                token: 'access-token',
                refreshToken: 'refresh-token' });

            sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: token, RefreshToken: 'refresh-token' } });
            sinon.stub(Crypt, 'getUserToken').returns({ token: '123' });
            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, 1);
                    assert.equal(res.statusCode, 200);
                    sinon.restore();
                    done();
                });
        });

        it('As a user, I should validate and login with correct credentials and get all details', (done) => {
            const loginUser = {
                'email': '<EMAIL>',
                'password': '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267d',
                'fcmToken': 'test-fcm-token'
            };

            const eqStub = sinon.stub();
            const execStub = sinon.stub();
            const saveStub = sinon.stub().resolves();

            eqStub.returns({ exec: execStub });
            execStub.resolves({ count: 1, toJSON: () => { return [
                { status: 'active', isVerified: 1, isDeleted: 0, id: '**********' }]; } });
            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        not: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{ status: 'active', isVerified: 1, isDeleted: 0, id: '**********' }])
                            })
                        })
                    })
                })
            });
            sinon.replace(User.prototype, 'save', saveStub);

            sinon.stub(User, 'update').returns({
                status: 'active', isVerified: 1, isDeleted: 0, id: '**********',
                firstName: 'firstName',
                lastName: 'lastName',
                email: 'email',
                countryCode: 'countryCode',
                phoneNumber: 'phoneNumber',
                role: 1,
                children: ['123'],
                associatedOrganizations: [{ organizationId: '222', role: 3 }],
                token: 'access-token',
                refreshToken: 'refresh-token' });

            sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: token, RefreshToken: 'refresh-token' } });
            sinon.stub(Crypt, 'getUserToken').returns({ token: '123' });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test',
                photoURL: 'https://test.com/test.jpg',
                connections: [{ childId: '123', status: 'connected' }, { childId: '456', status: 'connected' }],
                school: '222'
            },
            {
                id: '123',
                firstName: 'test',
                lastName: 'test',
                photoURL: 'https://test.com/test.jpg',
                connections: [{ childId: 'xyz85616-3769-4b2e-b36b-898597b8146e', status: 'connected' }],
                school: '222'
            },
            {
                id: '456',
                firstName: 'test',
                lastName: 'test',
                school: '222',
                connections: []
            }]);

            const organization = {
                id: '222',
                name: 'test',
                address: 'test',
                country: 'test',
                state: 'test',
                city: 'test',
                category: 'test',
                zipCode: 'test'
            };

            sinon.stub(Organization, 'batchGet').resolves([organization]);

            sinon.stub(Organization, 'get').resolves(organization);

            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, 1);
                    assert.equal(res.statusCode, 200);
                    sinon.restore();
                    done();
                });
        });

        it('As a user, I should validate and login with correct credentials and get details without photoURL', (done) => {
            const loginUser = {
                'email': '<EMAIL>',
                'password': '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267d',
                'fcmToken': 'test-fcm-token'
            };

            const saveStub = sinon.stub().resolves();

            sinon.stub(User, 'query').returns({
                eq: sinon.stub().returns({
                    where: sinon.stub().returns({
                        not: sinon.stub().returns({
                            eq: sinon.stub().returns({
                                exec: sinon.stub().resolves([{
                                    status: 'active',
                                    isVerified: 1,
                                    isDeleted: 0,
                                    id: '**********',
                                    accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT
                                }])
                            })
                        })
                    })
                })
            });
            sinon.replace(User.prototype, 'save', saveStub);

            sinon.stub(User, 'update').returns({
                status: 'active', isVerified: 1, isDeleted: 0, id: '**********',
                firstName: 'firstName',
                lastName: 'lastName',
                email: 'email',
                countryCode: 'countryCode',
                phoneNumber: 'phoneNumber',
                role: 1,
                children: ['123'],
                associatedOrganizations: [{ organizationId: '222', role: 3 }],
                token: 'access-token',
                refreshToken: 'refresh-token',
                accessLevel: CONSTANTS.ACCESS_LEVEL.ROOT
            });

            sinon.stub(Cognito, 'login').returns({ AuthenticationResult: { IdToken: token, RefreshToken: 'refresh-token' } });
            sinon.stub(Crypt, 'getUserToken').returns({ token: '123' });

            sinon.stub(Child, 'batchGet').resolves([{
                id: 'xyz85616-3769-4b2e-b36b-898597b8146e',
                firstName: 'test',
                lastName: 'test',
                connections: []
            }]);

            sinon.stub(Organization, 'batchGet').resolves([
                {
                    isDeleted: 1,
                    address: 'test',
                    country: 'test',
                    name: 'test',
                    state: 'test',
                    city: 'test',
                    category: 'test',
                    id: '222',
                    zipCode: 'test'
                },
                {
                    isDeleted: 0,
                    address: 'test',
                    country: 'test',
                    name: 'test',
                    state: 'test',
                    city: 'test',
                    category: 'test',
                    id: '222',
                    zipCode: 'test'
                },
                {
                    isDeleted: 0,
                    address: 'test',
                    country: 'test',
                    name: 'test',
                    state: 'test',
                    city: 'test',
                    category: CONSTANTS.ORGANIZATION_CATEGORIES.HOME_ROOM,
                    id: '222',
                    zipCode: 'test'
                }
            ]);

            request(process.env.BASE_URL)
                .post('/auth/signin')
                .send(loginUser)
                .end((err, res) => {
                    expect(res.body.status).to.be.status;
                    assert.equal(res.body.status, 1);
                    assert.equal(res.statusCode, 200);
                    sinon.restore();
                    done();
                });
        });
    } catch (exception) {
        CONSOLE_LOGGER.error(exception);
    }
});

describe('Social Signin', () => {
    const invalidSocialToken = 'test-social-signin-token';
    const tokenOptionalInfo = {
        algorithm: 'HS256',
        expiresIn: 86400
    };
    const invalidIssuer = {
        platform: 'google',
        token: 'token',
        firstName: 'test',
        lastName: 'user',
        email: '<EMAIL>',
        picture: 'url',
        id: 'id',
        iss: ['google' ],
        sub: '**********'
    };
    const invalidIssuerToken = {
        token: 'Bearer ' + jwt.sign(invalidIssuer, 'testToken', tokenOptionalInfo)
    };
    const invalidAudience = {
        platform: 'google',
        token: 'token',
        firstName: 'test',
        lastName: 'user',
        email: '<EMAIL>',
        picture: 'url',
        id: 'id',
        iss: ['google', 'accounts.google.com'],
        sub: '**********',
        aud: 'test'
    };
    const invalidAudienceToken = {
        token: 'Bearer ' + jwt.sign(invalidAudience, 'testToken', tokenOptionalInfo)
    };
    const googleUser = {
        platform: 'google',
        token: 'token',
        firstName: 'test',
        lastName: 'user',
        email: '<EMAIL>',
        picture: 'url',
        id: 'id',
        iss: ['google', 'accounts.google.com'],
        sub: '**********'
    };
    const validGoogleToken = {
        token: 'Bearer ' + jwt.sign(googleUser, 'testToken', tokenOptionalInfo)
    };

    const appleUser = {
        platform: 'apple',
        token: 'token',
        firstName: 'test',
        lastName: 'user',
        email: '<EMAIL>',
        picture: 'url',
        id: 'id',
        iss: ['apple'],
        sub: '**********'
    };
    const header = {
        alg: 'HS256',
        typ: 'JWT',
        kid: 'YOUR_KID'
    };
    const validAppleToken = {
        token: 'Bearer ' + jwt.sign(appleUser, 'testToken', { header })
    };

    it('As a user I should get error if token is not passed', (done) => {
        request(process.env.BASE_URL)
            .post('/auth/social-signin')
            .send({})
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 403);
                done();
            });
    });

    it('As a user I should not register as user if platform is invalid', (done) => {
        request(process.env.BASE_URL)
            .post('/auth/social-signin')
            .set({ 'Authorization': 'Bearer ' + invalidSocialToken })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                done();
            });
    });

    it('As a user I should not register as user if issuer is invalid', (done) => {
        request(process.env.BASE_URL)
            .post('/auth/social-signin')
            .set({ 'Authorization': invalidIssuerToken.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                done();
            });
    });

    it('As a user I should not register as user if audience is invalid', (done) => {
        request(process.env.BASE_URL)
            .post('/auth/social-signin')
            .set({ 'Authorization': invalidAudienceToken.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 400);
                done();
            });
    });

    it('As a user I should be able to signin using google', (done) => {
        sinon.stub(User, 'query').returns({
            eq: sinon.stub().returns({
                where: sinon.stub().returns({
                    not: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([{ id: '**********', children: [], associatedOrganizations: [] }])
                        })
                    })
                })
            })
        });
        request(process.env.BASE_URL)
            .post('/auth/social-signin')
            .set({ 'Authorization': validGoogleToken.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                User.query.restore();
                done();
            });
    });

    it('As a user I should be able to signin using google and create new account', (done) => {
        sinon.stub(User, 'query').returns({
            eq: sinon.stub().returns({
                where: sinon.stub().returns({
                    not: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([])
                        })
                    })
                })
            })
        });
        sinon.stub(Crypt, 'getUserToken').resolves({ token: validGoogleToken.token });
        const pendingParentInviteStub = sinon.stub(PendingParentInvite, 'query');
        const pendingParentInviteDeleteStub = sinon.stub(PendingParentInvite, 'delete').resolves({});
        pendingParentInviteStub.withArgs('invitedPartner')
            .returns({
                eq: sinon.stub().returns({
                    using: sinon.stub().returns({
                        exec: sinon.stub().resolves([{
                            inviterPartnerId: '124',
                            inviterPartnerEmail: '<EMAIL>',
                            children: ['59985bdc-77ba-4911-ba98-d49230566444']
                        }])
                    })
                })
            });
        const saveStub = sinon.stub().resolves({ id: '**********', children: [], associatedOrganizations: [] });
        sinon.replace(User.prototype, 'save', saveStub);

        request(process.env.BASE_URL)
            .post('/auth/social-signin')
            .set({ 'Authorization': validGoogleToken.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                sinon.restore();
                pendingParentInviteStub.restore();
                pendingParentInviteDeleteStub.restore();
                done();
            });
    });

    it('As a user I should be able to signin using apple', (done) => {
        sinon.stub(User, 'query').returns({
            eq: sinon.stub().returns({
                where: sinon.stub().returns({
                    not: sinon.stub().returns({
                        eq: sinon.stub().returns({
                            exec: sinon.stub().resolves([{ id: '**********', children: [], associatedOrganizations: [] }])
                        })
                    })
                })
            })
        });

        sinon.stub(SigninService, 'verifyAppleToken').resolves(appleUser);

        sinon.stub(Crypt, 'getUserToken').resolves({ token: validGoogleToken.token });

        const saveStub = sinon.stub().resolves();
        sinon.replace(User.prototype, 'save', saveStub);

        request(process.env.BASE_URL)
            .post('/auth/social-signin')
            .set({ 'Authorization': validAppleToken.token })
            .end((err, res) => {
                expect(res.body.status).to.be.status;
                assert.equal(res.statusCode, 200);
                sinon.restore();
                done();
            });
    });

});
