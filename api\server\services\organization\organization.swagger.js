/**
 *  routes and schema for Organization routes
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      addPtoAdmin:
 *          type: object
 *          required:
 *              - adminFirstName
 *              - adminLastName
 *              - email
 *              - orgName
 *              - address
 *              - parentOrganizationId
 *              - country
 *              - city
 *              - state
 *              - zipCode
 *              - category
 *          properties:
 *              adminFirstName:
 *                  type: string
 *                  description: first name of the org admin
 *              adminLastName:
 *                  type: string
 *                  description: last name of the org admin
 *              email:
 *                  type: string
 *                  description: email of the org admin
 *              orgName:
 *                  type: string
 *                  description: org name
 *              address:
 *                  type: string
 *                  description: address of the org
 *              country:
 *                  type: string
 *                  description: country of the org
 *              city:
 *                  type: string
 *                  description: city of the org
 *              state:
 *                  type: string
 *                  description: state of the org
 *              zipCode:
 *                  type: string
 *                  description: zipCode of org
 *              countryCode:
 *                  type: string
 *                  description: countryCode of org
 *              phoneNumber:
 *                  type: string
 *                  description: phoneNumber of org
 *              category:
 *                  type: string
 *                  description: category of org; one of ['PTO', 'School', 'Homeroom']
 *
 *          example:
 *                   adminFirstName: firstName
 *                   adminLastName: lastName
 *                   email: <EMAIL>
 *                   orgName: org name
 *                   address: org address
 *                   country: country
 *                   city: city
 *                   state: state
 *                   zipCode: '84848'
 *                   category: PTO
 *                   parentOrganizationId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5'
 *                   phoneNumber: '8974589651'
 *                   countryCode: '+91'
 *      updatePTODetails:
 *          type: object
 *          required:
 *              - orgID
 *              - orgName
 *              - address
 *              - country
 *              - state
 *              - city
 *              - zipCode
 *              - cashInstruction
 *              - checkInstruction
 *              - venmoInstruction
 *          properties:
 *              orgId:
 *                  type: string
 *                  description: uuid of the org
 *              orgName:
 *                  type: string
 *                  description: org name
 *              category:
 *                  type: string
 *                  description: category of org; one of ['PTO', 'School', 'Homeroom']
 *              parentOrganizationId:
 *                  type: string
 *                  description: uuid of the parent organization
 *              address:
 *                  type: string
 *                  description: address of the org
 *              country:
 *                  type: string
 *                  description: country of the org
 *              city:
 *                  type: string
 *                  description: city of the org
 *              state:
 *                  type: string
 *                  description: state of the org
 *              zipCode:
 *                  type: string
 *                  description: zipCode of org
 *              stripeAllowed:
 *                  type: boolean
 *                  description: stripe to be enabled for org
 *              platformFee:
 *                  type: number
 *                  description: platform fee of org for stripe payment
 *              platformFeeCoveredBy:
 *                  type: string
 *                  description: platform fee to be covered by either of organization, parent, optional
 *              venmoAllowed:
 *                  type: boolean
 *                  description: venmo to be enabled for org
 *              cashAllowed:
 *                  type: boolean
 *                  description: cash payment to be enabled for org
 *              checkAllowed:
 *                  type: boolean
 *                  description: check payment to be enabled for org
 *              cashInstruction:
 *                  type: string
 *                  description: cash payment instruction
 *              checkInstruction:
 *                  type: string
 *                  description: check payment instruction
 *              venmoInstruction:
 *                  type: string
 *                  description: venmo payment instruction
 *              venmoPaymentURL:
 *                  type: string
 *                  description: venmo payment URL for payment
 *          example:
 *              orgId: uuid
 *              orgName: Pto name
 *              category: PTO
 *              address: org address
 *              country: country
 *              state: state
 *              city: city
 *              parentOrganizationId: '8d40ddbe-996a-4183-a4ef-b32f66e045a5'
 *              zipCode: '84848'
 *              stripeAllowed: true
 *              venmoAllowed: true
 *              cashAllowed: true
 *              checkAllowed: true
 *              cashInstruction: cash payment instruction
 *              checkInstruction: check payment instruction
 *              venmoInstruction: venmo payment instruction
 *              venmoPaymentURL: venmo payment URL
 *      changePTOStatus:
 *          type: object
 *          required:
 *              - orgId
 *              - status
 *          properties:
 *              orgId:
 *                  type: string
 *                  description: uuid of the org
 *              status:
 *                  type: number
 *                  description: 0-disable 1-enable
 *          example:
 *                   orgId: 9e118adb-a4f1-48d1-b32a-7dc46a4d5991
 *                   status: 1
 *      successAddPtoAdmin:
 *          type: object
 *          properties:
 *              status:
 *                  type: number
 *                  description: status if data exists
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Organization added successfully
 *      successChangePTOStatus:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Organization status has been changed successfully
 *      successUpdatePTOStatus:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Organization updated successfully
 *      successDeletePTO:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Organization has been deleted successfully
 *      failDeletePTO:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: Organization can not be deleted
 *      successPTOList:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  $ref: '#/components/messageDefinition/properties/orgList'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *              data:
 *                  - id: uuid
 *                    name: Test Organization
 *                    category: PTO
 *                    address: address line 1
 *                    city: City
 *                    state: State
 *                    country: Country
 *                    zipCode: Zip code
 *                    isEnabled: 1
 *                    paymentDetails:
 *                        stripeOnboardingStatus: inactive
 *                        stripeConnectAccountId: acct_1OD2WiQq0byx7TTQ
 *      successPTODetails:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  $ref: '#/components/messageDefinition/properties/orgDetails'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *              data:
 *                    id: uuid
 *                    name: Test Organization
 *                    category: PTO
 *                    address: address line 1
 *                    city: City
 *                    state: State
 *                    country: Country
 *                    zipCode: Zip code
 *                    canBeDisabled: 1
 *                    canBeDeleted: 0
 *                    isEnabled: 1
 *                    paymentDetails:
 *                        stripeOnboardingStatus: inactive
 *                        stripeConnectAccountId: acct_1OD2WiQq0byx7TTQ
 *                    associatedOrgAdmin:
 *                        - id: uuid
 *                          email: <EMAIL>
 *                          firstName: John
 *                          lastName: Doe
 *                          phoneNumber: "**********"
 *                          countryCode: "+1"
 *      successPTOSearch:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  $ref: '#/components/messageDefinition/properties/orgDetails'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *              data:
 *                    page: currentPage,
 *                    totalItems: TotalItems,
 *                    totalPages: totalPage,
 *                    organization:
 *                          id: uuid
 *                          name: Test Organization
 *                          category: PTO
 *                          address: address line 1
 *                          city: City
 *                          state: State
 *                          country: Country
 *                          zipCode: Zip code
 *                          canBeDisabled: 1
 *                          canBeDeleted: 0
 *                          isEnabled: 1
 *                          paymentDetails:
 *                              stripeOnboardingStatus: inactive
 *                              stripeConnectAccountId: acct_1OD2WiQq0byx7TTQ
 *                          associatedOrgAdmin:
 *                              - id: uuid
 *                                email: <EMAIL>
 *                                firstName: John
 *                                lastName: Doe
 *                                phoneNumber: "**********"
 *                                countryCode: "+1"
 *
 *      userAlreadyExists:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 0
 *              message: A user with the email [GIVEN_USER_EMAIL] already exists
 *
 *      successGetPTOUsers:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  $ref: '#/components/messageDefinition/properties/orgUsersList'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *              data:
 *                  - id: uuid
 *                    email: <EMAIL>
 *                    firstName: John
 *                    lastName: Doe
 *                    role: org admin,
 *                    status: active
 *
 *      addPTOUser:
 *          type: object
 *          required:
 *              - orgId
 *              - firstName
 *              - lastName
 *              - email
 *              - role
 *              - password
 *          properties:
 *              orgId:
 *                  type: string
 *                  description: uuid of the org
 *              firstName:
 *                  type: string
 *                  description: first name of org user
 *              lastName:
 *                  type: string
 *                  description: last name of org user
 *              email:
 *                  type: string
 *                  description: email of org user
 *              role:
 *                  type: string
 *                  description: role of org user either of admin, super admin, editor
 *              password:
 *                  type: string
 *                  description: password of org user
 *          example:
 *              orgId: uuid
 *              firstName: John
 *              lastName: Doe
 *              email: <EMAIL>
 *              role: admin
 *              password: Test@123
 *
 *      successAddPTOUser:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: User added to organization successfully
 *
 *
 *      updatePTOUser:
 *          type: object
 *          required:
 *              - orgId
 *              - userId
 *              - role
 *              - status
 *          properties:
 *              orgId:
 *                  type: string
 *                  description: uuid of the org
 *              userId:
 *                  type: string
 *                  description: uuid of the user
 *              role:
 *                  type: string
 *                  description: role of org user either of admin, super admin, editor
 *              status:
 *                  type: string
 *                  description: status of org user either of active, inactive, deleted, suspended, freezed
 *          example:
 *              orgId: uuid
 *              userId: uuid
 *              role: admin
 *              status: active
 *              email: <EMAIL>
 *              password: Test@123
 *
 *      successUpdatePTOUser:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: User updated successfully
 *
 *      deletePTOUser:
 *          type: object
 *          required:
 *              - orgId
 *              - userId
 *          properties:
 *              orgId:
 *                  type: string
 *                  description: uuid of the org
 *              userId:
 *                  type: string
 *                  description: uuid of the user
 *          example:
 *              orgId: uuid
 *              userId: uuid
 *
 *      addMulipleChildToOrganization:
 *          type: object
 *          required:
 *              - orgId
 *              - addedChildIds
 *              - removedChildIds
 *          properties:
 *              orgId:
 *                  type: string
 *                  description: uuid of the org
 *              addedChildIds:
 *                  type: array
 *                  description: array of uuid of the children
 *              removedChildIds:
 *                  type: array
 *                  description: array of uuid of the children
 *          example:
 *              orgId: uuid
 *              addedChildIds: [uuid]
 *              removedChildIds: [uuid]
 *
 *      successDeletePTOUser:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: User deleted successfully
 *
 *      successAddRemoveChildren:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *
 *      successGetAssociatedOrganizations:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              data:
 *                  type: array
 *                  items:
 *                      type: object
 *                      properties:
 *                          id:
 *                              type: string
 *                              description: uuid of the organization
 *                          name:
 *                              type: string
 *                              description: name of the organization
 *                          country:
 *                              type: string
 *                              description: country of the organization
 *                          zipCode:
 *                              type: string
 *                              description: zip code of the organization
 *                          address:
 *                              type: string
 *                              description: address of the organization
 *                          city:
 *                              type: string
 *                              description: city of the organization
 *                          state:
 *                              type: string
 *                              description: state of the organization
 *                          category:
 *                              type: string
 *                          isEnabled:
 *                              type: boolean
 *                          paymentDetails:
 *                              type: object
 *                              properties:
 *                                  stripeOnboardingStatus:
 *                                      type: string
 *                                      description: stripe onboarding status of the organization
 *                                  stripeConnectAccountId:
 *                                      type: string
 *                                      description: stripe connect account id of the organization
 *                                  venmoPaymentURL:
 *                                      type: string
 *                                      description: venmo payment url of the organization
 *                          paymentInstructions:
 *                              type: object
 *                              properties:
 *                                  cashInstruction:
 *                                      type: string
 *                                      description: cash instruction of the organization
 *                                  chequeInstruction:
 *                                      type: string
 *                                      description: cheque instruction of the organization
 *                                  venmoInstruction:
 *                                      type: string
 *                                      description: venmo instruction of the organization
 *                          allowedPaymentType:
 *                              type: object
 *                              properties:
 *                                  cash:
 *                                      type: boolean
 *                                      description: cash allowed or not
 *                                  cheque:
 *                                      type: boolean
 *                                      description: cheque allowed or not
 *                                  venmo:
 *                                      type: boolean
 *                                      description: venmo allowed or not
 *                          platformFee:
 *                              type: number
 *                              description: platform fee of the organization
 *                          logo:
 *                              type: string
 *                              description: logo of the organization
 *                          organizationType:
 *                              type: string
 *                              description: organization type of the organization
 *                          platformFeeCoveredBy:
 *                              type: string
 *                              description: platform fee covered by of the organization
 *                          isDeleted:
 *                              type: boolean
 *                          minPlatformFeeAmount:
 *                              type: number
 *                              description: minimum platform fee amount of the organization
 *                          parentOrganization:
 *                              type: string
 *                              description: parent organization of the organization
 *                          parentOrganizationName:
 *                              type: string
 *                              description: parent organization name of the organization
 *                          associatedOrganizations:
 *                              type: array
 *                              items:
 *                                  type: object
 *                                  properties:
 *                                      id:
 *                                          type: string
 *                                          description: uuid of the associated organization
 *                                      name:
 *                                          type: string
 *                                          description: name of the associated organization
 *                                      category:
 *                                          type: string
 *                                          description: category of the associated organization
 *                                      isEnabled:
 *                                          type: boolean
 *                                          description: is enabled or not
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *          example:
 *              status: 1
 *              message: Success
 *              data:
 *                  - id: uuid
 *                    name: Test Organization
 *                    country: USA
 *                    zipCode: 12345
 *                    address: 123 Main St
 *                    city: New York
 *                    state: NY
 *                    category: PTO
 *                    isEnabled: true
 *                    paymentDetails:
 *                        stripeOnboardingStatus: inactive
 *                        stripeConnectAccountId: acct_1OD2WiQq0byx7TTQ
 *                        venmoPaymentURL: https://venmo.com/test
 *                    paymentInstructions:
 *                        cashInstruction: Cash instruction
 *                        chequeInstruction: Cheque instruction
 *                        venmoInstruction: Venmo instruction
 *                    allowedPaymentType:
 *                        cash: true
 *                        cheque: true
 *                        venmo: true
 *                    platformFee: 10
 *                    logo: https://example.com/logo.png
 *                    organizationType: PTO
 *                    platformFeeCoveredBy: organization
 *                    isDeleted: false
 *                    minPlatformFeeAmount: 100
 *                    parentOrganization: uuid
 *                    parentOrganizationName: Test Organization
 *                    associatedOrganizations:
 *                        - id: uuid
 *                          name: Test Organization
 *                          category: PTO
 *                          isEnabled: true
 */

/**
 * @openapi
 * components:
 *  schemas:
 *      associatedOrgDetails:
 *          type: object
 *          required:
 *              - name
 *              - address
 *              - country
 *              - city
 *              - state
 *              - zipCode
 *              - category
 *          properties:
 *              name:
 *                  type: string
 *                  description: name of the organization
 *              address:
 *                  type: string
 *                  description: address of the organization
 *              country:
 *                  type: string
 *                  description: country of the organization
 *              city:
 *                  type: string
 *                  description: city of the organization
 *              state:
 *                  type: string
 *                  description: state of the organization
 *              zipCode:
 *                  type: string
 *                  description: zipCode of the organization
 *              category:
 *                  type: string
 *                  description: category of org; one of ['PTO', 'School', 'Homeroom']
 *              paymentInstructions:
 *                  type: object
 *                  properties:
 *                      cashInstruction:
 *                          type: string
 *                          description: cash payment instruction
 *                      chequeInstruction:
 *                          type: string
 *                          description: cheque payment instruction
 *                      venmoInstruction:
 *                          type: string
 *                          description: venmo payment instruction
 *              allowedPaymentType:
 *                  type: object
 *                  properties:
 *                      cash:
 *                          type: boolean
 *                          description: cash payment allowed
 *                      cheque:
 *                          type: boolean
 *                          description: cheque payment allowed
 *                      venmo:
 *                          type: boolean
 *                          description: venmo payment allowed
 *                      stripe:
 *                          type: boolean
 *                          description: stripe payment allowed
 *              paymentDetails:
 *                  type: object
 *                  properties:
 *                      venmoPaymentURL:
 *                          type: string
 *                          description: venmo payment URL
 *              platformFee:
 *                  type: number
 *                  description: platform fee percentage
 *              platformFeeCoveredBy:
 *                  type: string
 *                  description: platform fee covered by either of organization, parent, optional
 *          example:
 *              name: "Sample PTO"
 *              address: "123 Main St"
 *              country: "USA"
 *              city: "New York"
 *              state: "NY"
 *              zipCode: "10001"
 *              category: "PTO"
 *              paymentInstructions:
 *                  cashInstruction: "Please bring cash to the main office"
 *                  chequeInstruction: "Make checks payable to Sample PTO"
 *                  venmoInstruction: "Send payments to @SamplePTO"
 *              allowedPaymentType:
 *                  cash: true
 *                  cheque: true
 *                  venmo: false
 *                  stripe: false
 *              paymentDetails:
 *                  venmoPaymentURL: ""
 *              platformFee: 3.5
 *              platformFeeCoveredBy: "organization"
 *
 *      addAssociatedOrganizations:
 *          type: object
 *          required:
 *              - parentOrgId
 *              - email
 *              - adminFirstName
 *              - adminLastName
 *              - associatedOrgsDetails
 *          properties:
 *              parentOrgId:
 *                  type: string
 *                  description: uuid of the parent organization
 *              email:
 *                  type: string
 *                  description: email of the organization admin
 *              adminFirstName:
 *                  type: string
 *                  description: first name of the organization admin
 *              adminLastName:
 *                  type: string
 *                  description: last name of the organization admin
 *              associatedOrgsDetails:
 *                  type: array
 *                  items:
 *                      $ref: '#/components/schemas/associatedOrgDetails'
 *                  description: array of organization details to be created and associated
 *          example:
 *              parentOrgId: "parent-org-id"
 *              email: "<EMAIL>"
 *              adminFirstName: "John"
 *              adminLastName: "Doe"
 *              associatedOrgsDetails:
 *                  - name: "Elementary PTO"
 *                    address: "123 School St"
 *                    country: "USA"
 *                    city: "Springfield"
 *                    state: "IL"
 *                    zipCode: "62701"
 *                    category: "PTO"
 *                    paymentInstructions:
 *                        cashInstruction: "Bring cash to main office"
 *                    allowedPaymentType:
 *                        cash: true
 *                        cheque: true
 *                        venmo: false
 *                        stripe: false
 *                  - name: "Middle School PTO"
 *                    address: "456 Education Ave"
 *                    country: "USA"
 *                    city: "Springfield"
 *                    state: "IL"
 *                    zipCode: "62702"
 *                    category: "PTO"
 *                    paymentInstructions:
 *                        cashInstruction: "Bring cash to main office"
 *                    allowedPaymentType:
 *                        cash: true
 *                        cheque: true
 *                        venmo: false
 *                        stripe: false
 *
 *      successAddAssociatedOrganizations:
 *          type: object
 *          properties:
 *              status:
 *                  $ref: '#/components/messageDefinition/properties/status'
 *              message:
 *                  $ref: '#/components/messageDefinition/properties/message'
 *              data:
 *                  type: array
 *                  items:
 *                      type: object
 *                      properties:
 *                          id:
 *                              type: string
 *                              description: uuid of the created organization
 *                          name:
 *                              type: string
 *                              description: name of the organization
 *                          category:
 *                              type: string
 *                              description: category of the organization
 *                          address:
 *                              type: string
 *                              description: address of the organization
 *                          city:
 *                              type: string
 *                              description: city of the organization
 *                          state:
 *                              type: string
 *                              description: state of the organization
 *                          country:
 *                              type: string
 *                              description: country of the organization
 *                          zipCode:
 *                              type: string
 *                              description: zipCode of the organization
 *                          email:
 *                              type: string
 *                              description: email of the organization
 *                          createdBy:
 *                              type: string
 *                              description: id of the user who created the organization
 *                          parentOrganization:
 *                              type: string
 *                              description: id of the parent organization
 *                          parentOrganizationName:
 *                              type: string
 *                              description: name of the parent organization
 *          example:
 *              status: 1
 *              message: "Associated organizations created successfully"
 *              data:
 *                  - id: "org-id"
 *                    name: "Elementary PTO"
 *                    category: "PTO"
 *                    address: "123 School St"
 *                    city: "Springfield"
 *                    state: "IL"
 *                    country: "USA"
 *                    zipCode: "62701"
 *                    email: "<EMAIL>"
 *                    createdBy: "admin-user-id"
 *                    parentOrganization: "parent-org-id"
 *                    parentOrganizationName: "Springfield School District"
 */

/**
 * @openapi
 * /organization:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization]
 *      summary: add organization and admin
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addPtoAdmin'
 *      responses:
 *          200:
 *              description: success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddPtoAdmin'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization/list:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization]
 *      summary: get list of organization
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successPTOList'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization]
 *      summary: get organization details
 *      parameters:
 *          - in: query
 *            name: orgId
 *            schema:
 *                type: string
 *            description: uuid of the organization
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successPTODetails'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization/status:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization]
 *      summary: change organization status to disable or enable
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/changePTOStatus'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successChangePTOStatus'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization:
 *  delete:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization]
 *      summary: delete organization
 *      parameters:
 *          - in: query
 *            name: orgId
 *            schema:
 *                type: string
 *            description: uuid of the organization
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successDeletePTO'
 *          400:
 *              description: Can not be deleted
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/failDeletePTO'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization]
 *      summary: update organization details
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/updatePTODetails'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successUpdatePTOStatus'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          422:
 *              description: User with given email already exists
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/userAlreadyExists'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization/users:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization User]
 *      summary: get organization users list
 *      parameters:
 *          - in: query
 *            name: orgId
 *            schema:
 *                type: string
 *            description: uuid of the organization
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGetPTOUsers'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization/user:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization User]
 *      summary: add organization user
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addPTOUser'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddPTOUser'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization/user:
 *  put:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization User]
 *      summary: update organization user
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/updatePTOUser'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successUpdatePTOUser'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization/user:
 *  delete:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization User]
 *      summary: delete organization user
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/deletePTOUser'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successDeletePTOUser'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization/search:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization]
 *      summary: search organization details
 *      parameters:
 *          - in: query
 *            searchValue: search string
 *            schema:
 *                type: string
 *            description: search organization with any specific string
 *          - in: query
 *            name: page
 *            schema:
 *                type: number
 *            description: page number
 *          - in: query
 *            name: limit
 *            schema:
 *                type: number
 *            description: per page limit
 *
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successPTOSearch'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */


/**
 * @openapi
 * /organization/add-children:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      tags: [Organization]
 *      summary: Add/remove multiple child from organization.
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addMulipleChildToOrganization'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddRemoveChildren'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization/associatedOrganizations:
 *  get:
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *          - in: header
 *            name: reqtoken
 *            schema:
 *              type: string
 *            description: Request token
 *            required: true
 *          - in: query
 *            name: orgId
 *            schema:
 *                type: string
 *            description: uuid of the organization
 *            required: true
 *      tags: [Organization]
 *      summary: get associated organizations
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successGetAssociatedOrganizations'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: internal server error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */

/**
 * @openapi
 * /organization/associatedOrganizations:
 *  post:
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *          - in: header
 *            name: reqtoken
 *            schema:
 *              type: string
 *            description: Request token
 *            required: true
 *      tags: [Organization]
 *      summary: Add multiple associated organizations to a parent organization
 *      description: Creates multiple organizations and associates them with a parent organization.
 *        This endpoint allows bulk creation of organizations under a parent organization.
 *      requestBody:
 *          required: true
 *          content:
 *              application/json:
 *                  schema:
 *                      $ref: '#/components/schemas/addAssociatedOrganizations'
 *      responses:
 *          200:
 *              description: Success
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/successAddAssociatedOrganizations'
 *          400:
 *              description: Validation failed
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/validationError'
 *          401:
 *              description: Unauthorised Access
 *              content:
 *                 application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unauthorisedAccessUser'
 *          500:
 *              description: Internal Server Error
 *              content:
 *                  application/json:
 *                      schema:
 *                          $ref: '#/components/schemas/unexpectedError'
 */
