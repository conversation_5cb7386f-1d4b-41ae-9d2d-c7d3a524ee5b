function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
const validation = require('../../util/validation');

/**
 * Class represents validations for notification.
 */
class NotificationValidator extends validation {
  constructor(body, locale, query) {
    if (stryMutAct_9fa48("1304")) {
      {}
    } else {
      stryCov_9fa48("1304");
      super(locale);
      this.body = body;
      this.query = query;
    }
  }

  /**
   * @desc This function is being used to validate request for add notification
   * <AUTHOR>
   * @since 31/01/2024
   */
  validateCreateNotification() {
    if (stryMutAct_9fa48("1305")) {
      {}
    } else {
      stryCov_9fa48("1305");
      const {
        title,
        description,
        associatedChildId
      } = this.body;
      super.field(title, stryMutAct_9fa48("1306") ? "" : (stryCov_9fa48("1306"), 'Title'));
      super.field(description, stryMutAct_9fa48("1307") ? "" : (stryCov_9fa48("1307"), 'Description'));
      super.field(associatedChildId, stryMutAct_9fa48("1308") ? "" : (stryCov_9fa48("1308"), 'Associated Child Id'));
    }
  }

  /**
   * @desc This function is being used to validate request for update notification read status
   * <AUTHOR>
   * @since 31/01/2024
   */
  validateUpdateNotification() {
    if (stryMutAct_9fa48("1309")) {
      {}
    } else {
      stryCov_9fa48("1309");
      const {
        notificationId
      } = this.query;
      super.field(notificationId, stryMutAct_9fa48("1310") ? "" : (stryCov_9fa48("1310"), 'Notification Id'));
    }
  }

  /**
   * @desc This function is being used to validate request for send notification to user or group of users
   * <AUTHOR>
   * @since 21/02/2024
   */
  validateSendNotification() {
    if (stryMutAct_9fa48("1311")) {
      {}
    } else {
      stryCov_9fa48("1311");
      const {
        description,
        children,
        allParticipants,
        allOrgChildren,
        eventId,
        organizationId
      } = this.body;
      super.field(organizationId, stryMutAct_9fa48("1312") ? "" : (stryCov_9fa48("1312"), 'Organization Id'));
      super.field(description, stryMutAct_9fa48("1313") ? "" : (stryCov_9fa48("1313"), 'Notification description'));
      if (stryMutAct_9fa48("1316") ? false : stryMutAct_9fa48("1315") ? true : stryMutAct_9fa48("1314") ? allOrgChildren : (stryCov_9fa48("1314", "1315", "1316"), !allOrgChildren)) {
        if (stryMutAct_9fa48("1317")) {
          {}
        } else {
          stryCov_9fa48("1317");
          if (stryMutAct_9fa48("1320") ? false : stryMutAct_9fa48("1319") ? true : stryMutAct_9fa48("1318") ? allParticipants : (stryCov_9fa48("1318", "1319", "1320"), !allParticipants)) {
            if (stryMutAct_9fa48("1321")) {
              {}
            } else {
              stryCov_9fa48("1321");
              super.arrayField(children, stryMutAct_9fa48("1322") ? "" : (stryCov_9fa48("1322"), 'Children array'));
            }
          } else {
            if (stryMutAct_9fa48("1323")) {
              {}
            } else {
              stryCov_9fa48("1323");
              super.field(eventId, stryMutAct_9fa48("1324") ? "" : (stryCov_9fa48("1324"), 'Event Id'));
            }
          }
        }
      }
    }
  }
}
module.exports = NotificationValidator;